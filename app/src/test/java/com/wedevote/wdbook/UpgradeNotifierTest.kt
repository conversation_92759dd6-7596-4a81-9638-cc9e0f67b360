package com.wedevote.wdbook

import com.wedevote.wdbook.base.AndroidBusinessPatch
import org.junit.Test
import org.junit.Assert.*

/**
 * 测试升级通知功能
 */
class UpgradeNotifierTest {

    @Test
    fun testAndroidUpgradeNotifierImplementsInterface() {
        val notifier = AndroidBusinessPatch()
        assertTrue("AndroidUpgradeNotifier should implement UpgradeNotifier", notifier is BusinessPatch)
    }

    @Test
    fun testUpgradeNotifierMethods() {
        val notifier = AndroidBusinessPatch()

        // 测试方法调用不会抛出异常（即使没有Activity也不应该崩溃）
        try {
            notifier.onAppNeedUpdateNotification(true, null)
            notifier.onAppNeedUpdateNotification(false, "https://example.com/upgrade")
        } catch (e: Exception) {
            fail("UpgradeNotifier methods should not throw exceptions: ${e.message}")
        }
    }

    @Test
    fun testUpgradeNotifierWithoutActivity() {
        val notifier = AndroidBusinessPatch()

        // 测试在没有Activity的情况下调用不会崩溃
        assertDoesNotThrow {
            notifier.onAppNeedUpdateNotification(true, null)
            notifier.onAppNeedUpdateNotification(false, "https://example.com/upgrade")
        }
    }

    private fun assertDoesNotThrow(block: () -> Unit) {
        try {
            block()
        } catch (e: Exception) {
            fail("Expected no exception, but got: ${e.message}")
        }
    }
}
