<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_root_view_bg"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/top_title_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/top_title_height"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="@string/account_and_security"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintTop_toBottomOf="@id/top_title_layout">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="?theme_bg_color_white_double_line"
                android:orientation="vertical">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.7dp"
                    android:background="?theme_bg_color_divide_line_color" />

                <com.aquila.lib.widget.group.GroupImageTextLayout
                    android:id="@+id/account_security_bind_email_layout"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:background="@drawable/selector_comm_click_bg"
                    android:foreground="?android:attr/selectableItemBackground"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    app:attr_image_src="@drawable/ic_arrow_right_gray"
                    app:attr_parent_orientation="IMAGE_RIGHT_TEXT_LEFT_SIDE_ALIGN"
                    app:attr_text="@string/email"
                    app:attr_text_color="?theme_text_color_black"
                    app:attr_text_size="16sp">

                    <TextView
                        android:id="@+id/account_security_bind_email_TextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_marginRight="20dp"
                        android:textColor="?theme_text_color_black"
                        />

                    <TextView
                        android:id="@+id/account_security_unbind_email_TextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_marginRight="20dp"
                        android:textColor="?theme_text_color_blue_006FFF"
                        android:visibility="gone"
                        android:text="未绑定"
                        />
                </com.aquila.lib.widget.group.GroupImageTextLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.7dp"
                    android:layout_marginLeft="16dp"
                    android:background="?theme_bg_color_divide_line_color" />

                <com.aquila.lib.widget.group.GroupImageTextLayout
                    android:id="@+id/account_security_bind_phone_layout"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:background="@drawable/selector_comm_click_bg"
                    android:foreground="?android:attr/selectableItemBackground"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    app:attr_image_src="@drawable/ic_arrow_right_gray"
                    app:attr_parent_orientation="IMAGE_RIGHT_TEXT_LEFT_SIDE_ALIGN"
                    app:attr_text="@string/phone_number"
                    app:attr_text_color="?theme_text_color_black"
                    app:attr_text_size="16sp">

                    <TextView
                        android:id="@+id/account_security_bind_phone_TextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_marginRight="20dp"
                        android:textColor="?theme_text_color_black"
                        />
                    <TextView
                        android:id="@+id/account_security_unbind_phone_TextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_marginRight="20dp"
                        android:visibility="gone"
                        android:textColor="?theme_text_color_blue_006FFF"
                        android:text="未绑定"
                        />
                </com.aquila.lib.widget.group.GroupImageTextLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.7dp"
                    android:background="?theme_bg_color_divide_line_color" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/account_security_device_manager_LinearLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="?theme_bg_color_white"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.7dp"
                    android:background="?theme_bg_color_divide_line_color" />

                <com.aquila.lib.widget.group.GroupImageTextLayout
                    android:id="@+id/account_security_device_manager_layout"
                    android:layout_width="match_parent"
                    android:layout_height="64dp"
                    android:background="@drawable/selector_comm_click_bg"
                    android:foreground="?android:attr/selectableItemBackground"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    app:attr_image_src="@drawable/ic_arrow_right_gray"
                    app:attr_parent_orientation="IMAGE_RIGHT_TEXT_LEFT_SIDE_ALIGN"
                    app:attr_text="@string/device_manager"
                    app:attr_text_color="?theme_text_color_black"
                    app:attr_text_size="16sp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.7dp"
                    android:background="?theme_bg_color_divide_line_color" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="?theme_bg_color_white"
                android:orientation="vertical">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.7dp"
                    android:background="?theme_bg_color_divide_line_color" />

                <com.aquila.lib.widget.group.GroupImageTextLayout
                    android:id="@+id/account_security_change_password_layout"
                    android:layout_width="match_parent"
                    android:layout_height="64dp"
                    android:background="@drawable/selector_comm_click_bg"
                    android:foreground="?android:attr/selectableItemBackground"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    app:attr_image_src="@drawable/ic_arrow_right_gray"
                    app:attr_parent_orientation="IMAGE_RIGHT_TEXT_LEFT_SIDE_ALIGN"
                    app:attr_text="@string/label_change_password"
                    app:attr_text_color="?theme_text_color_black"
                    app:attr_text_size="16sp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.7dp"
                    android:layout_marginLeft="16dp"
                    android:background="?theme_bg_color_divide_line_color" />

                <RelativeLayout
                    android:id="@+id/setting_delete_account_Layout"
                    android:layout_width="match_parent"
                    android:layout_height="64dp"
                    android:background="?theme_comm_click_bg"
                    android:paddingLeft="15dp"
                    android:paddingTop="10dp"
                    android:paddingRight="15dp">

                    <TextView
                        android:id="@+id/setting_delete_label_TextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/delete_account"
                        android:textColor="?theme_text_color_black"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/setting_delete_descTextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@id/setting_delete_label_TextView"
                        android:text="@string/delete_account_sub_title"
                        android:textColor="?theme_text_color_gray"
                        android:textSize="14sp" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:src="@drawable/ic_arrow_right_gray" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.7dp"
                    android:background="?theme_bg_color_divide_line_color" />
            </LinearLayout>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>