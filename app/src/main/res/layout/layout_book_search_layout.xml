<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="20dp"
        android:layout_marginTop="2dp"
        android:background="?theme_bg_color_white_4E"
        app:cardElevation="4dp"
        app:cardCornerRadius="8dp"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/book_search_top_layout"
            android:layout_width="match_parent"
            android:layout_height="47dp"
            android:background="?theme_bg_color_white_4E"
            android:gravity="center_vertical"
            android:orientation="horizontal">


            <LinearLayout
                android:id="@+id/book_search_text_top_layout"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:layout_marginStart="40dp"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/book_search_text_top_TextView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:maxLines="1"
                    android:ellipsize="middle"
                    android:textColor="?theme_text_color_black_373636"
                    android:textSize="12sp"
                    tools:text="本书包含“耶稣”的搜索结果本书包含“耶稣”的搜索结果本书包含“耶稣”的搜索结果" />

                <TextView
                    android:id="@+id/book_search_text_chapter_TextView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:textColor="?theme_text_color_black_4e"
                    android:textSize="10sp"
                    tools:text="前言" />
            </LinearLayout>


            <ImageView
                android:id="@+id/book_search_close_ImageView"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?theme_comm_click_bg"
                android:foreground="?selectableItemBackground"
                android:scaleType="center"
                android:src="@drawable/ic_clear_input_gray"
                app:layout_constraintBottom_toBottomOf="@id/search_input_EditText"
                app:layout_constraintRight_toRightOf="@id/search_input_EditText"
                app:layout_constraintTop_toTopOf="@id/search_input_EditText" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="20dp"
        android:layout_marginBottom="3dp"
        android:background="?theme_bg_color_white_4E"
        app:cardElevation="1dp"
        app:cardCornerRadius="8dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:id="@+id/book_search_bottom_layout"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:background="?theme_bg_color_white_4E"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/book_search_prev_layout"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingStart="8dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_in_book_left_arrow"
                    app:tint="?theme_text_color_black_373636"
                    tools:ignore="UseAppTint" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/previous_one"
                    android:textColor="?theme_text_color_black_373636"
                    android:textSize="12sp" />
            </LinearLayout>

            <View
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <LinearLayout
                android:id="@+id/book_search_text_result_layout"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_in_book_search"
                    app:tint="?theme_icon_tint_color_black"
                    tools:ignore="UseAppTint" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/search_result"
                    android:layout_marginStart="4dp"
                    android:textColor="?theme_text_color_black_373636"
                    android:textSize="14sp" />
            </LinearLayout>

            <View
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <LinearLayout
                android:id="@+id/book_search_next_layout"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingEnd="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/next_one"
                    android:textColor="?theme_text_color_black_373636"
                    android:textSize="12sp" />

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_in_book_right_arrow"
                    app:tint="?theme_text_color_black_373636"
                    tools:ignore="UseAppTint" />

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout> 