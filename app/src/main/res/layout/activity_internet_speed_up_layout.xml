<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_root_view_bg"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/speed_up_title_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_height"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="@string/internet_speed_up"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/speed_up_empty_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingTop="200dp"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            android:id="@+id/speed_up_empty_TextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="30dp"
            android:gravity="center"
            android:text="Please scan the given QR code to improve the stability of your network connection."
            android:textColor="?theme_text_color_black"
            android:textSize="16sp" />
    </LinearLayout>

    <com.aquila.lib.widget.view.CustomRecyclerView
        android:id="@+id/speed_up_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="15dp"
        android:paddingRight="15dp" />
</LinearLayout>