<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_root_view_bg"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/login_indicator_layout"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="?theme_bg_color_white_bottom_line">

        <ImageView
            android:id="@+id/register_back_View"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:padding="8dp"
            android:background="@drawable/selector_item_click_circle_bg"
            android:foreground="?android:attr/selectableItemBackground"
            app:tint="?theme_icon_tint_color"
            tools:ignore="UseAppTint"
            android:src="@drawable/ic_in_book_left_arrow"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/register_login_title_TextView"
            style="@style/style_title_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true" />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:paddingTop="10dp">

        <FrameLayout
            android:id="@+id/register_content_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"></FrameLayout>


        <TextView
            android:id="@+id/register_next_TextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="14dp"
            android:enabled="false"
            android:background="@drawable/selector_all_conner_brown_bg"
            android:gravity="center_horizontal"
            android:padding="13dp"
            android:text="Next"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <LinearLayout
            android:id="@+id/security_verify_method_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:layout_marginBottom="16dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:text="@string/get_verification_code_failed_change_other_way"
                android:textColor="?theme_text_color_gray"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/security_verify_method_TextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                tools:text="@string/mobile_verify"
                android:textColor="@color/color_blue_006FFF"
                android:textSize="16sp" />

        </LinearLayout>


    </LinearLayout>
</LinearLayout>