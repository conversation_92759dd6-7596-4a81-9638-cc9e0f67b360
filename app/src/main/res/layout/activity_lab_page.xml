<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_bg_color_white"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/lab_page_top_title_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/top_title_height"
        app:attr_title_text="Lab Page"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/lab_page_top_title_layout">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="?theme_bg_color_white_bottom_line"
            android:orientation="vertical">

            <View
                android:layout_width="match_parent"
                android:layout_height="0.7dp"
                android:background="?theme_bg_color_divide_line_color"
                app:layout_constraintBottom_toTopOf="@+id/lab_page_use_test_cft_config_TextView"
                app:layout_constraintTop_toTopOf="@+id/lab_page_use_test_cft_config_TextView"
                tools:layout_editor_absoluteX="16dp" />

            <TextView
                android:id="@+id/lab_page_use_test_cft_config_TextView"
                android:layout_width="0dp"
                android:layout_height="64dp"
                android:layout_marginStart="15dp"
                android:gravity="center_vertical"
                android:text="CFT 切换测试公网地址的参数"
                android:textColor="?theme_text_color_black"
                android:textSize="16sp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@id/lab_page_use_test_cft_config_Switch"
                app:layout_constraintTop_toTopOf="parent" />

            <com.wedevote.wdbook.ui.widgets.SlideSwitch
                android:id="@+id/lab_page_use_test_cft_config_Switch"
                android:layout_width="46dp"
                android:layout_height="28dp"
                android:layout_marginEnd="16dp"
                app:attr_is_check="false"
                app:attr_shape_type="CIRCLE"
                app:attr_theme_color="@color/color_E9973E"
                app:layout_constraintBottom_toBottomOf="@id/lab_page_use_test_cft_config_TextView"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@id/lab_page_use_test_cft_config_TextView" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.7dp"
                android:layout_marginStart="16dp"
                android:background="?theme_bg_color_divide_line_color"
                app:layout_constraintBottom_toBottomOf="@+id/lab_page_use_test_cft_config_TextView"
                app:layout_constraintTop_toBottomOf="@+id/lab_page_use_test_cft_config_TextView"
                tools:layout_editor_absoluteX="16dp" />

            <TextView
                android:id="@+id/lab_page_show_cft_log_TextView"
                android:layout_width="0dp"
                android:layout_height="64dp"
                android:gravity="center_vertical"
                android:text="CFT 日志打印"
                android:textColor="?theme_text_color_black"
                android:textSize="16sp"
                app:layout_constraintLeft_toLeftOf="@id/lab_page_use_test_cft_config_TextView"
                app:layout_constraintRight_toLeftOf="@id/lab_page_show_cft_log_Switch"
                app:layout_constraintTop_toBottomOf="@id/lab_page_use_test_cft_config_TextView" />

            <com.wedevote.wdbook.ui.widgets.SlideSwitch
                android:id="@+id/lab_page_show_cft_log_Switch"
                android:layout_width="46dp"
                android:layout_height="28dp"
                android:layout_marginEnd="16dp"
                app:attr_is_check="false"
                app:attr_shape_type="CIRCLE"
                app:attr_theme_color="@color/color_E9973E"
                app:layout_constraintBottom_toBottomOf="@id/lab_page_show_cft_log_TextView"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@id/lab_page_show_cft_log_TextView" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.7dp"
                android:background="?theme_bg_color_divide_line_color"
                app:layout_constraintBottom_toBottomOf="@+id/lab_page_show_cft_log_TextView"
                app:layout_constraintTop_toBottomOf="@+id/lab_page_show_cft_log_TextView"
                tools:layout_editor_absoluteX="16dp" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>