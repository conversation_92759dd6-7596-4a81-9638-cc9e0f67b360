<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RelativeLayout
        android:id="@+id/dialog_note_guide_main"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/dialog_note_guide_bottom"
        android:background="@color/half_transparent">
        <ImageView
            android:id="@+id/dialog_note_guide_imageView"
            android:layout_alignParentBottom="true"
            android:layout_height="wrap_content"
            android:layout_width="match_parent"
            android:src="@drawable/guide_recycle"/>
    </RelativeLayout>
    <LinearLayout
        android:id="@+id/dialog_note_guide_bottom"
        android:layout_height="50dp"
        android:layout_width="match_parent"
        android:orientation="horizontal"
        android:layout_alignParentBottom="true">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/half_transparent"
            android:id="@+id/dialog_note_position_left"
            android:layout_weight="1"/>
        <TextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/half_transparent"
            android:id="@+id/dialog_note_position_middle"
            android:layout_weight="1"/>
        <TextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/half_transparent"
            android:id="@+id/dialog_note_position_right"
            android:layout_weight="1"/>
    </LinearLayout>
</RelativeLayout>