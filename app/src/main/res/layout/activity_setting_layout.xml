<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_root_view_bg"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/setting_top_title_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/top_title_height"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="@string/home_mine_setting"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/setting_logout_Button"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="?theme_bg_color_white_top_line"
        android:foreground="?android:attr/selectableItemBackground"
        android:text="@string/logout"
        android:textColor="@color/color_red_FF6D55"
        app:layout_constraintBottom_toBottomOf="parent" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toTopOf="@id/setting_logout_Button"
        app:layout_constraintTop_toBottomOf="@id/setting_top_title_layout">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?theme_bg_color_white"
                android:paddingBottom="20dp"
                android:paddingHorizontal="24dp"
                app:layout_constraintBottom_toBottomOf="@+id/setting_theme_label_TextView"
                app:layout_constraintTop_toBottomOf="@+id/setting_theme_label_TextView">

                <TextView
                    android:id="@+id/setting_theme_label_TextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:minHeight="64dp"
                    android:text="@string/label_subject"
                    android:textColor="?theme_text_color_black"
                    android:textSize="16sp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    android:id="@+id/setting_theme_fit_sysytem_container_layout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:visibility="visible"
                    app:layout_constraintHorizontal_chainStyle="spread_inside"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toLeftOf="@id/setting_theme_light_container_layout"
                    app:layout_constraintTop_toBottomOf="@id/setting_theme_label_TextView">

                    <TextView
                        android:id="@+id/setting_theme_fit_system_TextView"
                        android:layout_width="90dp"
                        android:layout_height="50dp"
                        android:background="@drawable/shape_theme_bg_stoke_light"
                        android:gravity="center"
                        android:text="@string/theme_follow_system"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <ImageView
                        android:id="@+id/setting_theme_fit_system_check_ImageView"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_marginTop="16dp"
                        android:src="@drawable/selected_favorite_check_box_yellow"
                        app:layout_constraintLeft_toLeftOf="@id/setting_theme_light_TextView"
                        app:layout_constraintRight_toRightOf="@id/setting_theme_light_TextView"
                        app:layout_constraintTop_toBottomOf="@id/setting_theme_light_TextView" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/setting_theme_light_container_layout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical"
                    app:layout_constraintLeft_toRightOf="@id/setting_theme_fit_sysytem_container_layout"
                    app:layout_constraintRight_toLeftOf="@id/setting_theme_night_container_layout"
                    app:layout_constraintTop_toBottomOf="@id/setting_theme_label_TextView">

                    <TextView
                        android:id="@+id/setting_theme_light_TextView"
                        android:layout_width="90dp"
                        android:layout_height="50dp"
                        android:background="@drawable/shape_theme_bg_stoke_light"
                        android:gravity="center"
                        android:text="@string/theme_light"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <ImageView
                        android:id="@+id/setting_theme_light_check_ImageView"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_marginTop="16dp"
                        android:src="@drawable/selected_favorite_check_box_yellow"
                        app:layout_constraintLeft_toLeftOf="@id/setting_theme_light_TextView"
                        app:layout_constraintRight_toRightOf="@id/setting_theme_light_TextView"
                        app:layout_constraintTop_toBottomOf="@id/setting_theme_light_TextView" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/setting_theme_night_container_layout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="@id/setting_theme_light_container_layout"
                    app:layout_constraintLeft_toRightOf="@id/setting_theme_light_container_layout"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="@id/setting_theme_light_container_layout">

                    <TextView
                        android:id="@+id/setting_theme_night_TextView"
                        android:layout_width="90dp"
                        android:layout_height="50dp"
                        android:background="@drawable/shape_theme_bg_stoke_night"
                        android:gravity="center"
                        android:text="@string/theme_dark"
                        android:textColor="@color/white"
                        android:textSize="14sp" />

                    <ImageView
                        android:id="@+id/setting_theme_night_check_ImageView"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_marginTop="16dp"
                        android:src="@drawable/selected_favorite_check_box_yellow" />
                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="?theme_bg_color_white">

                <com.aquila.lib.widget.group.GroupImageTextLayout
                    android:id="@+id/setting_language_setting_Layout"
                    android:layout_width="match_parent"
                    android:layout_height="64dp"
                    android:background="@drawable/selector_comm_click_bg"
                    android:foreground="?android:attr/selectableItemBackground"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    app:attr_image_src="@drawable/ic_arrow_right_gray"
                    app:attr_parent_orientation="IMAGE_RIGHT_TEXT_LEFT_SIDE_ALIGN"
                    app:attr_text="@string/multiple_language"
                    app:attr_text_color="?theme_text_color_black"
                    app:attr_text_size="16sp">

                    <TextView
                        android:id="@+id/setting_select_language_TexView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_marginTop="1.5dp"
                        android:layout_marginRight="5dp"
                        android:gravity="center"
                        android:paddingLeft="10dp"
                        android:paddingRight="10dp"
                        android:text="简体中文"
                        android:textColor="?theme_text_color_gray"
                        android:textSize="12sp" />

                </com.aquila.lib.widget.group.GroupImageTextLayout>
            </FrameLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="?theme_bg_color_white_bottom_line">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="0.7dp"
                    android:background="?theme_bg_color_divide_line_color"
                    app:layout_constraintBottom_toTopOf="@+id/setting_show_page_TextView"
                    app:layout_constraintTop_toTopOf="@+id/setting_show_page_TextView"
                    tools:layout_editor_absoluteX="16dp" />

                <TextView
                    android:id="@+id/setting_show_page_TextView"
                    android:layout_width="0dp"
                    android:layout_height="64dp"
                    android:layout_marginLeft="15dp"
                    android:gravity="center_vertical"
                    android:text="@string/show_page_number"
                    android:textColor="?theme_text_color_black"
                    android:textSize="16sp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toLeftOf="@id/setting_show_page_Switch"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.wedevote.wdbook.ui.widgets.SlideSwitch
                    android:id="@+id/setting_show_page_Switch"
                    android:layout_width="45dp"
                    android:layout_height="24dp"
                    android:layout_marginRight="15dp"
                    app:attr_is_check="false"
                    app:attr_shape_type="CIRCLE"
                    app:attr_theme_color="@color/color_E9973E"
                    app:layout_constraintBottom_toBottomOf="@id/setting_show_page_TextView"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="@id/setting_show_page_TextView" />

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="0.7dp"
                    android:layout_marginLeft="16dp"
                    android:background="?theme_bg_color_divide_line_color"
                    app:layout_constraintBottom_toBottomOf="@+id/setting_show_page_TextView"
                    app:layout_constraintTop_toBottomOf="@+id/setting_show_page_TextView"
                    tools:layout_editor_absoluteX="16dp" />

                <TextView
                    android:id="@+id/setting_label_not_lock_screen_TextView"
                    android:layout_width="0dp"
                    android:layout_height="64dp"
                    android:gravity="center_vertical"
                    android:text="@string/reading_not_lock_screen"
                    android:textColor="?theme_text_color_black"
                    android:textSize="16sp"
                    app:layout_constraintLeft_toLeftOf="@id/setting_show_page_TextView"
                    app:layout_constraintRight_toLeftOf="@id/setting_not_lock_screen_Switch"
                    app:layout_constraintTop_toBottomOf="@id/setting_show_page_TextView" />

                <com.wedevote.wdbook.ui.widgets.SlideSwitch
                    android:id="@+id/setting_not_lock_screen_Switch"
                    android:layout_width="45dp"
                    android:layout_height="24dp"
                    android:layout_marginRight="15dp"
                    app:attr_is_check="false"
                    app:attr_shape_type="CIRCLE"
                    app:attr_theme_color="@color/color_E9973E"
                    app:layout_constraintBottom_toBottomOf="@id/setting_label_not_lock_screen_TextView"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="@id/setting_label_not_lock_screen_TextView" />

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="0.7dp"
                    android:background="?theme_bg_color_divide_line_color"
                    app:layout_constraintBottom_toBottomOf="@+id/setting_label_not_lock_screen_TextView"
                    app:layout_constraintTop_toBottomOf="@+id/setting_label_not_lock_screen_TextView"
                    tools:layout_editor_absoluteX="16dp" />

                <TextView
                    android:id="@+id/setting_label_wifi_download_TextView"
                    android:layout_width="0dp"
                    android:layout_height="64dp"
                    android:gravity="center_vertical"
                    android:text="@string/download_only_in_wifi"
                    android:textColor="?theme_text_color_black"
                    android:textSize="16sp"
                    android:visibility="gone"
                    app:layout_constraintLeft_toLeftOf="@id/setting_label_not_lock_screen_TextView"
                    app:layout_constraintRight_toLeftOf="@id/setting_wifi_download_Switch"
                    app:layout_constraintTop_toBottomOf="@id/setting_label_not_lock_screen_TextView" />

                <com.wedevote.wdbook.ui.widgets.SlideSwitch
                    android:id="@+id/setting_wifi_download_Switch"
                    android:layout_width="45dp"
                    android:layout_height="24dp"
                    android:visibility="gone"
                    app:attr_is_check="true"
                    app:attr_shape_type="CIRCLE"
                    app:attr_theme_color="@color/color_E9973E"
                    app:layout_constraintBottom_toBottomOf="@id/setting_label_wifi_download_TextView"
                    app:layout_constraintRight_toRightOf="@id/setting_not_lock_screen_Switch"
                    app:layout_constraintTop_toTopOf="@id/setting_label_wifi_download_TextView" />

                <TextView
                    android:id="@+id/setting_label_change_theme_TextView"
                    android:layout_width="0dp"
                    android:layout_height="64dp"
                    android:gravity="center_vertical"
                    android:text="@string/dark_theme_fellow_system"
                    android:textColor="@color/text_color_black_33"
                    android:textSize="16sp"
                    android:visibility="gone"
                    app:layout_constraintLeft_toLeftOf="@id/setting_label_not_lock_screen_TextView"
                    app:layout_constraintRight_toLeftOf="@id/setting_wifi_download_Switch"
                    app:layout_constraintTop_toBottomOf="@id/setting_label_wifi_download_TextView" />

                <com.wedevote.wdbook.ui.widgets.SlideSwitch
                    android:id="@+id/setting_change_theme_Switch"
                    android:layout_width="45dp"
                    android:layout_height="24dp"
                    android:visibility="gone"
                    app:attr_is_check="true"
                    app:attr_shape_type="CIRCLE"
                    app:attr_theme_color="@color/color_E9973E"
                    app:layout_constraintBottom_toBottomOf="@id/setting_label_change_theme_TextView"
                    app:layout_constraintRight_toRightOf="@id/setting_not_lock_screen_Switch"
                    app:layout_constraintTop_toTopOf="@id/setting_label_change_theme_TextView" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="?theme_bg_color_white_double_line"
                android:orientation="vertical">

                <com.aquila.lib.widget.group.GroupImageTextLayout
                    android:id="@+id/setting_notification_setting_layout"
                    android:layout_width="match_parent"
                    android:layout_height="64dp"
                    android:background="@drawable/selector_comm_click_bg"
                    android:foreground="?android:attr/selectableItemBackground"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    android:visibility="gone"
                    app:attr_image_src="@drawable/ic_arrow_right_gray"
                    app:attr_parent_orientation="IMAGE_RIGHT_TEXT_LEFT_SIDE_ALIGN"
                    app:attr_text="@string/notification_setting"
                    app:attr_text_color="?theme_text_color_black"
                    app:attr_text_size="16sp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.7dp"
                    android:layout_marginLeft="16dp"
                    android:background="?theme_bg_color_divide_line_color"
                    android:visibility="gone" />

                <com.aquila.lib.widget.group.GroupImageTextLayout
                    android:id="@+id/setting_device_manager_layout"
                    android:layout_width="match_parent"
                    android:layout_height="64dp"
                    android:background="@drawable/selector_comm_click_bg"
                    android:foreground="?android:attr/selectableItemBackground"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    android:visibility="gone"
                    app:attr_image_src="@drawable/ic_arrow_right_gray"
                    app:attr_parent_orientation="IMAGE_RIGHT_TEXT_LEFT_SIDE_ALIGN"
                    app:attr_text="@string/device_manager"
                    app:attr_text_color="?theme_text_color_black"
                    app:attr_text_size="16sp"
                    tools:visibility="visible" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.7dp"
                    android:layout_marginLeft="16dp"
                    android:background="?theme_bg_color_divide_line_color" />

                <com.aquila.lib.widget.group.GroupImageTextLayout
                    android:id="@+id/setting_about_layout"
                    android:layout_width="match_parent"
                    android:layout_height="64dp"
                    android:background="@drawable/selector_comm_click_bg"
                    android:foreground="?android:attr/selectableItemBackground"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    app:attr_image_src="@drawable/ic_arrow_right_gray"
                    app:attr_parent_orientation="IMAGE_RIGHT_TEXT_LEFT_SIDE_ALIGN"
                    app:attr_text="@string/title_about_us"
                    app:attr_text_color="?theme_text_color_black"
                    app:attr_text_size="16sp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.7dp"
                    android:layout_marginLeft="16dp"
                    android:background="?theme_bg_color_divide_line_color" />

                <com.aquila.lib.widget.group.GroupImageTextLayout
                    android:id="@+id/setting_faith_announcement_Layout"
                    android:layout_width="match_parent"
                    android:layout_height="64dp"
                    android:background="@drawable/selector_comm_click_bg"
                    android:foreground="?android:attr/selectableItemBackground"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    android:visibility="gone"
                    app:attr_image_src="@drawable/ic_arrow_right_gray"
                    app:attr_parent_orientation="IMAGE_RIGHT_TEXT_LEFT_SIDE_ALIGN"
                    app:attr_text="@string/faith_confession"
                    app:attr_text_color="@color/text_color_black_33"
                    app:attr_text_size="16sp" />

                <com.aquila.lib.widget.group.GroupImageTextLayout
                    android:id="@+id/setting_clear_cache_Layout"
                    android:layout_width="match_parent"
                    android:layout_height="64dp"
                    android:background="@drawable/selector_comm_click_bg"
                    android:foreground="?android:attr/selectableItemBackground"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    android:visibility="gone"
                    app:attr_image_src="@drawable/ic_arrow_right_gray"
                    app:attr_parent_orientation="IMAGE_RIGHT_TEXT_LEFT_SIDE_ALIGN"
                    app:attr_text="@string/clear_cache"
                    app:attr_text_color="@color/text_color_black_33"
                    app:attr_text_size="16sp" />


                <com.aquila.lib.widget.group.GroupImageTextLayout
                    android:id="@+id/setting_check_upgrade_layout"
                    android:layout_width="match_parent"
                    android:layout_height="64dp"
                    android:background="@drawable/selector_comm_click_bg"
                    android:foreground="?android:attr/selectableItemBackground"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    app:attr_image_src="@drawable/ic_arrow_right_gray"
                    app:attr_parent_orientation="IMAGE_RIGHT_TEXT_LEFT_SIDE_ALIGN"
                    app:attr_text="@string/check_new_version"
                    app:attr_text_color="?theme_text_color_black"
                    app:attr_text_size="16sp">

                    <TextView
                        android:id="@+id/setting_app_version_TextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_marginRight="20dp"
                        android:text="V1.0"
                        android:textColor="?theme_text_color_black" />
                </com.aquila.lib.widget.group.GroupImageTextLayout>
            </LinearLayout>

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="0.7dp"
                android:background="?theme_bg_color_divide_line_color"
                app:layout_constraintBottom_toBottomOf="@+id/setting_label_not_lock_screen_TextView"
                app:layout_constraintTop_toBottomOf="@+id/setting_label_not_lock_screen_TextView"
                tools:layout_editor_absoluteX="16dp" />

            <LinearLayout
                android:id="@+id/setting_internet_speed_up_Layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="?theme_bg_color_white"
                android:orientation="vertical">

                <com.aquila.lib.widget.group.GroupImageTextLayout
                    android:layout_width="match_parent"
                    android:layout_height="64dp"
                    android:background="@drawable/selector_comm_click_bg"
                    android:foreground="?android:attr/selectableItemBackground"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    app:attr_image_src="@drawable/ic_arrow_right_gray"
                    app:attr_parent_orientation="IMAGE_RIGHT_TEXT_LEFT_SIDE_ALIGN"
                    app:attr_text="@string/internet_speed_up"
                    app:attr_text_color="?theme_text_color_black"
                    app:attr_text_size="16sp"></com.aquila.lib.widget.group.GroupImageTextLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/setting_account_security_Layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="10dp"
                android:background="?theme_bg_color_white"
                android:orientation="vertical">

                <com.aquila.lib.widget.group.GroupImageTextLayout
                    android:layout_width="match_parent"
                    android:layout_height="64dp"
                    android:background="@drawable/selector_comm_click_bg"
                    android:foreground="?android:attr/selectableItemBackground"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    app:attr_image_src="@drawable/ic_arrow_right_gray"
                    app:attr_parent_orientation="IMAGE_RIGHT_TEXT_LEFT_SIDE_ALIGN"
                    app:attr_text="@string/account_and_security"
                    app:attr_text_color="?theme_text_color_black"
                    app:attr_text_size="16sp">

                </com.aquila.lib.widget.group.GroupImageTextLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:background="?theme_bg_color_white"
                android:orientation="vertical"
                android:visibility="gone">

                <RelativeLayout
                    android:id="@+id/setting_delete_account_Layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?theme_comm_click_bg"
                    android:paddingLeft="15dp"
                    android:paddingTop="10dp"
                    android:paddingRight="15dp"
                    android:paddingBottom="10dp">

                    <TextView
                        android:id="@+id/setting_delete_label_TextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/delete_account"
                        android:textColor="?theme_text_color_black"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/setting_delete_descTextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@id/setting_delete_label_TextView"
                        android:layout_marginTop="5dp"
                        android:text="@string/delete_account_sub_title"
                        android:textColor="?theme_text_color_gray"
                        android:textSize="14sp" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:src="@drawable/ic_arrow_right_gray" />

                </RelativeLayout>

            </LinearLayout>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>