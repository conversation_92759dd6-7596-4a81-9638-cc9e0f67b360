<?xml version="1.0" encoding="utf-8"?>

<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/format_book_mark_Flag_ImageView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_gravity="right"
        android:layout_marginRight="5dp"
        android:elevation="10dp"
        android:src="@drawable/ic_bookmark_long_red" />

    <com.aquila.lib.layout.SmartRefreshLayout
        android:id="@+id/format_root_RefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?theme_bg_color_white"
        app:srlEnableLoadMore="false">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/format_text_title_TextView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:maxLines="1"
                android:textDirection="ltr"
                android:ellipsize="end"
                android:gravity="center"
                android:paddingTop="48dp"
                android:paddingBottom="12dp"
                android:paddingLeft="32dp"
                android:paddingRight="32dp"
                tools:text="标题"
                android:textColor="@color/text_color_black_000"
                android:textSize="13sp"
                app:layout_constraintTop_toTopOf="parent" />


            <com.wedevote.wdbook.ui.read.lib.view.FormatContentView
                android:id="@+id/format_text_content_FormatView"
                android:layout_width="match_parent"
                android:layout_marginTop="10dp"
                android:layout_height="0dp"
                android:layout_below="@id/format_text_title_TextView"
                android:orientation="vertical"
                android:paddingLeft="25dp"
                app:layout_constraintBottom_toTopOf="@id/format_text_page_number_TextView"
                app:layout_constraintTop_toBottomOf="@id/format_text_title_TextView" />

            <TextView
                android:id="@+id/format_text_note_count_TextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/format_text_content_FormatView"
                android:layout_marginLeft="25dp"
                android:layout_marginBottom="15dp"
                android:background="@color/color_E9973E"
                android:drawableRight="@drawable/ic_triangle_right_white"
                android:drawablePadding="5dp"
                android:paddingLeft="10dp"
                android:paddingTop="2dp"
                android:paddingRight="10dp"
                android:paddingBottom="2dp"
                android:text="笔记 22"
                android:textColor="@color/white"
                android:textSize="8sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent" />

            <TextView
                android:id="@+id/format_text_page_number_TextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/format_text_content_FormatView"
                android:layout_alignParentRight="true"
                android:layout_marginBottom="15dp"
                android:gravity="center"
                android:minHeight="20dp"
                android:paddingLeft="25dp"
                android:paddingRight="25dp"
                android:text="1/100"
                android:textColor="@color/text_color_black_000"
                android:textSize="12sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent" />

            <com.wedevote.wdbook.ui.read.lib.TextSelectOperateLayout
                android:id="@+id/format_text_selected_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="invisible"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.aquila.lib.layout.SmartRefreshLayout>

</FrameLayout>