<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/book_page_title_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:gravity="center_vertical"
        android:minHeight="40dp"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:textColor="@color/text_color_black_000"
        android:textSize="12sp"
        tools:text="这个是标题这个是标题" />

    <com.wedevote.wdbook.ui.read.lib.view.FormatContentView
        android:id="@+id/book_page_content_FormatView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/book_page_number_info_TextView"
        android:layout_below="@id/book_page_title_TextView"
        android:layout_marginBottom="5dp"
        android:orientation="vertical"
        android:paddingLeft="25dp" />

    <TextView
        android:id="@+id/book_page_number_info_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:gravity="center"

        android:minHeight="40dp"
        android:paddingLeft="25dp"
        android:paddingRight="25dp"
        android:text="1/100"
        android:textColor="@color/text_color_black_000"
        android:textSize="12sp" />

    <com.wedevote.wdbook.ui.read.lib.TextSelectOperateLayout
        android:id="@+id/book_page_text_selected_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        tools:visibility="gone" />


</RelativeLayout>