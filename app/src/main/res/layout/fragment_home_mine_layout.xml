<?xml version="1.0" encoding="utf-8"?>
<com.aquila.lib.layout.SmartRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mine_root_container_Layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_root_view_bg"
    app:srlEnableLoadMore="false">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?theme_root_view_bg"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/mine_top_container_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                app:layout_constraintTop_toTopOf="parent">

                <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
                    android:id="@+id/home_mine_top_title_layout"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:background="?theme_bg_color_white"
                    app:attr_hide_back="true"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:id="@+id/home_mine_notification_ImageView"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:layout_alignParentRight="true"
                        android:scaleType="center"
                        android:src="@drawable/ic_mine_notification"
                        app:tint="?theme_icon_tint_color"
                        tools:ignore="UseAppTint" />

                    <com.aquila.lib.widget.view.DotView
                        android:id="@+id/home_mine_notification_count_DotView"
                        android:layout_width="5dp"
                        android:layout_height="5dp"
                        android:layout_alignTop="@id/home_mine_notification_ImageView"
                        android:layout_alignRight="@id/home_mine_notification_ImageView"
                        android:layout_marginStart="11dp"
                        android:layout_marginTop="11dp"
                        android:layout_marginEnd="11dp"
                        android:layout_marginBottom="11dp"
                        android:gravity="center"
                        android:visibility="gone"
                        app:attr_dot_color="?theme_text_color_red"
                        app:attr_dot_radius="4dp"
                        tools:visibility="visible" />

                </com.wedevote.wdbook.ui.widgets.CommTopTitleLayout>

                <LinearLayout
                    android:id="@+id/mine_not_login_LinerLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    android:paddingTop="24dp"
                    android:paddingBottom="24dp"
                    android:background="?theme_bg_color_white"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="@string/not_login"
                        android:textColor="?attr/theme_text_color_black"
                        android:textSize="18sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="50dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginRight="50dp"
                        android:gravity="center"
                        android:text="@string/after_login_tip"
                        android:textColor="?attr/theme_text_color_gray"
                        android:textSize="14sp" />

                    <Button
                        android:id="@+id/sso_register_Button"
                        android:layout_width="match_parent"
                        android:layout_height="44dp"
                        android:layout_marginLeft="24dp"
                        android:layout_marginTop="20dp"
                        android:layout_marginRight="24dp"
                        android:background="@drawable/selector_all_conner_brown_stroke_bg"
                        android:text="@string/not_account_please_regester"
                        android:textColor="@color/color_orange_FF8A00"
                        android:textSize="16sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <Button
                        android:id="@+id/sso_login_Button"
                        android:layout_width="match_parent"
                        android:layout_height="44dp"
                        android:layout_marginLeft="24dp"
                        android:layout_marginTop="20dp"
                        android:layout_marginRight="24dp"
                        android:background="@drawable/selector_all_conner_brown_bg"
                        android:text="@string/have_account_please_login"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/mine_user_info_LinerLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?theme_bg_color_white"
                    android:gravity="left|center_vertical"
                    android:orientation="horizontal"
                    android:visibility="visible">

                    <ImageView
                        android:id="@+id/mine_avatar_ImageView"
                        android:layout_width="90dp"
                        android:layout_height="90dp"
                        android:layout_margin="16dp"
                        android:src="@drawable/ic_default_avatar" />

                    <TextView
                        android:id="@+id/mine_nickname_TextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:maxLines="1"
                        android:ellipsize="end"
                        android:gravity="left|center_vertical"
                        android:minHeight="30dp"
                        android:textColor="?attr/theme_text_color_black_373636"
                        android:textSize="18sp"
                        tools:text="这个是昵称" />

                    <TextView
                        android:id="@+id/mine_edit_account_TextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:minHeight="30dp"
                        android:paddingTop="10dp"
                        android:paddingBottom="10dp"
                        android:paddingRight="16dp"
                        android:paddingLeft="5dp"
                        android:textColor="?attr/theme_text_color_black_373636"
                        android:textSize="14sp"
                        android:text="@string/edit_account"
                        />
                </LinearLayout>

            </LinearLayout>


            <com.wedevote.wdbook.ui.widgets.WidgetMineItemLayout
                android:id="@+id/mine_note_count_Layout"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="8dp"
                android:background="?theme_bg_color_white"

                android:paddingLeft="15dp"
                android:paddingRight="15dp"
                app:attr_arrow_image_tint="?theme_icon_tint_color"
                app:attr_image_src="@drawable/ic_mine_note"
                app:attr_image_tint="?theme_icon_tint_color"
                app:attr_title_text="@string/notes"

                app:layout_constraintTop_toBottomOf="@id/mine_top_container_layout"
                tools:attr_data_text="123条" />


            <LinearLayout
                android:id="@+id/mine_middle_container_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="?theme_bg_color_white_double_line"
                android:orientation="vertical"
                app:layout_constraintTop_toBottomOf="@id/mine_note_count_Layout">

                <com.wedevote.wdbook.ui.widgets.WidgetMineItemLayout
                    android:id="@+id/mine_book_Layout"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:background="?theme_comm_click_bg"

                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    app:attr_arrow_image_tint="?theme_icon_tint_color"
                    app:attr_image_src="@drawable/ic_mine_folder"
                    app:attr_image_tint="?theme_icon_tint_color"
                    app:attr_title_text="@string/home_mine_my_book"
                    tools:attr_data_text="323" />

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="0.7dp"
                    android:layout_marginLeft="16dp"
                    android:background="?theme_bg_color_divide_line_color" />

                <com.wedevote.wdbook.ui.widgets.WidgetMineItemLayout
                    android:id="@+id/mine_favorite_Layout"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:background="?theme_comm_click_bg"

                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    app:attr_arrow_image_tint="?theme_icon_tint_color"
                    app:attr_image_src="@drawable/ic_mine_favorite"
                    app:attr_image_tint="?theme_icon_tint_color"
                    app:attr_title_text="@string/label_favorite"
                    tools:attr_data_text="33本" />

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="0.7dp"
                    android:layout_marginLeft="16dp"
                    android:background="?theme_bg_color_divide_line_color" />

                <com.wedevote.wdbook.ui.widgets.WidgetMineItemLayout
                    android:id="@+id/mine_order_Layout"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:background="@drawable/selector_comm_click_bg"

                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    app:attr_arrow_image_tint="?theme_icon_tint_color"
                    app:attr_image_src="@drawable/ic_mine_order"
                    app:attr_image_tint="?theme_icon_tint_color"
                    app:attr_title_text="@string/home_mine_order_record" />

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="0.7dp"
                    android:layout_marginLeft="16dp"
                    android:background="?theme_bg_color_divide_line_color" />

                <com.wedevote.wdbook.ui.widgets.WidgetMineItemLayout
                    android:id="@+id/mine_coupon_Layout"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:background="@drawable/selector_comm_click_bg"

                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    app:attr_arrow_image_tint="?theme_icon_tint_color"
                    app:attr_image_src="@drawable/ic_mine_coupon"
                    app:attr_image_tint="?theme_icon_tint_color"
                    app:attr_title_text="优惠券"
                    tools:attr_data_text="3" />

            </LinearLayout>


            <LinearLayout
                android:id="@+id/mine_setting_container_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="?theme_bg_color_white_double_line"
                android:orientation="vertical"
                app:layout_constraintTop_toBottomOf="@id/mine_middle_container_layout">

                <com.wedevote.wdbook.ui.widgets.WidgetMineItemLayout
                    android:id="@+id/mine_setting_ItemLayout"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:background="@drawable/selector_comm_click_bg"

                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    app:attr_arrow_image_tint="?theme_icon_tint_color"
                    app:attr_image_src="@drawable/ic_mine_setting"
                    app:attr_image_tint="?theme_icon_tint_color"
                    app:attr_title_text="@string/home_mine_setting" />

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="0.7dp"
                    android:layout_marginLeft="16dp"
                    android:background="?theme_bg_color_divide_line_color" />


                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <com.wedevote.wdbook.ui.widgets.WidgetMineItemLayout
                        android:id="@+id/mine_feedback_ItemLayout"
                        android:layout_width="match_parent"
                        android:layout_height="50dp"
                        android:background="@drawable/selector_comm_click_bg"

                        android:paddingLeft="15dp"
                        android:paddingRight="15dp"
                        android:visibility="visible"
                        app:attr_arrow_image_tint="?theme_icon_tint_color"
                        app:attr_image_src="@drawable/ic_mine_feedback"
                        app:attr_image_tint="?theme_icon_tint_color"
                        app:attr_title_text="@string/help_and_feedback"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.aquila.lib.widget.view.DotView
                        android:id="@+id/mine_feedback_flag_DotView"
                        android:layout_width="10dp"
                        android:layout_height="10dp"
                        android:layout_marginLeft="40dp"
                        android:layout_marginTop="10dp"
                        android:visibility="gone"
                        app:attr_dot_color="?theme_text_color_red"
                        app:attr_dot_radius="4dp"
                        app:layout_constraintLeft_toLeftOf="@+id/mine_feedback_ItemLayout"
                        app:layout_constraintTop_toTopOf="@+id/mine_feedback_ItemLayout"
                        tools:visibility="visible" />

                </androidx.constraintlayout.widget.ConstraintLayout>
            </LinearLayout>

            <TextView
                android:id="@+id/mine_app_version_TextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|center_horizontal"
                android:layout_marginTop="50dp"
                android:layout_marginBottom="8dp"
                android:gravity="center"
                android:text="V1.0"
                android:textColor="@color/text_color_black_4c"
                android:textSize="14sp"
                app:layout_constraintBottom_toTopOf="@id/mine_app_mail"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/mine_setting_container_layout"
                app:layout_constraintVertical_bias="1" />

            <TextView
                android:id="@+id/mine_app_mail"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|center_horizontal"
                android:layout_marginBottom="8dp"
                android:gravity="center"
                android:lineSpacingMultiplier="1.3"
                android:text="@string/connact_us"
                android:textColor="@color/text_color_gray_707A89"
                android:textSize="12sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>
</com.aquila.lib.layout.SmartRefreshLayout>