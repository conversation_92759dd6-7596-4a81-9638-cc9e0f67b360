<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="15dp"
    android:layout_marginBottom="15dp"
    android:background="?theme_bg_white_conner_8dp"
    android:orientation="vertical"
    android:padding="20dp">

    <LinearLayout
        android:id="@+id/server_item_main_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/server_item_title_TextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Title"
                android:textColor="?theme_text_color_black"
                android:textSize="22sp" />

            <ImageView
                android:id="@+id/server_item_close_ImageView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:src="?theme_dialog_close_ImageView" />
        </RelativeLayout>

        <TextView
            android:id="@+id/server_item_time_TextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Added on 2024-1-1"
            android:textColor="?theme_text_color_gray"
            android:textSize="12sp" />

        <com.aquila.lib.widget.view.SquaredImageView
            android:id="@+id/server_item_qr_ImageView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:src="@drawable/default_img_rect" />

        <LinearLayout
            android:id="@+id/server_item_status_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/server_item_status_ImageView"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_gravity="center_vertical"
                android:src="@drawable/ic_finished_green"
                android:visibility="visible" />

            <TextView
                android:id="@+id/server_item_status_TextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:text="Active"
                android:textColor="@color/green_4DCC33"
                android:textSize="16sp" />
        </LinearLayout>
    </LinearLayout>

    <TextView
        android:id="@+id/server_item_choose_TextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:background="@drawable/shape_gray_color_corner_56"
        android:gravity="center_horizontal"
        android:padding="10dp"
        android:text="Use this service"
        android:textColor="?theme_primary_color_22252A"
        android:textSize="16sp" />
</LinearLayout>