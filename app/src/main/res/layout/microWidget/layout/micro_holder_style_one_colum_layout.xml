<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/micro_item_layout_padding">

    <TextView
        android:id="@+id/micro_one_column_title_TextView"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:gravity="center_vertical"
        android:textColor="@color/text_color_black_000"
        android:textSize="18sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="最受欢迎新书" />


    <TextView
        android:id="@+id/micro_one_column_view_all_TextView"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:drawableRight="@drawable/ic_arrow_right_gray"
        android:drawablePadding="10dp"
        android:gravity="center"
        android:paddingLeft="10dp"
        android:text="@string/label_all"
        android:textColor="@color/text_color_black_000"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="@id/micro_one_column_title_TextView"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/micro_one_column_title_TextView" />


    <com.aquila.lib.widget.view.CustomRecyclerView
        android:id="@+id/micro_one_column_data_RecyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        app:attr_item_decoration_type="ITEM_SPACE"
        app:attr_layout_style="list_horizontal"
        app:layout_constraintTop_toBottomOf="@id/micro_one_column_title_TextView"
        tools:itemCount="20"
        tools:listitem="@layout/item_micro_picture_portrait_layout" />

</androidx.constraintlayout.widget.ConstraintLayout>