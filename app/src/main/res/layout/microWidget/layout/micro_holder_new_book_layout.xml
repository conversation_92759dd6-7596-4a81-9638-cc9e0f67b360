<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:padding="@dimen/micro_item_layout_padding">

    <TextView
        android:id="@+id/micro_new_book_title_TextView"
        android:layout_width="wrap_content"
        android:layout_height="50dp"
        android:gravity="center_vertical"
        android:text="最新上架"
        android:textColor="@color/text_color_black_000"
        android:textSize="16sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="最新上架" />

    <com.aquila.lib.widget.group.GroupImageTextLayout
        android:id="@+id/micro_new_book_view_all_layout"
        android:layout_width="wrap_content"
        android:layout_height="50dp"
        android:gravity="center_vertical"
        app:attr_image_src="@drawable/ic_arrow_right_gray"
        app:attr_parent_orientation="IMAGE_RIGHT_TEXT_LEFT"
        app:attr_text="全部"
        app:attr_text_color="@color/text_color_gray_707A89"
        app:attr_text_margin_image_size="15dp"
        app:attr_text_size="16sp"
        app:layout_constraintBottom_toBottomOf="@+id/micro_new_book_title_TextView"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/micro_new_book_title_TextView"

        />


    <com.aquila.lib.widget.view.CustomRecyclerView
        android:id="@+id/micro_new_book_data_RecyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="5dp"
        android:nestedScrollingEnabled="false"
        app:attr_item_decoration_type="ITEM_DIVIDE_LNE"
        app:attr_item_divide_line_color="@color/divide_line_color"
        app:attr_item_divide_line_size="@dimen/divide_line_size"
        app:attr_item_margin="15dp"
        app:attr_layout_style="list_vertical"
        app:layout_constraintTop_toBottomOf="@id/micro_new_book_title_TextView"
        tools:itemCount="5"
        tools:listitem="@layout/item_micro_store_book_list_layout" />

</androidx.constraintlayout.widget.ConstraintLayout>