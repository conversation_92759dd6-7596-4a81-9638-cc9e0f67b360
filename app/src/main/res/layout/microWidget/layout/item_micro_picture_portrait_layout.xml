<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="5dp">

    <com.aquila.lib.widget.view.AdaptiveImageView
        android:id="@+id/item_picture_portrait_ImageView"
        android:layout_width="126dp"
        android:layout_height="wrap_content"
        android:scaleType="fitXY"
        app:attr_aspect_ratio="126,178"
        app:attr_fit_type="FIT_NONE"
        tools:src="@drawable/ic_default_book_cover" />

    <TextView
        android:id="@+id/item_picture_name_TextView"
        android:layout_width="120dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="5dp"
        android:gravity="center_vertical"
        android:minHeight="20dp"
        android:singleLine="true"
        android:textColor="?theme_text_color_black"
        android:textSize="16sp"
        tools:text="书籍标题书籍标题书籍标题书籍标题书籍标题书籍标题" />

    <TextView
        android:id="@+id/item_picture_author_TextView"
        android:layout_width="120dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="5dp"
        android:gravity="center_vertical"
        android:minHeight="20dp"
        android:singleLine="true"
        android:textColor="?theme_text_color_gray"
        android:textSize="12sp"
        tools:text="书籍作者" />


</LinearLayout>