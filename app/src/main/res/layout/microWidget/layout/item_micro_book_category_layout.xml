<?xml version="1.0" encoding="utf-8"?>

<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="80dp"
    android:layout_gravity="center"
    android:layout_marginLeft="8dp"
    android:layout_marginRight="8dp"
    android:background="?theme_bg_color_white_1E"
    app:cardCornerRadius="5dp"
    tools:layout_height="80dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?theme_bg_color_white_1E"
        android:padding="10dp"
        android:paddingLeft="15dp"
        android:paddingTop="15dp"
        android:paddingRight="13dp"
        android:paddingBottom="15dp">

        <ImageView
            android:id="@+id/cell_category_cover_ImageView"
            android:layout_width="46dp"
            android:layout_height="48dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:scaleType="fitCenter"
            tools:src="@drawable/app_logo" />

        <TextView
            android:id="@+id/cell_category_name_TextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignTop="@id/cell_category_cover_ImageView"
            android:layout_toLeftOf="@id/cell_category_cover_ImageView"
            android:paddingRight="15dp"
            android:textColor="?theme_text_color_black"
            android:textSize="14sp"
            tools:text="书籍分类书籍" />

    </RelativeLayout>


</androidx.cardview.widget.CardView>