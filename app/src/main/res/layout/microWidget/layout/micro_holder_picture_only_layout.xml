<?xml version="1.0" encoding="utf-8"?>

<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingLeft="0dp"
    android:paddingTop="5dp"
    android:paddingRight="0dp"
    android:paddingBottom="5dp">

    <androidx.cardview.widget.CardView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:cardCornerRadius="3dp"
        android:layout_gravity="center">

        <com.aquila.lib.widget.view.AdaptiveImageView
            android:id="@+id/micro_holder_picture_ImageView"
            android:layout_width="match_parent"
            android:layout_height="160dp"
            android:layout_gravity="center"
            android:scaleType="fitXY"
            app:attr_aspect_ratio="350,160"
            app:attr_fit_type="FIT_NONE" />

    </androidx.cardview.widget.CardView>

</FrameLayout>