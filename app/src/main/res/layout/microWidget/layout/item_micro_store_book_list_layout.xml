<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="5dp"
    android:paddingBottom="5dp">

    <RelativeLayout
        android:id="@+id/item_book_info_cover_RelativeLayout"
        android:layout_width="83dp"
        android:layout_height="110dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:background="?theme_text_color_black_e3_4e"
        >

        <com.aquila.lib.widget.view.AdaptiveImageView
            android:id="@+id/item_book_info_cover_ImageView"
            android:layout_width="81dp"
            android:layout_height="108dp"
            android:layout_centerInParent="true"
            android:adjustViewBounds="true"
            android:background="@drawable/shape_book_corners_bg"
            android:scaleType="centerCrop"
            app:attr_aspect_ratio="81,108"
            tools:src="@drawable/demo_03" />
    </RelativeLayout>


    <ImageView
        android:id="@+id/item_book_info_purchase_flag_ImageView"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:scaleType="fitCenter"
        android:src="@drawable/ic_book_purchased"
        android:visibility="gone"
        app:layout_constraintRight_toRightOf="@id/item_book_info_cover_RelativeLayout"
        app:layout_constraintTop_toTopOf="@id/item_book_info_cover_RelativeLayout"
        tools:visibility="visible" />


    <TextView
        android:id="@+id/item_book_info_name_TextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:maxLines="2"
        android:ellipsize="end"
        android:textColor="?theme_text_color_black"
        android:textSize="16sp"
        app:layout_constraintLeft_toRightOf="@id/item_book_info_cover_RelativeLayout"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/item_book_info_cover_RelativeLayout"
        app:layout_constraintBottom_toTopOf="@id/item_book_info_author_TextView"
        tools:text="精灵宝钻(精装插图本)" />

    <TextView
        android:id="@+id/item_book_info_author_TextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:paddingTop="0dp"
        android:paddingBottom="0dp"
        android:singleLine="true"
        android:textColor="?theme_text_color_black"
        android:textSize="12sp"
        app:layout_constraintLeft_toLeftOf="@id/item_book_info_name_TextView"
        app:layout_constraintRight_toRightOf="@id/item_book_info_name_TextView"
        app:layout_constraintTop_toBottomOf="@id/item_book_info_name_TextView"
        app:layout_constraintBottom_toTopOf="@id/item_book_info_original_price_TextView"
        tools:text="J.R.R.托尔金\nJ.R.R.托尔金J.R.R.托尔金J.R.R.托尔金托尔金托尔金托尔金托尔金托尔金" />

    <TextView
        android:id="@+id/item_book_info_original_price_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:singleLine="true"
        android:textColor="?theme_text_color_black"
        android:textSize="12sp"
        app:layout_constraintBottom_toTopOf="@id/item_book_info_discount_price_TextView"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintLeft_toLeftOf="@id/item_book_info_name_TextView"
        app:layout_constraintRight_toLeftOf="@id/item_book_read_Button"
        app:layout_constraintTop_toBottomOf="@id/item_book_info_author_TextView"
        tools:text="现价$1(约￥6.97)(2.9折)现价$1(约￥6.97)(2.9折)现价$1(约￥6.97)(2.9折)现价$1(约￥6.97)(2.9折)现价$1(约￥6.97)(2.9折)现价$1(约￥6.97)(2.9折)现价$1(约￥6.97)(2.9折)现价$1(约￥6.97)(2.9折)" />

    <TextView
        android:id="@+id/item_book_info_discount_price_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:ellipsize="end"
        android:singleLine="true"
        android:text="$19.9"
        android:textColor="?theme_text_color_black"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintLeft_toLeftOf="@id/item_book_info_name_TextView"
        app:layout_constraintRight_toLeftOf="@id/item_book_read_Button"
        app:layout_constraintTop_toBottomOf="@id/item_book_info_original_price_TextView" />


    <com.wedevote.wdbook.ui.widgets.ProgressButton
        android:id="@+id/item_book_read_Button"
        android:layout_width="80dp"
        android:layout_height="32dp"
        android:background="@drawable/selector_download_btn_orange_bg"
        android:elevation="10dp"
        android:textColor="@color/color_E9973E"
        android:textSize="12sp"
        android:visibility="gone"
        app:attr_bg_color="#D7DADE"
        app:attr_need_show_Progress="false"
        app:attr_progress_color="@color/color_E9973E"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:attr_button_progress="0"
        tools:text="@string/download"
        tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout>