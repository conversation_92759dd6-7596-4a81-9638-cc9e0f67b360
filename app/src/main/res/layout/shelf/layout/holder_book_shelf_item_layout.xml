<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="7dp">

    <androidx.cardview.widget.CardView
        android:id="@+id/archive_cover_CardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="4dp"
        app:cardElevation="2dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?theme_cover_border_bg">

            <com.aquila.lib.widget.view.AdaptiveImageView
                android:id="@+id/shelf_book_cover_ImageView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="0.5dp"
                android:scaleType="fitXY"
                app:attr_aspect_ratio="92,132"
                app:attr_round_height="3dp"
                app:attr_round_width="3dp"
                app:layout_constraintBottom_toTopOf="@id/shelf_book_name_TextView"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:src="@drawable/ic_default_book_cover" />

        </FrameLayout>

    </androidx.cardview.widget.CardView>

    <ImageView
        android:id="@+id/shelf_book_status_ImageView"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:layout_margin="5dp"
        android:elevation="10dp"
        android:padding="2.5dp"
        android:src="@drawable/ic_shelf_book_cloud"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/archive_cover_CardView"
        app:layout_constraintRight_toRightOf="@id/archive_cover_CardView"
        tools:visibility="visible" />


    <ImageView
        android:id="@+id/shelf_book_check_ImageView"
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:clickable="false"
        android:elevation="10dp"
        android:padding="5dp"
        android:scaleType="fitCenter"
        android:src="@drawable/selected_check_box_yellow"
        app:layout_constraintBottom_toBottomOf="@id/archive_cover_CardView"
        app:layout_constraintLeft_toLeftOf="@id/archive_cover_CardView"
        app:layout_constraintRight_toRightOf="@id/archive_cover_CardView"
        app:layout_constraintTop_toTopOf="@id/archive_cover_CardView" />

    <TextView
        android:id="@+id/shelf_book_read_percent_TextView"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:layout_marginRight="5dp"
        android:background="@drawable/ic_read_percent"
        android:gravity="center"
        android:paddingBottom="2dp"
        android:textColor="@color/white"
        android:textSize="6sp"
        android:visibility="gone"
        app:layout_constraintRight_toRightOf="@id/archive_cover_CardView"
        app:layout_constraintTop_toTopOf="@id/archive_cover_CardView"
        tools:text="3%"
        tools:visibility="visible" />

    <!--        </androidx.constraintlayout.widget.ConstraintLayout>-->

    <!--    </androidx.cardview.widget.CardView>-->


    <TextView
        android:id="@+id/shelf_book_name_TextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:ellipsize="end"
        android:gravity="top"
        android:maxLines="2"
        android:paddingLeft="2.5dp"
        android:paddingRight="2.5dp"
        android:textColor="?theme_text_color_black"
        android:textSize="12sp"
        app:layout_constraintLeft_toLeftOf="@id/archive_cover_CardView"
        app:layout_constraintRight_toRightOf="@id/archive_cover_CardView"
        app:layout_constraintTop_toBottomOf="@id/archive_cover_CardView"
        tools:text="纳尼亚传奇纳尼亚传奇纳尼亚传奇纳尼亚传奇纳尼亚传奇纳尼亚传奇" />

    <View
        android:id="@+id/shelf_mask_View"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#70373636"
        app:layout_constraintBottom_toBottomOf="@id/archive_cover_CardView"
        app:layout_constraintLeft_toLeftOf="@id/archive_cover_CardView"
        app:layout_constraintRight_toRightOf="@id/archive_cover_CardView"
        app:layout_constraintTop_toTopOf="@id/archive_cover_CardView" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/shelf_download_info_container_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/archive_cover_CardView"
        app:layout_constraintLeft_toLeftOf="@id/archive_cover_CardView"
        app:layout_constraintRight_toRightOf="@id/archive_cover_CardView"
        app:layout_constraintTop_toTopOf="@id/archive_cover_CardView"
        tools:visibility="visible">

        <ProgressBar
            android:id="@+id/note_book_download_ProgressBar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="0dp"
            android:layout_height="3dp"
            android:max="100"
            android:progressDrawable="@drawable/layer_list_progress_drawable"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.7"
            tools:progress="40" />

        <TextView
            android:id="@+id/note_book_download_percent_TextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:elevation="5dp"
            android:textColor="?theme_text_color_white"
            android:textSize="10sp"
            app:layout_constraintLeft_toLeftOf="@id/note_book_download_ProgressBar"
            app:layout_constraintRight_toRightOf="@id/note_book_download_ProgressBar"
            app:layout_constraintTop_toBottomOf="@id/note_book_download_ProgressBar"
            tools:text="80%" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
