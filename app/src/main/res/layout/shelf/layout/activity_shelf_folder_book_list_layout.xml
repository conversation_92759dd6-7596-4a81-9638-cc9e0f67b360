<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="?theme_bg_color_white"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/book_folder_title_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/top_title_height"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_max_title_width="180dp"
        app:layout_constraintTop_toTopOf="parent"
        tools:attr_title_text="文件夹名称文件夹名称">

        <TextView
            android:id="@+id/book_folder_edit_TextView"
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            android:layout_alignParentRight="true"
            android:background="@drawable/selector_comm_click_bg"
            android:foreground="?android:attr/selectableItemBackground"
            android:gravity="center"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:text="@string/edit"
            android:textColor="?theme_text_color_black"
            android:textSize="16sp"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/book_folder_select_all_TextView"
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            android:layout_alignParentLeft="true"
            android:background="@drawable/selector_comm_click_bg"
            android:foreground="?android:attr/selectableItemBackground"
            android:gravity="center"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:text="@string/select_all"
            android:textColor="?theme_text_color_black"
            android:textSize="16sp"
            android:visibility="gone"
            tools:visibility="visible" />

    </com.wedevote.wdbook.ui.widgets.CommTopTitleLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/shelf_folder_option_container_Layout"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:paddingLeft="5dp"
        android:paddingRight="5dp"
        android:visibility="visible"
        app:layout_constraintTop_toBottomOf="@id/book_folder_title_layout"
        tools:visibility="visible">

        <TextView
            android:id="@+id/shelf_folder_change_name_TextView"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:background="@drawable/selector_comm_click_bg"
            android:foreground="?android:attr/selectableItemBackground"
            android:gravity="center"
            android:paddingLeft="5dp"
            android:paddingRight="5dp"
            android:text="@string/change_group_name"
            android:textColor="?theme_text_color_dark"
            android:textSize="14sp"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/shelf_folder_delete_folder_TextView"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:background="@drawable/selector_comm_click_bg"
            android:foreground="?android:attr/selectableItemBackground"
            android:gravity="center"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:text="@string/delete_group"
            android:textColor="?theme_text_color_dark"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="@id/shelf_folder_change_name_TextView"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/shelf_folder_change_name_TextView" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <Button
        android:id="@+id/shelf_folder_move_folder_Button"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="?theme_bg_color_white_top_line"
        android:foreground="?android:attr/selectableItemBackground"
        android:paddingLeft="5dp"
        android:paddingRight="5dp"
        android:text="@string/team_up"
        android:textColor="?theme_text_color_blue_book_notes"
        android:textSize="14sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:visibility="visible" />

    <com.aquila.lib.layout.SmartRefreshLayout
        android:id="@+id/book_folder_data_RefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/shelf_folder_move_folder_Button"
        app:layout_constraintTop_toBottomOf="@id/shelf_folder_option_container_Layout"
        tools:visibility="visible">

        <com.aquila.lib.widget.view.CustomRecyclerView
            android:id="@+id/book_folder_data_RecyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:attr_item_decoration_type="ITEM_SPACE"
            app:attr_item_divide_line_color="@color/divide_line_color"
            app:attr_layout_style="grid_vertical"
            app:attr_span_count="3"
            tools:itemCount="33"
            tools:listitem="@layout/holder_book_shelf_item_layout" />

    </com.aquila.lib.layout.SmartRefreshLayout>

    <TextView
        android:id="@+id/book_folder_empty_prompt_TextView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:gravity="center"
        android:text="@string/group_no_data"
        android:textColor="?theme_text_color_dark"
        android:textSize="14sp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/shelf_folder_move_folder_Button"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/shelf_folder_option_container_Layout" />


    <!--    <GridView-->
    <!--        android:id="@+id/book_folder_data_GridView"-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="match_parent"-->
    <!--        android:numColumns="3"-->
    <!--        tools:listitem="@layout/holder_book_shelf_item_layout" />-->


</androidx.constraintlayout.widget.ConstraintLayout>