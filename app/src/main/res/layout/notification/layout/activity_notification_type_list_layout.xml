<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_root_view_bg"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/notification_type_top_title_Layout"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="待办事项" />


    <com.aquila.lib.layout.SmartRefreshLayout
        android:id="@+id/notification_type_RefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.aquila.lib.widget.view.CustomRecyclerView
                android:id="@+id/notification_type_data_RecyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:attr_layout_style="list_vertical"
                tools:listitem="@layout/holder_notification_center_message_layout" />

            <LinearLayout
                android:id="@+id/notification_type_empty_container_Layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="150dp"
                    android:text="@string/no_message_tips_title"
                    android:textColor="?theme_text_color_black"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:text="@string/no_message_tips_content"
                    android:textColor="?theme_text_color_black"
                    android:textSize="14sp" />

            </LinearLayout>

        </FrameLayout>

    </com.aquila.lib.layout.SmartRefreshLayout>


</LinearLayout>