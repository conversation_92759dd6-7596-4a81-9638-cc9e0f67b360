<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_root_view_bg"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/notification_center_top_title_layout"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="@string/notificaiton_center"
        app:attr_title_text_color="?theme_text_color_black">

        <ImageView
            android:id="@+id/notification_center_clear_ImageView"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:background="?theme_comm_click_bg"
            android:foreground="?selectableItemBackground"
            android:scaleType="center"
            android:src="@drawable/ic_notification_clear"
            android:tint="?theme_icon_tint_color"
            android:layout_alignParentRight="true"
            tools:ignore="UseAppTint" />
    </com.wedevote.wdbook.ui.widgets.CommTopTitleLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/notification_center_type_container_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?theme_bg_color_white"
        android:orientation="horizontal"
        android:paddingLeft="50dp"
        android:paddingTop="10dp"
        android:paddingRight="50dp"
        android:paddingBottom="10dp">

        <com.aquila.lib.widget.group.GroupImageTextLayout
            android:id="@+id/notification_center_type_platform_Layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:padding="10dp"
            app:attr_image_height="32dp"
            app:attr_image_src="@drawable/ic_notification_type_platform"
            app:attr_image_width="32dp"
            app:attr_parent_orientation="IMAGE_TOP_TEXT_BOTTOM"
            app:attr_text="@string/message_type_platfom"
            app:attr_text_color="?theme_text_color_black"
            app:attr_text_margin_image_size="5dp"
            app:attr_text_size="16sp"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/notification_center_type_coupon_Layout"
            app:layout_constraintTop_toTopOf="parent" />


        <com.aquila.lib.widget.group.GroupImageTextLayout
            android:id="@+id/notification_center_type_coupon_Layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="10dp"
            app:attr_image_height="32dp"
            app:attr_image_src="@drawable/ic_notification_type_coupon"
            app:attr_image_width="32dp"
            app:attr_parent_orientation="IMAGE_TOP_TEXT_BOTTOM"
            app:attr_text="@string/message_type_recommend"
            app:attr_text_color="?theme_text_color_black"
            app:attr_text_margin_image_size="5dp"
            app:attr_text_size="16sp"
            app:layout_constraintLeft_toRightOf="@id/notification_center_type_platform_Layout"
            app:layout_constraintRight_toLeftOf="@+id/notification_center_type_todo_Layout"
            app:layout_constraintTop_toTopOf="parent" />


        <com.aquila.lib.widget.group.GroupImageTextLayout
            android:id="@+id/notification_center_type_todo_Layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="15dp"
            android:padding="10dp"
            android:visibility="gone"
            app:attr_image_height="32dp"
            app:attr_image_src="@drawable/ic_notification_type_todo_list"
            app:attr_image_width="32dp"
            app:attr_parent_orientation="IMAGE_TOP_TEXT_BOTTOM"
            app:attr_text="@string/message_type_task"
            app:attr_text_color="?theme_text_color_black"
            app:attr_text_margin_image_size="5dp"
            app:attr_text_size="16sp"
            app:layout_constraintLeft_toRightOf="@id/notification_center_type_coupon_Layout"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/notification_center_platform_count_TextView"
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="5dp"
            android:background="?theme_circle_bg_red"
            android:gravity="center"
            android:textColor="?theme_text_color_white"
            android:textSize="8sp"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="@id/notification_center_type_platform_Layout"
            app:layout_constraintRight_toRightOf="@id/notification_center_type_platform_Layout"
            app:layout_constraintTop_toTopOf="@id/notification_center_type_platform_Layout"
            tools:text="2"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/notification_center_coupon_count_TextView"
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="5dp"
            android:background="?theme_circle_bg_red"
            android:ellipsize="end"
            android:gravity="center"
            android:singleLine="true"
            android:textColor="?theme_text_color_white"
            android:textSize="8sp"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="@id/notification_center_type_coupon_Layout"
            app:layout_constraintRight_toRightOf="@id/notification_center_type_coupon_Layout"
            app:layout_constraintTop_toTopOf="@id/notification_center_type_coupon_Layout"
            tools:text="24"

            tools:visibility="visible" />

        <TextView
            android:id="@+id/notification_center_todo_count_TextView"
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="5dp"
            android:background="?theme_circle_bg_red"
            android:ellipsize="end"
            android:gravity="center"
            android:singleLine="true"
            android:textColor="?theme_text_color_white"
            android:textSize="8sp"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="@id/notification_center_type_todo_Layout"
            app:layout_constraintRight_toRightOf="@id/notification_center_type_todo_Layout"
            app:layout_constraintTop_toTopOf="@id/notification_center_type_todo_Layout"
            tools:text="..."
            tools:visibility="gone" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.aquila.lib.layout.SmartRefreshLayout
        android:id="@+id/fragment_notification_RefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/notification_center_type_platform_Layout"
        tools:visibility="visible">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.aquila.lib.widget.view.CustomRecyclerView
                android:id="@+id/fragment_notification_data_RecyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:attr_layout_style="list_vertical"
                tools:listitem="@layout/holder_notification_center_message_layout"
                tools:visibility="gone" />

            <LinearLayout
                android:id="@+id/fragment_notification_empty_container_Layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="150dp"
                    android:text="@string/no_message_tips_title"
                    android:textColor="?theme_text_color_black"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:text="@string/no_message_tips_content"
                    android:textColor="?theme_text_color_black"
                    android:textSize="14sp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/fragment_notification_loading_container_Layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="150dp"
                android:gravity="center"
                android:padding="20dp"
                android:visibility="gone"
                tools:visibility="visible">

                <ProgressBar
                    android:layout_width="20dp"
                    android:layout_height="20dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:text="数据获取中"
                    android:textColor="?theme_text_color_black"
                    android:textSize="16sp" />

            </LinearLayout>

        </FrameLayout>

    </com.aquila.lib.layout.SmartRefreshLayout>


</LinearLayout>