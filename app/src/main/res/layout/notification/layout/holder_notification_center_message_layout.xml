<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:fitsSystemWindows="true"
    android:orientation="vertical"
    android:padding="15dp">

    <TextView
        android:id="@+id/notification_message_time_TextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:minHeight="40dp"
        android:text=""
        android:textColor="?theme_text_color_dark_4c"
        android:textSize="12sp"
        tools:text="2021年12月年09日 上午08:00" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?theme_bg_white_conner_8dp"
        android:paddingLeft="16dp"
        android:paddingTop="16dp"
        android:paddingRight="16dp">

        <com.aquila.lib.widget.view.DotView
            android:id="@+id/notification_message_dot_View"
            android:layout_width="10dp"
            android:layout_height="10dp"
            android:layout_marginRight="5dp"
            android:visibility="visible"
            app:attr_dot_color="?theme_text_color_red"
            app:attr_dot_radius="5dp"
            app:layout_constraintBottom_toBottomOf="@id/notification_message_title_TextView"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/notification_message_title_TextView"
            app:layout_constraintTop_toTopOf="@id/notification_message_title_TextView"
            tools:visibility="gone" />

        <TextView
            android:id="@+id/notification_message_title_TextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:textColor="?theme_text_color_black"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintLeft_toRightOf="@id/notification_message_dot_View"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="重要通知" />

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toTopOf="@+id/notification_message_view_detail_Layout"
            app:layout_constraintLeft_toLeftOf="@id/notification_message_dot_View"
            app:layout_constraintRight_toRightOf="parent"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            app:layout_constraintTop_toBottomOf="@id/notification_message_title_TextView"
            app:layout_constraintVertical_bias="0">

            <TextView
                android:id="@+id/notification_message_content_TextView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?theme_bg_color_white_bottom_line"
                android:gravity="center_vertical"
                android:minHeight="40dp"
                android:paddingBottom="10dp"
                android:textColor="?theme_text_color_dark"
                android:textSize="14sp"
                android:visibility="gone"
                android:lineSpacingMultiplier="1.15"
                app:layout_constraintBottom_toTopOf="@+id/notification_message_view_detail_Layout"
                app:layout_constraintLeft_toLeftOf="@id/notification_message_dot_View"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/notification_message_title_TextView"
                app:layout_constraintVertical_bias="0"
                tools:text="微读书城」App已在苹果 App Store 中国区被下架，如您是中国大陆地区用户，已安装iOS版「微读书城」App，请查看应对方法。"
                tools:visibility="gone" />

            <com.aquila.lib.widget.view.AdaptiveImageView
                android:id="@+id/notification_message_picture_ImageView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:scaleType="fitXY"
                android:visibility="visible"
                app:attr_aspect_ratio="312,132"

                app:attr_fit_type="FIT_IMG_SIZE"
                app:attr_min_height="132dp"
                app:layout_constraintBottom_toTopOf="@id/notification_message_view_detail_Layout"
                app:layout_constraintLeft_toLeftOf="@id/notification_message_dot_View"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/notification_message_title_TextView"
                tools:src="@drawable/img_banner"
                tools:visibility="visible" />
        </FrameLayout>


        <com.aquila.lib.widget.group.GroupImageTextLayout
            android:id="@+id/notification_message_view_detail_Layout"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginBottom="8dp"
            android:foreground="?selectableItemBackground"
            android:gravity="center_vertical"
            app:attr_image_src="@drawable/ic_arrow_right_dark"
            app:attr_parent_orientation="IMAGE_RIGHT_TEXT_LEFT_SIDE_ALIGN"
            app:attr_text_color="?theme_text_color_black"
            app:attr_text_single_line="true"
            app:attr_text_margin_image_size="10dp"
            app:attr_text_max_width="320dp"
            app:attr_text_size="14sp"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:attr_text="查看详情" />


    </androidx.constraintlayout.widget.ConstraintLayout>


</LinearLayout>