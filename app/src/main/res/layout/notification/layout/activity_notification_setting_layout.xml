<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_root_view_bg"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/notification_setting_top_title_layout"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="@string/notification_setting" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?theme_bg_color_white"
        android:divider="?theme_divide_line_horizontal"
        android:orientation="vertical"
        android:showDividers="middle">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/notification_setting_accept_message_container_layout"
            android:layout_width="match_parent"
            android:visibility="gone"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/notification_setting_label_TextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:gravity="center_vertical"
                android:minHeight="30dp"
                android:text="@string/receive_message_notify"
                android:textColor="?theme_text_color_black"
                android:textSize="16sp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/notification_setting_label_desc_TextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:minHeight="30dp"
                android:text="@string/receive_message_tips"
                android:textColor="?theme_text_color_gray"
                android:textSize="14sp"
                app:layout_constraintLeft_toLeftOf="@id/notification_setting_label_TextView"
                app:layout_constraintTop_toBottomOf="@id/notification_setting_label_TextView" />

            <TextView
                android:id="@+id/notification_setting_label_status_TextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="10dp"
                android:gravity="center_vertical"
                android:minHeight="30dp"
                android:text="@string/notification_close"
                android:textColor="?theme_text_color_gray"
                android:textSize="14sp"
                app:layout_constraintBottom_toBottomOf="@id/notification_setting_label_TextView"
                app:layout_constraintRight_toLeftOf="@id/notification_setting_arrow_ImageView"
                app:layout_constraintTop_toTopOf="@id/notification_setting_label_TextView" />

            <ImageView
                android:id="@+id/notification_setting_arrow_ImageView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="15dp"
                android:src="@drawable/ic_arrow_right_gray"
                app:layout_constraintBottom_toBottomOf="@id/notification_setting_label_TextView"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@id/notification_setting_label_TextView" />

        </androidx.constraintlayout.widget.ConstraintLayout>


        <RelativeLayout
            android:id="@+id/notification_setting_receive_message_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?theme_comm_click_bg"
            android:foreground="?selectableItemBackground"
            android:minHeight="50dp">

            <TextView
                android:id="@+id/notification_setting_receive_coupon_message_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="15dp"
                android:gravity="center_vertical"
                android:minHeight="50dp"
                android:text="@string/receive_activity_notify"
                android:textColor="?theme_text_color_black"
                android:textSize="16sp" />

            <com.wedevote.wdbook.ui.widgets.SlideSwitch
                android:id="@+id/notification_setting_receive_SlideSwitch"
                android:layout_width="50dp"
                android:layout_height="25dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="15dp"
                app:attr_is_check="true"
                app:attr_shape_type="CIRCLE"
                app:attr_theme_color="?theme_text_color_orange" />

        </RelativeLayout>
    </LinearLayout>
</LinearLayout>