<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_root_view_bg"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/notification_message_detail_top_title_layout"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="@string/title_activity_detail">

        <ImageView
            android:id="@+id/notification_message_detail_share_ImageView"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_alignParentRight="true"
            android:background="?theme_comm_click_bg"
            android:foreground="?selectableItemBackground"
            android:scaleType="center"
            android:src="@drawable/ic_share_black"
            app:tint="?theme_icon_tint_color"
            android:visibility="gone"
            tools:ignore="UseAppTint"
            tools:visibility="visible" />
    </com.wedevote.wdbook.ui.widgets.CommTopTitleLayout>

    <WebView
        android:id="@+id/notification_message_detail_data_WebView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</LinearLayout>