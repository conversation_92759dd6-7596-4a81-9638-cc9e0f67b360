<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?theme_comm_click_bg"
    android:foreground="?selectableItemBackground"
    android:orientation="vertical"
    android:paddingTop="10dp"
    android:paddingBottom="10dp">

    <TextView
        android:id="@+id/deeplink_name_TextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:minHeight="20dp"
        android:paddingLeft="15dp"
        android:text="显示栏目列表"
        android:textColor="?theme_text_color_black"
        android:textSize="16sp" />

    <EditText
        android:id="@+id/deeplink_url_EditText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/transparent"
        android:gravity="center_vertical"
        android:minHeight="30dp"
        android:paddingLeft="15dp"
        android:text="wdbook://store/widgets/free_books"
        android:textColor="?theme_text_color_blue"
        android:textSize="16sp" />
</LinearLayout>