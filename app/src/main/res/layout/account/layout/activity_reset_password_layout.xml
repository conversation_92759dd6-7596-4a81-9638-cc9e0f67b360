<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/white">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/reset_password_top_title_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/top_title_height"
        android:layout_alignParentTop="true"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="@string/title_reset_password"
        app:layout_constraintTop_toTopOf="parent" />

    <EditText
        android:id="@+id/reset_password_mail_EditText"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_centerHorizontal="true"
        android:layout_marginLeft="25dp"
        android:layout_marginRight="25dp"
        android:background="@drawable/selector_edit_text_stoke_bg"
        android:gravity="center_vertical"
        android:hint="@string/hint_input_email"

        android:inputType="textEmailAddress"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:singleLine="true"
        android:textSize="16sp"
        app:layout_constraintBottom_toTopOf="@id/reset_password_reset_Button"
        app:layout_constraintTop_toBottomOf="@id/reset_password_top_title_layout"
        app:layout_constraintVertical_bias="0.35"
        app:layout_constraintVertical_chainStyle="packed" />


    <Button
        android:id="@+id/reset_password_reset_Button"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginLeft="25dp"
        android:layout_marginTop="30dp"
        android:layout_marginRight="25dp"
        android:background="@drawable/selector_button_bg_blue"
        android:text="@string/reset_password"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/reset_password_mail_EditText" />


</androidx.constraintlayout.widget.ConstraintLayout>