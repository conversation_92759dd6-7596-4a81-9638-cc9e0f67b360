<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_marginTop="8dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?theme_bg_color_white"
    android:foreground="?android:attr/selectableItemBackground"
    android:orientation="vertical">

    <TextView
        android:id="@+id/order_list_name_TextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="13dp"
        android:layout_marginBottom="13dp"
        android:gravity="center_vertical"
        android:paddingLeft="16dp"
        android:text="@string/order_time"
        android:textColor="?theme_text_color_black_373636"
        android:textSize="16sp"
        android:textStyle="bold" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.7dp"
        android:layout_marginBottom="12dp"
        android:background="?theme_bg_color_divide_line_color" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/order_list_single_Layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="24dp"
        android:paddingRight="24dp">

        <com.aquila.lib.widget.view.AdaptiveImageView
            android:id="@+id/item_book_info_cover_ImageView"
            android:layout_width="64dp"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:background="@drawable/shape_book_corners_bg"
            android:scaleType="fitStart"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/demo_03" />

        <TextView
            android:id="@+id/item_book_info_name_TextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintHorizontal_weight="1"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="24dp"
            android:singleLine="true"
            android:textColor="?theme_text_color_black_373636"
            android:textSize="16sp"
            app:layout_constraintLeft_toRightOf="@id/item_book_info_cover_ImageView"
            app:layout_constraintRight_toLeftOf="@id/item_order_money_TextView"
            app:layout_constraintTop_toTopOf="@id/item_book_info_cover_ImageView"
            tools:text="精灵宝钻(精装插图本)" />

        <TextView
            android:id="@+id/item_order_author_TextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:ellipsize="end"
            android:paddingTop="0dp"
            android:paddingBottom="0dp"
            android:singleLine="true"
            android:textColor="?theme_text_color_gray"
            android:textSize="12sp"
            app:layout_constraintLeft_toLeftOf="@id/item_book_info_name_TextView"
            app:layout_constraintRight_toRightOf="@id/item_book_info_name_TextView"
            app:layout_constraintTop_toBottomOf="@id/item_book_info_name_TextView"
            tools:text="J.R.R.托尔金\nJ.R.R.托尔金J.R.R.托尔金J.R.R.托尔金托尔金托尔金托尔金托尔金托尔金" />

        <TextView
            android:id="@+id/item_order_copyright_TextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:singleLine="true"
            android:textColor="?theme_text_color_gray"
            android:textSize="12sp"
            app:layout_constraintLeft_toLeftOf="@id/item_book_info_name_TextView"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/item_order_author_TextView"
            tools:text="出版社" />

        <TextView
            android:id="@+id/item_order_money_TextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center_vertical|right"
            android:maxLines="1"
            android:singleLine="true"
            android:textColor="?theme_text_color_black_373636"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="@id/item_book_info_name_TextView"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintLeft_toRightOf="@id/item_book_info_name_TextView"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/item_book_info_name_TextView"
            tools:text="$6" />

        <TextView
            android:id="@+id/item_order_number_one_TextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:ellipsize="end"
            android:gravity="center_vertical|right"
            android:maxLines="1"
            android:singleLine="true"
            android:textColor="?theme_text_color_black_4e"
            android:textSize="14sp"
            app:layout_constraintLeft_toLeftOf="@id/item_order_money_TextView"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/item_order_money_TextView"
            tools:text="@string/sum_items" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/order_list_multiple_Layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">
        <com.aquila.lib.widget.view.CustomRecyclerView
            android:id="@+id/item_order_list_RecyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:paddingLeft="24dp"
            app:attr_end_item_margin="12dp"
            app:attr_item_margin="16dp"
            app:attr_layout_style="list_horizontal"
            app:layout_constraintTop_toBottomOf="@id/micro_recommend_title_TextView"
            tools:itemCount="4"
            tools:listitem="@layout/holder_item_order_book_list_layout" />

        <TextView
            android:id="@+id/item_order_number_TextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:ellipsize="end"
            android:layout_marginRight="16dp"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:singleLine="true"
            android:drawableRight="@drawable/ic_arrow_right_gray"
            android:textColor="?theme_text_color_black_4e"
            android:textSize="14sp"
            android:layout_marginLeft="24dp"
            android:drawablePadding="7.5dp"
            app:layout_constraintLeft_toLeftOf="@id/item_order_money_TextView"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/item_order_money_TextView"
            android:text="共6件" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_marginTop="12dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="16dp"
        android:paddingRight="16dp">

        <Button
            android:id="@+id/item_order_get_receipt_Button"
            android:layout_width="wrap_content"
            android:layout_height="34dp"
            android:background="?theme_white_bg_gray_stoke_conner"
            android:gravity="center"
            android:text="@string/get_record"
            android:textColor="?theme_text_color_black_373636"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/item_order_actual_pay_TextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="center_vertical|right"
            android:maxLines="1"
            android:singleLine="true"
            android:textColor="?theme_text_color_black_373636"
            android:textSize="16sp"
            android:textStyle="bold"
            tools:text="实付款：" />
    </LinearLayout>
</LinearLayout>