<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/sso_root_RelativeLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerInParent="true"
        android:background="?theme_bg_white_conner_8dp"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:gravity="center"
            android:text="@string/not_login"
            android:textColor="?attr/theme_text_color_black"
            android:textSize="24sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="50dp"
            android:layout_marginTop="16dp"
            android:layout_marginRight="50dp"
            android:gravity="center"
            android:text="@string/after_login_tip"
            android:textColor="?attr/theme_text_color_gray"
            android:textSize="18sp" />

        <Button
            android:id="@+id/sso_register_Button"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_marginLeft="24dp"
            android:layout_marginTop="55dp"
            android:layout_marginRight="24dp"
            android:background="@drawable/selector_all_conner_brown_stroke_bg"
            android:text="@string/not_account_please_regester"
            android:textColor="@color/color_orange_FF8A00"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <Button
            android:id="@+id/book_login_Button"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_marginLeft="24dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="24dp"
            android:layout_marginBottom="20dp"
            android:background="@drawable/selector_all_conner_brown_bg"
            android:text="@string/have_account_please_login"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/sso_login_LinearLayout"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_marginLeft="24dp"
            android:layout_marginRight="24dp"
            android:layout_marginBottom="40dp"
            android:background="@drawable/selector_all_conner_brown_stroke_bg"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="10dp"
                android:src="@drawable/ic_wd_logo" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="@string/wd_bible_auth_login"
                android:textColor="?attr/theme_text_color_black"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>