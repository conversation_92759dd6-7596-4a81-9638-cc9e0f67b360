<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_root_view_bg"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/order_detail_top_TitleLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/top_title_height"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="@string/order_detail_title" >
        <TextView
            android:id="@+id/order_detail_get_record_TextView"
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            android:layout_alignParentRight="true"
            android:background="?theme_comm_click_bg"
            android:foreground="?selectableItemBackground"
            android:gravity="center"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:text="@string/get_record"
            android:textColor="?theme_text_color_black"
            android:textSize="14sp" />
    </com.wedevote.wdbook.ui.widgets.CommTopTitleLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <com.aquila.lib.widget.view.CustomRecyclerView
                android:id="@+id/order_detail_data_RecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?theme_bg_color_white"
                app:attr_layout_style="list_vertical"
                tools:itemCount="2"
                tools:listitem="@layout/holder_order_detail_item_layout" />


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="?theme_bg_color_white"
                android:paddingLeft="24dp"
                android:paddingTop="16dp"
                android:paddingRight="24dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="5dp"
                    android:gravity="right|center_vertical"
                    android:text="@string/order_time"
                    android:textColor="@color/text_color_gray_99"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/item_detail_value_one_TextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="8dp"
                    android:gravity="center_vertical"
                    android:textColor="?theme_text_color_black_373636"
                    android:textSize="14sp"
                    android:textStyle="bold"


                    tools:text="购买《丁道尔注释》" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?theme_bg_color_white"
                android:paddingBottom="8dp"
                android:paddingLeft="24dp"
                android:paddingTop="8dp"
                android:paddingRight="24dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="5dp"
                    android:gravity="right|center_vertical"
                    android:text="@string/order_number"
                    android:textColor="@color/text_color_gray_99"
                    android:textSize="14sp"
                    />

                <TextView
                    android:id="@+id/item_detail_value_two_TextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="8dp"
                    android:gravity="center_vertical"
                    android:textColor="?theme_text_color_black_373636"
                    android:textSize="14sp"
                    android:textStyle="bold"


                    tools:text="购买《丁道尔注释》" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/item_detail_value_three_LinearLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?theme_bg_color_white"
                android:paddingLeft="24dp"

                android:paddingRight="24dp"
                android:paddingBottom="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="5dp"
                    android:gravity="right|center_vertical"
                    android:text="@string/pay_method"
                    android:textColor="@color/text_color_gray_99"
                    android:textSize="14sp"


                    />

                <TextView
                    android:id="@+id/item_detail_value_three_TextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="8dp"
                    android:gravity="center_vertical"
                    android:textColor="?theme_text_color_black_373636"
                    android:textSize="14sp"
                    android:textStyle="bold"


                    tools:text="购买《丁道尔注释》" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="?theme_bg_color_white"
                android:paddingLeft="24dp"
                android:paddingTop="16dp"
                android:paddingRight="24dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/total_products_amount"
                    android:textColor="@color/text_color_gray_99"
                    android:textSize="14sp"
                    />

                <TextView
                    android:id="@+id/item_detail_value_fore_TextView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="right"
                    android:textColor="?theme_text_color_black_373636"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    tools:text="购买《丁道尔注释》" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/item_detail_value_five_LinearLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?theme_bg_color_white"
                android:paddingLeft="24dp"
                android:paddingTop="8dp"
                android:paddingRight="24dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/paid_books_amount"
                    android:textColor="@color/text_color_gray_99"
                    android:textSize="14sp"
                    />

                <TextView
                    android:id="@+id/item_detail_value_five_TextView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="right"
                    android:textColor="?theme_text_color_black_373636"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    tools:text="购买《丁道尔注释》" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/item_detail_value_six_LinearLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?theme_bg_color_white"
                android:paddingLeft="24dp"
                android:paddingTop="8dp"
                android:paddingRight="24dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/cupon_amount"
                    android:textColor="@color/text_color_gray_99"
                    android:textSize="14sp"
                    />

                <TextView
                    android:id="@+id/item_detail_value_six_TextView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="right"
                    android:textColor="?theme_text_color_black_373636"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    tools:text="购买《丁道尔注释》" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/item_detail_value_seven_LinearLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?theme_bg_color_white"
                android:paddingLeft="24dp"
                android:paddingTop="8dp"
                android:paddingRight="24dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/gift_card_amount"
                    android:textColor="@color/text_color_gray_99"
                    android:textSize="14sp"
                    />

                <TextView
                    android:id="@+id/item_detail_value_seven_TextView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="right"
                    android:textColor="?theme_text_color_black_373636"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    tools:text="购买《丁道尔注释》" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/item_detail_value_eight_LinearLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?theme_bg_color_white"
                android:paddingLeft="24dp"
                android:paddingTop="8dp"
                android:paddingRight="24dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/account_balance"
                    android:textColor="@color/text_color_gray_99"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/item_detail_value_eight_TextView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="right"
                    android:textColor="?theme_text_color_black_373636"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    tools:text="购买《丁道尔注释" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?theme_bg_color_white"
                android:paddingLeft="24dp"
                android:paddingTop="8dp"
                android:paddingRight="24dp"
                android:paddingBottom="17dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="right"
                    android:text="@string/actual_pay_money"
                    android:textColor="?theme_text_color_black_373636"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/item_detail_actual_pay_TextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="8dp"
                    android:textColor="?theme_text_color_red"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    tools:text="$8" />
            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="40dp" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

</LinearLayout>