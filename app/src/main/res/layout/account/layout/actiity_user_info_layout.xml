<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/user_info_top_title_Layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/top_title_height"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="@string/title_user_info" />

    <com.aquila.lib.widget.view.ElasticScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:divider="@drawable/shape_divide_line_horizontal"
                android:orientation="vertical"
                android:showDividers="middle">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="15dp"
                    android:paddingTop="10dp"
                    android:paddingRight="15dp"
                    android:paddingBottom="10dp">

                    <TextView
                        style="@style/style_user_info_label"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:text="@string/avatar"
                        android:textColor="@color/text_color_black_000"
                        android:textSize="16sp" />

                    <ImageView
                        android:id="@+id/user_info_avatar_ImageView"
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:layout_alignParentRight="true"
                        android:layout_marginRight="15dp"
                        android:src="@drawable/ic_default_avatar" />
                </RelativeLayout>

                <com.aquila.lib.widget.group.GroupSideAlignLayout
                    android:id="@+id/user_info_nickname_Layout"
                    style="@style/style_user_info_label"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    app:attr_left_text="@string/label_nickname"
                    app:attr_left_text_color="@color/text_color_black_000"
                    app:attr_right_text="这个是昵称" />

                <com.aquila.lib.widget.group.GroupSideAlignLayout
                    android:id="@+id/user_info_email_address_Layout"
                    style="@style/style_user_info_label"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    app:attr_left_text="@string/label_email_address"
                    app:attr_left_text_color="@color/text_color_black_000"
                    app:attr_right_text="<EMAIL>" />

                <com.aquila.lib.widget.group.GroupSideAlignLayout
                    android:id="@+id/user_info_phone_number_Layout"
                    style="@style/style_user_info_label"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    app:attr_left_text="@string/label_phone_number"
                    app:attr_left_text_color="@color/text_color_black_000"
                    app:attr_right_text="18913102035" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:background="@color/white"
                android:divider="@drawable/shape_divide_line_horizontal"
                android:orientation="vertical"

                android:showDividers="middle">

                <com.wedevote.wdbook.ui.widgets.WidgetMineItemLayout
                    style="@style/style_user_info_label"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    android:id="@+id/user_info_gender_Layout"
                    app:attr_data_text="女"
                    app:attr_show_image="false"
                    app:attr_title_text="@string/label_gender" />

                <com.wedevote.wdbook.ui.widgets.WidgetMineItemLayout
                    style="@style/style_user_info_label"
                    android:id="@+id/user_info_age_Layout"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    app:attr_data_text="20-30岁"
                    app:attr_show_image="false"
                    app:attr_title_text="@string/label_age" />

                <com.wedevote.wdbook.ui.widgets.WidgetMineItemLayout
                    style="@style/style_user_info_label"
                    android:layout_width="match_parent"
                    android:id="@+id/user_info_belief_age_Layout"
                    android:layout_height="50dp"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    app:attr_data_text="10年"
                    app:attr_show_image="false"
                    app:attr_title_text="@string/label_faith_time" />
            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:background="@color/white"
                android:orientation="vertical">

                <com.wedevote.wdbook.ui.widgets.WidgetMineItemLayout
                    android:id="@+id/user_info_change_password_Layout"
                    style="@style/style_user_info_label"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    app:attr_data_text=""
                    app:attr_show_image="false"
                    app:attr_title_text="@string/label_change_password" />
            </LinearLayout>
        </LinearLayout>
    </com.aquila.lib.widget.view.ElasticScrollView>
</LinearLayout>