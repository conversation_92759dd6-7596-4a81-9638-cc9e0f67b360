<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/order_list_single_Layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="12dp"
    android:paddingBottom="12dp"
    android:paddingLeft="24dp"
    android:paddingRight="24dp"
    >

    <com.aquila.lib.widget.view.AdaptiveImageView
        android:id="@+id/item_book_info_cover_ImageView"
        android:layout_width="64dp"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        android:background="@drawable/shape_book_corners_bg"
        android:scaleType="fitStart"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/demo_03" />

    <TextView
        android:id="@+id/item_book_info_name_TextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintHorizontal_weight="1"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="24dp"
        android:singleLine="true"
        android:textColor="?theme_text_color_black_373636"
        android:textSize="16sp"
        app:layout_constraintLeft_toRightOf="@id/item_book_info_cover_ImageView"
        app:layout_constraintRight_toLeftOf="@id/item_order_money_TextView"
        app:layout_constraintTop_toTopOf="@id/item_book_info_cover_ImageView"
        tools:text="精灵宝钻(精装插图本)" />

    <TextView
        android:id="@+id/item_order_author_TextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:ellipsize="end"
        android:paddingTop="0dp"
        android:paddingBottom="0dp"
        android:singleLine="true"
        android:textColor="?theme_text_color_gray"
        android:textSize="12sp"
        app:layout_constraintLeft_toLeftOf="@id/item_book_info_name_TextView"
        app:layout_constraintRight_toRightOf="@id/item_book_info_name_TextView"
        app:layout_constraintTop_toBottomOf="@id/item_book_info_name_TextView"
        tools:text="J.R.R.托尔金\nJ.R.R.托尔金J.R.R.托尔金J.R.R.托尔金托尔金托尔金托尔金托尔金托尔金" />

    <TextView
        android:id="@+id/item_order_copyright_TextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="?theme_text_color_gray"
        android:textSize="12sp"
        app:layout_constraintLeft_toLeftOf="@id/item_book_info_name_TextView"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/item_order_author_TextView"
        tools:text="出版社" />

    <TextView
        android:id="@+id/item_order_money_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center_vertical|right"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="?theme_text_color_black_373636"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="@id/item_book_info_name_TextView"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintLeft_toRightOf="@id/item_book_info_name_TextView"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/item_book_info_name_TextView"
        tools:text="$6" />

    <TextView
        android:id="@+id/item_order_number_one_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:ellipsize="end"
        android:gravity="center_vertical|right"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="?theme_text_color_gray_BC_88"
        android:textSize="14sp"
        app:layout_constraintLeft_toLeftOf="@id/item_order_money_TextView"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/item_order_money_TextView"
        tools:text="@string/sum_items" />

</androidx.constraintlayout.widget.ConstraintLayout>
