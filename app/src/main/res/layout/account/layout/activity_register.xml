<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_root_view_bg"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/login_indicator_layout"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="?theme_bg_color_white_bottom_line">


        <ImageView
            android:id="@+id/register_back_View"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:padding="8dp"
            android:background="@drawable/selector_item_click_circle_bg"
            android:foreground="?android:attr/selectableItemBackground"
            app:tint="?theme_icon_tint_color"
            tools:ignore="UseAppTint"
            android:src="@drawable/ic_in_book_left_arrow"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/register_login_title_TextView"
            style="@style/style_title_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true" />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:paddingTop="10dp">

        <FrameLayout
            android:id="@+id/register_content_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"></FrameLayout>

        <LinearLayout
            android:id="@+id/register_user_agree_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="21dp"
            android:layout_marginTop="21dp"
            android:orientation="horizontal">

            <CheckBox
                android:id="@+id/register_use_agree_CheckBox"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="5dp"
                android:button="?theme_checkBox"
                android:gravity="center" />

            <TextView
                android:id="@+id/register_use_description_TextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:text="已阅读并同意"
                android:textColor="?theme_text_color_gray"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/register_use_protocol_TextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:text="@string/text_user_agreement"
                android:textColor="@color/color_blue_006FFF"
                android:textSize="16sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="3dp"
                android:layout_marginRight="3dp"
                android:text="&amp;"
                android:textColor="?theme_text_color_gray"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/register_use_privacy_TextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/text_privacy_agreement"
                android:textColor="@color/color_blue_006FFF"
                android:textSize="16sp" />
        </LinearLayout>

        <TextView
            android:id="@+id/register_next_TextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="16dp"
            android:enabled="false"
            android:background="@drawable/selector_all_conner_brown_bg"
            android:gravity="center_horizontal"
            android:padding="13dp"
            android:text="Next"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/register_tip_TextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/account_can_login_bible_and_book"
            android:textColor="@color/text_color_black_BC"
            android:textSize="14sp" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="bottom"
            android:paddingBottom="44dp">

            <LinearLayout
                android:id="@+id/register_type_change_Layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="20dp"
                android:gravity="center_horizontal"
                android:background="?theme_shape_corner_big"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/register_type_change_ImageView"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="16dp"
                    android:src="?theme_ic_user_phone" />

                <TextView
                    android:id="@+id/register_type_change_TextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="15dp"
                    android:textColor="?theme_text_color_black_373636"
                    android:textSize="16sp"
                    tools:text="@string/use_email_register" />
            </LinearLayout>
        </RelativeLayout>
    </LinearLayout>
</LinearLayout>