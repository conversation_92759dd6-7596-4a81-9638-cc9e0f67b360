<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_root_view_bg"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/login_top_title_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_height"
        android:background="?theme_bg_color_white_bottom_line" />

    <TextView
        android:id="@+id/login_account_title_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="30dp"
        android:layout_marginBottom="6dp"
        android:text="@string/phone_number"
        android:textSize="14sp" />

    <LinearLayout
        android:id="@+id/phone_email_LinearLayout"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginLeft="21dp"
        android:layout_marginRight="21dp"
        android:background="?theme_shape_corner_gray"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/activity_horizontal_margin">

        <TextView
            android:id="@+id/login_country_code_TextView"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:paddingRight="17dp"
            android:text="+86"
            android:textColor="@color/color_orange_FF8A00"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/login_country_divide_TextView"
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:layout_marginRight="17dp"
            android:background="?theme_bg_color_divide_line_color_login" />

        <EditText
            android:id="@+id/login_account_EditText"
            android:layout_width="match_parent"
            android:layout_height="@dimen/title_height"
            android:layout_weight="1"
            android:background="@color/transparent"
            android:cursorVisible="true"
            android:gravity="center_vertical"
            android:inputType="textEmailAddress"
            android:maxLength="64"
            android:maxLines="1"
            android:paddingRight="@dimen/activity_horizontal_margin"
            android:textColor="?theme_text_color_black_373636"
            android:textColorHint="?theme_text_color_gray_BC_88"
            android:textCursorDrawable="@null"
            android:textSize="@dimen/text_size_16" />

        <ImageView
            android:id="@+id/error_tip_ImageView"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="16dp"
            android:adjustViewBounds="true"
            android:scaleType="fitXY"
            android:src="@drawable/selector_login_error_tip"
            android:visibility="gone"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/clear_text_ImageView"
            android:layout_width="52dp"
            android:layout_height="match_parent"
            android:adjustViewBounds="true"
            android:paddingLeft="16dp"
            android:paddingRight="16dp"
            android:scaleType="fitCenter"
            android:src="@drawable/ic_clear_text"
            android:visibility="gone"
            tools:visibility="visible" />
    </LinearLayout>

    <TextView
        android:id="@+id/phone_error_tip_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="6dp"
        android:text="@string/login_input_right_email_tip"
        android:textColor="@color/color_red_FF342A"
        android:textSize="14sp"
        android:visibility="gone"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/login_password_title_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="6dp"
        android:text="@string/password"
        android:textSize="14sp" />

    <RelativeLayout
        android:id="@+id/login_password_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_height"
        android:layout_marginLeft="21dp"
        android:layout_marginRight="21dp"
        android:background="?theme_shape_corner_gray">

        <EditText
            android:id="@+id/login_password_EditText"
            android:layout_width="match_parent"
            android:layout_height="@dimen/title_height"
            android:background="@color/transparent"
            android:cursorVisible="true"
            android:gravity="center_vertical"
            android:hint="@string/please_input_passward"
            android:maxLines="1"
            android:paddingLeft="@dimen/activity_horizontal_margin"
            android:paddingRight="@dimen/activity_horizontal_margin"
            android:textColor="?theme_text_color_black_373636"
            android:textColorHint="?theme_text_color_gray_BC_88"
            android:textCursorDrawable="@null"
            android:textSize="@dimen/text_size_16" />

        <ImageView
            android:id="@+id/login_password_ImageView"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:enabled="true"
            android:gravity="center"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:src="@drawable/selector_eye_ic" />

    </RelativeLayout>

    <TextView
        android:id="@+id/login_forget_password_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="right"
        android:layout_marginTop="5dp"
        android:layout_marginRight="10dp"
        android:gravity="right"
        android:padding="10dp"
        android:text="forget password"
        android:textColor="@color/color_blue_006FFF"
        android:textSize="@dimen/text_size_14" />

    <TextView
        android:id="@+id/login_login_TextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="20dp"
        android:background="@drawable/selector_all_conner_brown_bg"
        android:enabled="false"
        android:gravity="center_horizontal"
        android:padding="13dp"
        android:text="@string/label_login"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:textSize="16sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="bottom"
        android:orientation="vertical"
        android:paddingBottom="40dp">

        <LinearLayout
            android:id="@+id/login_mail_Layout"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="20dp"
            android:background="?theme_shape_corner_big"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="?theme_ic_user_phone" />

            <TextView
                android:id="@+id/login_switch_TextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="16dp"
                android:textColor="?theme_text_color_black_373636"
                android:textSize="16sp"
                tools:text="使用邮箱登录" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/sso_login_LinearLayout"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:background="?theme_shape_corner_big"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="16dp"
                android:src="@drawable/ic_wd_logo" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="@string/wd_bible_auth_login"
                android:textColor="?attr/theme_text_color_black_373636"
                android:textSize="16sp" />

        </LinearLayout>
    </LinearLayout>
</LinearLayout>