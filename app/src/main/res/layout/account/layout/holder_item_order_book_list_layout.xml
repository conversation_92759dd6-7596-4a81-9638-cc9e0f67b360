<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <com.aquila.lib.widget.view.AdaptiveImageView
        android:id="@+id/item_order_book_list_ImageView"
        android:layout_width="56dp"
        android:layout_height="wrap_content"
        android:src="@drawable/demo_02"
        app:attr_aspect_ratio="196,275"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>