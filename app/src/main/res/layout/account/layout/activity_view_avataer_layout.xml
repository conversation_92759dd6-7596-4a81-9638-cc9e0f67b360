<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp">

        <ImageView
            android:id="@+id/avatar_back_ImageView"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:background="@drawable/selector_comm_click_bg"
            android:foreground="?android:attr/selectableItemBackground"
            android:scaleType="center"
            android:src="@drawable/ic_back_white" />

        <TextView
            android:id="@+id/avatar_title_TextView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            android:layout_toLeftOf="@id/avatar_more_ImageView"
            android:layout_toRightOf="@id/avatar_back_ImageView"
            android:gravity="center"
            android:text="@string/personal_avatar"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <ImageView
            android:id="@+id/avatar_more_ImageView"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_alignParentRight="true"
            android:background="@drawable/selector_comm_click_bg"
            android:foreground="?android:attr/selectableItemBackground"
            android:scaleType="center"
            android:src="@drawable/ic_more_white" />
    </RelativeLayout>

    <ImageView
        android:id="@+id/avatar_img_ImageView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitCenter"
        android:src="@drawable/ic_default_avatar" />


</LinearLayout>