<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_root_view_bg"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/login_indicator_layout"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="?theme_bg_color_white_bottom_line">

        <ImageView
            android:id="@+id/register_back_View"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:padding="8dp"
            android:background="@drawable/selector_item_click_circle_bg"
            android:foreground="?android:attr/selectableItemBackground"
            app:tint="?theme_icon_tint_color"
            tools:ignore="UseAppTint"
            android:src="@drawable/ic_in_book_left_arrow"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/register_login_title_TextView"
            style="@style/style_title_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true" />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:paddingTop="20dp">

        <TextView
            android:id="@+id/register_phone_title_TextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginBottom="6dp"
            android:text="@string/old_password"
            android:textSize="14sp" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="21dp"
            android:layout_marginRight="21dp">

            <EditText
                android:id="@+id/set_password_old_EditText"
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_height"
                android:cursorVisible="true"
                android:gravity="center_vertical"
                android:maxLines="1"
                android:maxLength="64"
                android:paddingStart="16dp"
                android:inputType="textPassword"
                android:hint="@string/please_input_old_password"
                android:paddingEnd="50dp"
                android:background="?theme_shape_corner_gray"
                android:textColor="?theme_text_color_black_373636"
                android:textColorHint="?theme_text_color_gray_BC_88"
                android:textCursorDrawable="@null"
                android:textSize="@dimen/text_size_16" />

            <ImageView
                android:id="@+id/set_password_show_ImageView"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/title_height"
                android:layout_alignParentEnd="true"
                android:enabled="true"
                android:gravity="center"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:src="@drawable/selector_eye_ic" />

            <TextView
                android:id="@+id/password_error_tip_TextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:layout_marginBottom="6dp"
                android:textColor="@color/color_red_FF342A"
                android:visibility="gone"
                android:layout_below="@id/set_password_show_ImageView"
                tools:visibility="visible"
                tools:text="@string/login_input_right_email_tip"
                android:textSize="14sp" />

        </RelativeLayout>

        <FrameLayout
            android:id="@+id/register_content_layout"
            android:layout_marginTop="20dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/register_next_TextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="16dp"
            android:enabled="false"
            android:background="@drawable/selector_all_conner_brown_bg"
            android:gravity="center_horizontal"
            android:padding="13dp"
            android:text="Next"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="16sp" />

    </LinearLayout>
</LinearLayout>