<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="44dp"
    android:background="?theme_bg_color_white"
    android:orientation="horizontal">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@drawable/selector_contents_listitem_bg">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/code_item_number_TextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="24dp"
                android:maxLines="1"
                android:text="+86"
                android:textColor="@color/color_orange_FF8A00"
                android:textSize="14sp" />
        </LinearLayout>

        <TextView
            android:id="@+id/code_item_name_TextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:ellipsize="end"
            android:maxLines="1"
            android:paddingLeft="24dp"
            android:text="中国"
            android:textColor="?theme_text_color_black_373636"
            android:textSize="18sp" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:background="?theme_bg_color_divide_line_color" />
</RelativeLayout>