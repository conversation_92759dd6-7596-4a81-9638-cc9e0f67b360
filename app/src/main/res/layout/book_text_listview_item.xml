<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@color/transparent"
    android:paddingLeft="0dp"
    android:paddingRight="25dp"
    android:paddingTop="0dp"
    android:paddingBottom="0dp">

    <com.wedevote.wdbook.ui.read.lib.view.TextLineView
        android:id="@+id/book_text_line_context_TextLineView"
        android:background="@color/transparent"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:paddingTop="0dp"
        android:paddingBottom="0dp"
        />
</LinearLayout>
