<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="#c000"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="#111">

        <ImageView
            android:id="@+id/full_image_back_ImageView"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_alignParentRight="true"
            android:background="?theme_comm_click_bg"
            android:foreground="?android:attr/selectableItemBackground"
            android:scaleType="center"
            android:src="@drawable/ic_clear" />
    </RelativeLayout>

    <com.wedevote.wdbook.ui.read.FullScaleView
        android:id="@+id/full_size_ImageView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        />
</LinearLayout>
