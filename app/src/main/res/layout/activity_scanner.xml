<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/qr_scanner_title_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_height"
        android:background="?theme_bg_color_white_bottom_line">

        <TextView
            android:id="@+id/scan_album_TextView"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:gravity="center"
            android:paddingRight="10dp"
            android:text="@string/label_album"
            android:textColor="?theme_text_color_black"
            android:textSize="@dimen/text_size_16" />

    </com.wedevote.wdbook.ui.widgets.CommTopTitleLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <SurfaceView
            android:id="@+id/scanner_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center" />

        <com.wedevote.wdbook.tools.zxing.view.ViewfinderView
            android:id="@+id/viewfinder_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
    </FrameLayout>

</LinearLayout>