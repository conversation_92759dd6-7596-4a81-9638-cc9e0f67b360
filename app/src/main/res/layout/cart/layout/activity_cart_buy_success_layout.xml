<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_root_view_bg"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/cart_buy_success_top_title_Layout"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="?theme_bg_color_white_bottom_line" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?theme_bg_color_white"
        android:padding="35dp">

        <ImageView
            android:id="@+id/cart_buy_success_flag_ImageView"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:scaleType="fitCenter"
            android:src="@drawable/ic_check_box_checked_yellow"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/cart_buy_success_label_TextView"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/cart_buy_success_label_TextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:text="购买成功"
            android:textColor="?theme_text_color_black"
            android:textSize="24sp"
            app:layout_constraintBottom_toBottomOf="@id/cart_buy_success_flag_ImageView"
            app:layout_constraintLeft_toRightOf="@id/cart_buy_success_flag_ImageView"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/cart_buy_success_flag_ImageView" />

        <TextView
            android:id="@+id/cart_buy_success_prompt_TextView"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="10dp"
            android:gravity="center"
            android:text="感谢您使用在线购买功能！"
            android:textColor="?theme_text_color_gray"
            android:textSize="16sp"
            app:layout_constrainedWidth="true"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cart_buy_success_flag_ImageView" />

        <Button
            android:id="@+id/cart_buy_success_go_back_home_Button"
            android:layout_width="115dp"
            android:layout_height="40dp"
            android:layout_marginTop="30dp"
            android:background="@drawable/selector_download_btn_orange_bg"
            android:text="返回书城"
            android:textColor="?theme_text_color_orange"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/cart_buy_success_go_shelf_Button"
            app:layout_constraintTop_toBottomOf="@id/cart_buy_success_prompt_TextView" />

        <Button
            android:id="@+id/cart_buy_success_go_shelf_Button"
            android:layout_width="115dp"
            android:layout_height="40dp"
            android:layout_marginTop="30dp"
            android:background="?theme_bg_white_orange_btn"
            android:text="去书架看书"
            android:textColor="?theme_text_color_white"
            app:layout_constraintLeft_toRightOf="@id/cart_buy_success_go_back_home_Button"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cart_buy_success_prompt_TextView" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="10dp"
        android:background="?theme_bg_color_white"
        android:orientation="vertical"
        android:paddingLeft="0dp"
        android:paddingTop="15dp"
        android:paddingRight="0dp"
        android:paddingBottom="15dp">

        <TextView
            android:id="@+id/cart_buy_success_recommend__TextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"

            android:text="您可能会喜欢这些书籍"
            android:textColor="?theme_text_color_black"
            android:textSize="18sp"
            android:textStyle="bold" />

        <com.aquila.lib.widget.view.CustomRecyclerView
            android:id="@+id/cart_buy_success_data_RecyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="10dp"
            app:attr_divide_top_margin="5dp"
            app:attr_item_decoration_type="ITEM_SPACE"
            app:attr_layout_style="grid_vertical"
            app:attr_span_count="3"
            tools:listitem="@layout/holder_cart_recommend_layout" />


    </LinearLayout>


</LinearLayout>