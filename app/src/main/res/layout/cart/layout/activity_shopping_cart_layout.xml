<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_bg_color_white"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/cart_top_title_layout"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="购物车" />

    <RelativeLayout
        android:id="@+id/cart_data_container_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/cart_top_title_layout"
        android:visibility="visible"
        tools:visibility="visible">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cart_bottom_container_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:background="?theme_bg_color_white_top_line"
            android:paddingLeft="10dp"
            android:paddingTop="15dp"
            android:paddingRight="15dp"
            android:paddingBottom="15dp">

            <com.aquila.lib.widget.group.GroupImageTextLayout
                android:id="@+id/cart_select_all_check_Layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="5dp"
                android:paddingTop="10dp"
                android:paddingRight="5dp"
                android:paddingBottom="10dp"
                app:attr_image_height="20dp"
                app:attr_image_src="@drawable/selected_favorite_check_box_yellow"
                app:attr_image_width="20dp"
                app:attr_is_selected="false"
                app:attr_parent_orientation="IMAGE_LEFT_TEXT_RIGHT"
                app:attr_scale_type="FIT_CENTER"
                app:attr_text="全选"
                app:attr_text_color="?theme_text_color_black"
                app:attr_text_margin_image_size="10dp"
                app:attr_text_size="16sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <Button
                android:id="@+id/cart_payment_Button"
                android:layout_width="100dp"
                android:layout_height="40dp"
                android:background="?theme_bg_white_orange_btn"
                android:text="结算"
                android:textColor="?theme_text_color_white"
                android:textSize="16sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="结算(8)" />

            <TextView
                android:id="@+id/cart_amount_price_TextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="10dp"
                android:gravity="right"
                android:text=""
                android:textColor="?theme_text_color_red"
                android:textSize="16sp"
                app:layout_constraintBottom_toTopOf="@id/cart_amount_discount_detail_TextView"
                app:layout_constraintRight_toLeftOf="@id/cart_payment_Button"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="$23.3" />

            <TextView
                android:id="@+id/cart_amount_label_TextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="10dp"
                android:text="合计:"
                android:textColor="?theme_text_color_black"
                android:textSize="16sp"
                app:layout_constraintRight_toLeftOf="@id/cart_amount_price_TextView"
                app:layout_constraintTop_toTopOf="parent" />


            <TextView
                android:id="@+id/cart_amount_discount_detail_TextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:background="?theme_shape_gray_conner_8_bg"
                android:padding="5dp"
                android:text="优惠明细"
                android:textColor="?theme_text_color_black"
                android:textSize="10sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="@id/cart_amount_price_TextView"
                app:layout_constraintTop_toBottomOf="@id/cart_amount_price_TextView" />


            <TextView
                android:id="@+id/cart_amount_has_discount_price_TextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="10dp"
                android:padding="5dp"
                android:textColor="?theme_text_color_black"
                android:textSize="10sp"
                app:layout_constraintBaseline_toBaselineOf="@id/cart_amount_discount_detail_TextView"
                app:layout_constraintRight_toLeftOf="@id/cart_amount_price_TextView"
                tools:text="已优惠:$23.1" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.aquila.lib.layout.SmartRefreshLayout
            android:id="@+id/cart_item_list_RefreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@id/cart_bottom_container_layout"
            app:srlEnableLoadMore="false">

            <com.aquila.lib.widget.view.CustomRecyclerView
                android:id="@+id/cart_item_RecyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:attr_item_decoration_type="ITEM_DIVIDE_LNE"
                app:attr_item_divide_line_color="?theme_bg_color_divide_line_color"
                app:attr_item_margin="10dp"
                tools:itemCount="10"
                tools:listitem="@layout/holder_cart_book_item_layout" />
        </com.aquila.lib.layout.SmartRefreshLayout>
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/cart_empty_container_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/cart_top_title_layout"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:paddingTop="180dp"
        android:visibility="gone"
        tools:visibility="gone">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:scaleType="center"
            android:src="@drawable/ic_cart_empty" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="45dp"
            android:text="购物车为空"
            android:textColor="?theme_text_color_black"
            android:textSize="16sp" />

        <Button
            android:id="@+id/cart_go_back_Button"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_marginLeft="30dp"
            android:layout_marginTop="45dp"
            android:layout_marginRight="30dp"
            android:layout_marginBottom="50dp"
            android:background="?theme_bg_white_orange_btn"
            android:text="返回书城逛逛"
            android:textColor="?theme_text_color_white"
            android:textSize="16sp" />


    </LinearLayout>


</RelativeLayout>