<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/cart_publisher_check_flag_ImageView"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginLeft="15dp"
        android:src="@drawable/selected_favorite_check_box_yellow"
        app:layout_constraintBottom_toBottomOf="@id/cart_publisher_name_Layout"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/cart_publisher_name_Layout" />

    <com.aquila.lib.widget.group.GroupImageTextLayout
        android:id="@+id/cart_publisher_name_Layout"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_marginLeft="15dp"
        android:gravity="center_vertical"
        app:attr_image_height="10dp"
        app:attr_image_src="@drawable/ic_arrow_right_black"
        app:attr_image_src_tint="?theme_icon_tint_color"
        app:attr_image_width="10dp"
        app:attr_parent_orientation="IMAGE_RIGHT_TEXT_LEFT"
        app:attr_text="出版社的名字"
        app:attr_text_color="?theme_text_color_black"
        app:attr_text_margin_image_size="5dp"
        app:attr_text_size="14sp"
        app:layout_constraintLeft_toRightOf="@id/cart_publisher_check_flag_ImageView"
        app:layout_constraintTop_toTopOf="parent"
        tools:attr_text="出版社的名字" />

    <com.aquila.lib.widget.view.CustomRecyclerView
        android:id="@+id/cart_publisher_data_RecyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/cart_publisher_name_Layout"
        tools:itemCount="2"
        tools:listitem="@layout/item_cart_book_info_layout" />
<!--        app:attr_is_expanded="true"-->


</androidx.constraintlayout.widget.ConstraintLayout>