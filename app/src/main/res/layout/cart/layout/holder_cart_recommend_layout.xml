<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.aquila.lib.widget.view.AdaptiveImageView
        android:id="@+id/cart_recommend_cover_ImageView"
        android:layout_width="100dp"
        android:layout_height="130dp"
        android:adjustViewBounds="true"
        android:background="@drawable/shape_book_corners_bg"
        android:scaleType="centerCrop"
        app:attr_aspect_ratio="96,128"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/demo_01" />

    <TextView
        android:id="@+id/cart_recommend_name_TextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:text=""
        android:textColor="?theme_text_color_black"
        android:textSize="12sp"
        android:textStyle="bold"
        app:layout_constrainedWidth="true"
        app:layout_constraintLeft_toLeftOf="@id/cart_recommend_cover_ImageView"
        app:layout_constraintRight_toRightOf="@id/cart_recommend_cover_ImageView"
        app:layout_constraintTop_toBottomOf="@id/cart_recommend_cover_ImageView"
        tools:text="圣经信息" />

    <TextView
        android:id="@+id/cart_recommend_author_TextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:singleLine="true"
        android:text=""
        android:textColor="?theme_text_color_dark"
        android:textSize="12sp"
        app:layout_constrainedWidth="true"
        app:layout_constraintLeft_toLeftOf="@id/cart_recommend_cover_ImageView"
        app:layout_constraintRight_toRightOf="@id/cart_recommend_cover_ImageView"
        app:layout_constraintTop_toBottomOf="@id/cart_recommend_name_TextView"
        tools:text="约翰·麦克阿瑟..." />


</androidx.constraintlayout.widget.ConstraintLayout>