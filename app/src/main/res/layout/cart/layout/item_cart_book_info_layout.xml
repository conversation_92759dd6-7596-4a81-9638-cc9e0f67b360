<?xml version="1.0" encoding="utf-8"?>

<com.chauthai.swipereveallayout.SwipeRevealLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:dragEdge="right"
    app:mode="same_level">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <Button
            android:id="@+id/book_cart_move_favorite_Button"
            android:layout_width="90dp"
            android:layout_height="match_parent"
            android:background="@color/color_orange_FF8A00"
            android:gravity="center"
            android:minWidth="70dp"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:text="移入收藏"
            android:textColor="?theme_text_color_white"
            android:textSize="14sp" />

        <Button
            android:id="@+id/book_cart_delete_Button"
            android:layout_width="90dp"
            android:layout_height="match_parent"
            android:background="@color/color_red_E53935"
            android:gravity="center"
            android:minWidth="70dp"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:text="@string/label_delete"
            android:textColor="?theme_text_color_white"
            android:textSize="14sp" />
    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/book_cart_content_container_Layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:alpha="1">

        <ImageView
            android:id="@+id/book_cart_check_flag_ImageView"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginLeft="15dp"
            android:src="@drawable/selected_favorite_check_box_yellow"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/book_cart_has_removed_TextView"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:background="@drawable/shaper_circle_black_teansparent_bg"
            android:elevation="1dp"
            android:gravity="center"
            android:text="已下架"
            android:visibility="gone"
            android:textColor="?theme_text_color_white"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="@id/book_cart_cover_ImageView"
            app:layout_constraintLeft_toLeftOf="@id/book_cart_cover_ImageView"
            app:layout_constraintRight_toRightOf="@id/book_cart_cover_ImageView"
            app:layout_constraintTop_toTopOf="@id/book_cart_cover_ImageView" />


        <com.aquila.lib.widget.view.AdaptiveImageView
            android:id="@+id/book_cart_cover_ImageView"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"
            app:attr_aspect_ratio="81,108"
            android:src="@drawable/ic_cover_demo"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/book_cart_check_flag_ImageView"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/ic_cover_demo" />

        <TextView
            android:id="@+id/book_cart_name_TextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:ellipsize="end"
            android:lines="2"
            android:minHeight="47dp"
            android:text="圣化的哀伤"
            android:textColor="?theme_text_color_black"
            android:textSize="16sp"
            app:layout_constraintLeft_toRightOf="@id/book_cart_cover_ImageView"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/book_cart_cover_ImageView"
            tools:text="圣化的哀伤 " />

        <TextView
            android:id="@+id/book_cart_price_label_TextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:ellipsize="end"
            android:maxLines="2"

            android:text="现价:"
            android:textColor="?theme_text_color_black"
            android:textSize="12sp"
            app:layout_constraintLeft_toLeftOf="@id/book_cart_name_TextView"
            app:layout_constraintTop_toBottomOf="@id/book_cart_name_TextView" />

        <TextView
            android:id="@+id/book_cart_real_price_TextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="10dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:text="$2.34(约9.32)"
            android:textColor="?theme_text_color_red"
            android:textSize="12sp"
            app:layout_constraintBaseline_toBaselineOf="@id/book_cart_price_label_TextView"
            app:layout_constraintLeft_toRightOf="@id/book_cart_price_label_TextView"
            tools:text="$2.34(约9.32)" />


        <TextView
            android:id="@+id/book_cart_original_price_label_TextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:text="原价:"
            android:textColor="?theme_text_color_black"
            android:textSize="12sp"
            app:layout_constraintLeft_toLeftOf="@id/book_cart_name_TextView"
            app:layout_constraintTop_toBottomOf="@id/book_cart_price_label_TextView" />

        <TextView
            android:id="@+id/book_cart_original_price_TextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="10dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="?theme_text_color_black"
            android:textSize="12sp"
            app:layout_constraintBaseline_toBaselineOf="@id/book_cart_original_price_label_TextView"
            app:layout_constraintLeft_toRightOf="@id/book_cart_original_price_label_TextView"
            tools:text="$3.34" />


        <TextView
            android:id="@+id/book_cart_coupon_TextView"
            android:layout_width="45dp"
            android:layout_height="20dp"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="5dp"
            android:background="@drawable/ic_cart_coupon_bg"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="2"
            android:text="优惠券"
            android:textColor="?theme_text_color_white"
            android:textSize="10sp"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="@id/book_cart_original_price_label_TextView"
            app:layout_constraintTop_toBottomOf="@id/book_cart_original_price_TextView"
            tools:visibility="visible" />
    </androidx.constraintlayout.widget.ConstraintLayout>


</com.chauthai.swipereveallayout.SwipeRevealLayout>