<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_bg_color_white"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/book_store_title_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:attr_hide_back="true"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="@string/bookstore" />

    <com.aquila.lib.layout.SmartRefreshLayout
        android:id="@+id/book_store_RefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?theme_bg_color_white"
        app:srlEnableLoadMore="false">

        <com.aquila.lib.widget.view.CustomRecyclerView
            android:id="@+id/book_store_data_RecyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:attr_item_decoration_type="ITEM_SPACE"
            app:attr_item_margin="0dp"
            app:attr_layout_style="list_vertical" />

    </com.aquila.lib.layout.SmartRefreshLayout>

</LinearLayout>