<?xml version="1.0" encoding="utf-8"?>

<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"

    app:cardElevation="10dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="350dp"
        android:layout_gravity="bottom"
        android:background="?theme_bg_color_white_1E"
        android:paddingLeft="15dp"
        android:paddingTop="15dp"
        android:paddingRight="15dp">

        <TextView
            android:id="@+id/dialog_verse_title_TextView"
            android:layout_width="0dp"
            android:layout_height="35dp"
            android:layout_marginLeft="9dp"
            android:gravity="center_vertical"
            android:textColor="?attr/theme_text_color_black"
            android:textSize="16sp"
            app:layout_constrainedWidth="true"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/dialog_verse_close_ImageView"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="这里是标题这里是标题这里是标题" />

        <ImageView
            android:id="@+id/dialog_verse_close_ImageView"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:background="?theme_comm_click_bg"
            android:foreground="?android:attr/selectableItemBackground"
            android:scaleType="center"
            android:src="?theme_dialog_close_ImageView"
            android:textSize="16sp"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <com.aquila.lib.widget.view.ElasticScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="45dp"
            android:scrollbarSize="2dp"
            android:scrollIndicators="right"
            android:paddingLeft="21dp"
            android:paddingRight="21dp"
            android:layout_marginBottom="20dp"
            app:layout_constraintTop_toBottomOf="@id/dialog_verse_title_TextView">

            <TextView
                android:id="@+id/dialog_verse_content_TextView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:lineSpacingMultiplier="1.1"
                android:textColor="?attr/theme_text_color_black"
                android:textSize="18sp"
                tools:text="这里是标题，这里只有正文没有标题\n\n这里是标题，这里只有正文没有标题\n\n这里是标题，这里只有正文没有标题\n\n这里是标题，这里只有正文没有标题\n\n这里是标题，这里只有正文没有标题\n\n这里是标题，这里只有正文没有标题\n\n" />
        </com.aquila.lib.widget.view.ElasticScrollView>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>
