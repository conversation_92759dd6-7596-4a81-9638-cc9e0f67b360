<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="60dp"
    android:background="?theme_bg_color_white_top_line">

    <com.aquila.lib.widget.group.GroupImageTextLayout
        android:id="@+id/bottom_tab_book_shelf_Layout"
        style="@style/style_home_bottom_tab"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:attr_image_height="20dp"
        app:attr_image_src="@drawable/ic_home_tab_book_shelf"
        app:attr_image_src_tint="?theme_tab_icon_tint_color"
        app:attr_image_width="20dp"
        app:attr_is_selected="false"
        app:attr_scale_type="FIT_CENTER"
        app:attr_text="@string/book_shelf"
        app:attr_text_color="?theme_tab_icon_tint_color"

        app:attr_text_margin_image_size="5dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/bottom_tab_book_store_Layout"
        app:layout_constraintTop_toTopOf="parent" />

    <com.aquila.lib.widget.group.GroupImageTextLayout
        android:id="@+id/bottom_tab_book_store_Layout"
        style="@style/style_home_bottom_tab"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:attr_image_height="20dp"
        app:attr_image_src="@drawable/ic_home_tab_book_store"
        app:attr_image_src_tint="?theme_tab_icon_tint_color"
        app:attr_image_width="20dp"
        app:attr_scale_type="FIT_CENTER"
        app:attr_text="@string/bookstore"
        app:attr_text_color="?theme_tab_icon_tint_color"
        app:attr_text_margin_image_size="5dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintLeft_toRightOf="@id/bottom_tab_book_shelf_Layout"
        app:layout_constraintRight_toLeftOf="@id/bottom_tab_mine_Layout"
        app:layout_constraintTop_toTopOf="parent" />

    <com.aquila.lib.widget.group.GroupImageTextLayout
        android:id="@+id/bottom_tab_mine_Layout"
        style="@style/style_home_bottom_tab"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:attr_image_height="16dp"
        app:attr_image_src="@drawable/ic_home_tab_mine"
        app:attr_image_src_tint="?theme_tab_icon_tint_color"
        app:attr_image_width="16dp"
        app:attr_is_selected="false"
        app:attr_scale_type="FIT_CENTER"
        app:attr_text="@string/title_home_mine"
        app:attr_text_color="?theme_tab_icon_tint_color"
        app:attr_text_margin_image_size="7.5dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintLeft_toRightOf="@id/bottom_tab_book_store_Layout"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <com.aquila.lib.widget.view.DotView
        android:id="@+id/bottom_tab_mine_unread_message_View"
        android:layout_width="8dp"
        android:layout_height="8dp"
        android:layout_marginLeft="25dp"
        android:layout_marginTop="5dp"
        app:attr_dot_color="?theme_text_color_red"
        app:attr_dot_radius="4dp"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="@id/bottom_tab_mine_Layout"
        app:layout_constraintRight_toRightOf="@id/bottom_tab_mine_Layout"
        app:layout_constraintTop_toTopOf="@id/bottom_tab_mine_Layout" />

</androidx.constraintlayout.widget.ConstraintLayout>