<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingLeft="14dp"
    android:paddingTop="0dp"
    android:paddingRight="14dp"
    android:paddingBottom="0dp">

    <androidx.cardview.widget.CardView
        android:id="@+id/banner_card_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="?theme_bg_color_white_1E"
        android:paddingTop="0dp"
        android:paddingBottom="0dp">

        <com.aquila.lib.widget.view.AdaptiveImageView
            android:id="@+id/item_banner_ImageView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="137dp"
            android:background="?theme_bg_color_white_1E"
            android:scaleType="fitCenter"
            app:attr_aspect_ratio="327,137"
            app:attr_fit_type="FIT_WIDTH" />
    </androidx.cardview.widget.CardView>
</FrameLayout>