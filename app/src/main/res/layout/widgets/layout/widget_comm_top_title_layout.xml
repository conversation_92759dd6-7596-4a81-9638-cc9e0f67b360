<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:minHeight="50dp">

    <ImageView
        android:id="@+id/top_title_back_ImageView"
        android:layout_width="60dp"
        android:layout_height="50dp"
        android:padding="8dp"
        android:background="?theme_comm_click_bg"
        android:foreground="?android:attr/selectableItemBackground"
        app:tint="?theme_icon_tint_color"
        tools:ignore="UseAppTint"
        android:src="@drawable/ic_in_book_left_arrow" />

    <TextView
        android:id="@+id/top_title_left_TextView"
        android:layout_width="wrap_content"
        android:layout_height="50dp"
        android:layout_alignParentStart="true"
        android:gravity="center"
        android:singleLine="true"
        android:ellipsize="end"
        android:textColor="?theme_text_color_black_17191c"
        android:textSize="22sp"
        android:paddingStart="16dp"
        android:paddingEnd="10dp"
        android:textStyle="bold"
        android:visibility="gone"
        tools:text="左侧标题" />

    <TextView
        android:id="@+id/top_title_content_TextView"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:layout_centerVertical="true"
        android:layout_marginLeft="50dp"
        android:layout_marginRight="50dp"
        android:gravity="center"
        android:singleLine="true"
        android:ellipsize="end"
        android:textColor="?theme_text_color_black"
        android:textSize="18sp"
        tools:text="这个是标题" />

</RelativeLayout>