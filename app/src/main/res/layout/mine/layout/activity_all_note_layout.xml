<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="?theme_bg_color_white"
    >

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/all_note_top_title_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/top_title_height"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="@string/notes"
        app:layout_constraintTop_toTopOf="parent" />

    <com.aquila.lib.layout.SmartRefreshLayout
        android:id="@+id/all_note_container_RefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/all_note_top_title_layout">

        <com.aquila.lib.widget.view.CustomRecyclerView
            android:id="@+id/all_note_data_RecyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            app:attr_divide_left_margin="15dp"
            app:attr_divide_right_margin="15dp"
            app:attr_item_decoration_type="ITEM_DIVIDE_LNE"
            app:attr_item_divide_line_color="@color/divide_line_color"
            app:attr_item_divide_line_size="@dimen/divide_line_size"
            app:attr_layout_style="grid_vertical"
            app:attr_span_count="3"
            tools:itemCount="13"
            tools:listitem="@layout/holder_item_book_note_layout" />

    </com.aquila.lib.layout.SmartRefreshLayout>

    <com.aquila.lib.widget.group.GroupImageTextLayout
        android:id="@+id/book_shelf_empty_icon_Layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:elevation="10dp"
        android:gravity="center_horizontal"
        app:attr_image_height="120dp"
        app:attr_image_src="?theme_ic_empty_book_shelf"
        app:attr_image_width="120dp"
        app:attr_parent_orientation="IMAGE_TOP_TEXT_BOTTOM"
        app:attr_text="@string/no_book_note"
        app:attr_text_color="?theme_text_color_black"
        app:attr_text_margin_image_size="20dp"
        app:attr_text_size="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.4"
        android:background="?theme_bg_color_white"
        />

</androidx.constraintlayout.widget.ConstraintLayout>