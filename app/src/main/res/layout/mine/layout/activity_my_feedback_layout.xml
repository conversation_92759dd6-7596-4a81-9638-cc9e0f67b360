<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_bg_color_white"
    android:fitsSystemWindows="true">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/my_feedback_top_title_layout"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="@string/my_feedback"
        app:layout_constraintTop_toTopOf="parent" />


    <Button
        android:id="@+id/my_feedback_new_problem_Button"
        android:layout_width="300dp"
        android:layout_height="40dp"
        android:layout_marginBottom="33dp"
        android:background="?theme_download_btn_orange_bg"
        android:text="@string/new_feedback"
        android:textColor="?theme_text_color_orange"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/my_feedback_new_problem_Button"
        app:layout_constraintTop_toBottomOf="@id/my_feedback_top_title_layout">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <com.aquila.lib.widget.view.CustomRecyclerView
                android:id="@+id/my_feedback_data_RecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:attr_is_expanded="true"
                app:attr_item_decoration_type="ITEM_DIVIDE_LNE"
                app:attr_item_divide_line_color="?theme_bg_color_divide_line_color"

                app:attr_item_divide_line_size="@dimen/divide_line_size"
                app:attr_item_margin="10dp"
                app:attr_layout_style="list_vertical"
                tools:itemCount="5"
                tools:listitem="@layout/holder_my_feedback_item_layout" />

            <TextView
                android:id="@+id/my_feedback_tips_TextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="40dp"
                android:text="@string/only_show_half_year_feedback"
                android:textColor="?theme_text_color_gray"
                android:textSize="16sp" />


        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>