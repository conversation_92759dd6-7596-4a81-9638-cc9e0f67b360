<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_bg_color_white"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/feedback_top_title_layout"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_is_text_bold="true"
        app:attr_title_text="@string/feedback_suggest"
        app:attr_title_text_color="?theme_text_color_black"
        app:attr_title_text_size="18sp" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="15dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:minHeight="30dp"
                android:text="@string/feedback_type"
                android:textColor="?theme_text_color_black"
                android:textSize="14sp"
                android:textStyle="bold" />

            <com.aquila.lib.widget.view.CustomRecyclerView
                android:id="@+id/feedback_type_RecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                app:attr_item_decoration_type="ITEM_SPACE"
                app:attr_item_margin="10dp"
                app:attr_layout_style="list_horizontal"
                tools:listitem="@layout/holder_item_feedback_type_layout" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:gravity="center_vertical"
                android:minHeight="40dp"
                android:textStyle="bold"
                android:text="@string/question_and_suggest"
                android:textColor="?theme_text_color_black"
                android:textSize="14sp" />

            <EditText
                android:id="@+id/feedback_input_content_EditText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?theme_shape_gray_conner_8_bg"
                android:gravity="top"
                android:hint="我们重视您的每一条反馈，请描述您遇到的问题"
                android:minHeight="200dp"
                android:padding="10dp"
                android:textColorHint="?theme_text_color_gray_BC_88"
                android:textColor="?theme_text_color_black"
                android:textSize="16sp" />

            <Button
                android:id="@+id/feedback_submit_Button"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="30dp"
                android:layout_marginRight="15dp"
                android:background="?theme_bg_white_orange_btn"
                android:text="@string/label_submit"
                android:textColor="?theme_text_color_white"
                android:textSize="16sp" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>