<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?theme_comm_click_bg"
    android:foreground="?selectableItemBackground"
    android:minHeight="50dp">

    <TextView
        android:id="@+id/item_help_center_content_TextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_toLeftOf="@id/item_help_center_arrow_ImageView"
        android:gravity="center_vertical"
        android:minHeight="50dp"
        android:textColor="?theme_text_color_black"
        android:textSize="16sp"
        tools:text="已购买的电子书是否可永久性阅读？" />

    <ImageView
        android:id="@+id/item_help_center_arrow_ImageView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="15dp"
        android:scaleType="center"
        android:src="@drawable/ic_arrow_right_gray" />
</RelativeLayout>