<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_root_view_bg"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/order_list_top_TitleLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/top_title_height"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="@string/home_mine_order_record" />


    <com.aquila.lib.widget.group.GroupImageTextLayout
        android:id="@+id/purchase_list_empty_icon_Layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:elevation="10dp"
        android:gravity="center"
        android:paddingBottom="100dp"
        android:visibility="gone"
        app:attr_image_height="0dp"
        app:attr_image_width="0dp"
        app:attr_parent_orientation="IMAGE_TOP_TEXT_BOTTOM"
        app:attr_text="@string/no_purchased_record"
        app:attr_text_color="?theme_text_color_black"
        app:attr_text_margin_image_size="20dp"
        app:attr_text_size="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.2"
        app:layout_constraintWidth_percent="0.4"
        tools:visibility="visible" />

    <com.aquila.lib.layout.SmartRefreshLayout
        android:id="@+id/order_list_data_RefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:srlEnableLoadMore="false"
        tools:visibility="visible">

        <com.aquila.lib.widget.view.CustomRecyclerView
            android:id="@+id/order_list_data_RecyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:attr_end_item_margin="15dp"
            app:attr_layout_style="list_vertical"
            tools:listitem="@layout/holder_item_order_list_layout"
            tools:visibility="gone" />

    </com.aquila.lib.layout.SmartRefreshLayout>

</LinearLayout>