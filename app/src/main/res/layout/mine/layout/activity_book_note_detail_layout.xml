<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="?theme_bg_color_white">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/book_note_detail_title_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/top_title_height"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="@string/note_list"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/book_note_detail_note_recyclerBin_Button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="10dp"
        android:minHeight="65dp"
        android:background="?theme_bg_color_white_top_line"
        android:foreground="?android:selectableItemBackground"
        android:gravity="center"
        android:text="@string/note_recyclerBin"
        android:textColor="?theme_text_color_blue_book_notes"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent" />

    <com.aquila.lib.layout.SmartRefreshLayout
        android:id="@+id/book_note_detail_RefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/book_note_detail_note_recyclerBin_Button"
        app:layout_constraintTop_toBottomOf="@id/book_note_detail_title_layout">

        <androidx.core.widget.NestedScrollView
            android:id="@+id/book_note_detail_NestedScrollView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false">

                    <com.aquila.lib.widget.view.AdaptiveImageView
                        android:id="@+id/book_note_detail_cover_ImageView"
                        android:layout_width="60dp"
                        android:layout_height="75dp"
                        android:layout_marginLeft="24dp"
                        android:layout_marginTop="15dp"
                        app:attr_aspect_ratio="50,68"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:src="@drawable/ic_default_book_cover" />

                    <TextView
                        android:id="@+id/book_note_detail_name_TextView"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="15dp"
                        android:ellipsize="end"
                        android:maxLines="2"
                        android:paddingRight="15dp"
                        android:textColor="?theme_text_color_black"
                        android:textSize="16sp"
                        app:layout_constraintLeft_toRightOf="@id/book_note_detail_cover_ImageView"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="@id/book_note_detail_cover_ImageView"
                        tools:text="丁道尔新约圣经注释-哥林多前书" />

                    <TextView
                        android:id="@+id/book_note_detail_author_TextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="?theme_text_color_gray"
                        android:textSize="12sp"
                        app:layout_constraintBottom_toTopOf="@id/book_note_detail_note_count_TextView"
                        app:layout_constraintLeft_toLeftOf="@id/book_note_detail_name_TextView"
                        app:layout_constraintTop_toBottomOf="@id/book_note_detail_name_TextView"
                        tools:text="柯德纳（Derek Kidner）" />

                    <TextView
                        android:id="@+id/book_note_detail_note_count_TextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:textColor="?theme_text_color_dark"
                        android:textSize="12sp"
                        app:layout_constraintBottom_toBottomOf="@id/book_note_detail_cover_ImageView"
                        app:layout_constraintLeft_toLeftOf="@id/book_note_detail_name_TextView"
                        tools:text="323条笔记" />

                    <View
                        android:id="@+id/book_note_detail_top_divide_line_View"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/divide_line_size"
                        android:layout_marginTop="15dp"
                        app:layout_constraintTop_toBottomOf="@id/book_note_detail_cover_ImageView" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/divide_line_size"
                    android:background="?theme_bg_color_divide_line_color" />

                <com.aquila.lib.widget.view.CustomRecyclerView
                    android:id="@+id/book_note_detail_data_RecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:nestedScrollingEnabled="false"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    app:attr_layout_style="list_vertical"
                    app:attr_item_divide_line_size="@dimen/divide_line_size"
                    app:attr_item_decoration_type="ITEM_DIVIDE_LNE"
                    app:attr_item_divide_line_color="?theme_bg_color_divide_line_color"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/book_note_detail_top_divide_line_View"
                    tools:itemCount="100"
                    tools:listitem="@layout/holder_item_detail_book_note_layout" />

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </com.aquila.lib.layout.SmartRefreshLayout>

</androidx.constraintlayout.widget.ConstraintLayout>