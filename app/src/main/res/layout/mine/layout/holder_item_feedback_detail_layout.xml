<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="15dp">

    <ImageView
        android:id="@+id/item_feedback_detail_avatar_ImageView"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:scaleType="centerCrop"
        android:src="@drawable/ic_default_avatar"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/item_feedback_detail_name_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:singleLine="true"
        android:text="@string/mine"
        android:textColor="?theme_text_color_black"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="@id/item_feedback_detail_avatar_ImageView"
        app:layout_constraintLeft_toRightOf="@+id/item_feedback_detail_avatar_ImageView"
        app:layout_constraintTop_toTopOf="@id/item_feedback_detail_avatar_ImageView" />

    <TextView
        android:id="@+id/item_feedback_detail_time_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:singleLine="true"
        android:text="2021-12-1 12:09"
        android:textColor="?theme_text_color_gray"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="@id/item_feedback_detail_name_TextView"
        app:layout_constraintLeft_toRightOf="@id/item_feedback_detail_name_TextView"
        app:layout_constraintTop_toTopOf="@id/item_feedback_detail_name_TextView" />

    <TextView
        android:id="@+id/item_feedback_detail_content_TextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:paddingLeft="55dp"
        android:textColor="?theme_text_color_black"
        android:textSize="14sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/item_feedback_detail_avatar_ImageView"
        tools:paddingLeft="0dp"
        tools:text="感谢您的反馈，目前导出笔记的功能正在规划中，将在后续版本推出。" />

</androidx.constraintlayout.widget.ConstraintLayout>