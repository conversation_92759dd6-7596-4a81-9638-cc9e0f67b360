<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="?theme_root_view_bg"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/faq_detail_title_layout"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="这个是标题" />

    <TextView
        android:id="@+id/faq_detail_TextView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="10dp"
        android:visibility="gone"
        android:textColor="?theme_text_color_black"
        android:textSize="16sp" />

    <WebView
        android:id="@+id/faq_detail_WebView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />


    <TextView
        android:id="@+id/faq_detail_no_network_TextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="100dp"
        android:gravity="center"
        android:padding="30dp"
        android:text="网络连接失败，请稍后再试"
        android:textColor="?theme_text_color_black"
        android:textSize="16sp"
        android:visibility="gone" />


</LinearLayout>