<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_root_view_bg"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="语言" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?theme_bg_color_white"
        android:divider="?theme_divide_line_horizontal"
        android:orientation="vertical"
        android:showDividers="middle">

        <com.aquila.lib.widget.group.GroupImageTextLayout
            android:id="@+id/language_simple_Layout"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@drawable/selector_comm_click_bg"
            android:gravity="center_vertical"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            app:attr_image_src="@drawable/ic_check_yellow"
            app:attr_image_src_tint="@color/bt_text_blue"
            app:attr_parent_orientation="IMAGE_RIGHT_TEXT_LEFT_SIDE_ALIGN"
            app:attr_text="简体中文"
            app:attr_text_color="?theme_text_color_black"
            app:attr_text_size="16sp" />


        <com.aquila.lib.widget.group.GroupImageTextLayout
            android:id="@+id/language_traditional_layout"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@drawable/selector_comm_click_bg"
            android:gravity="center_vertical"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            app:attr_image_src="@drawable/ic_check_yellow"
            app:attr_image_src_tint="@color/bt_text_blue"
            app:attr_parent_orientation="IMAGE_RIGHT_TEXT_LEFT_SIDE_ALIGN"
            app:attr_text="繁體中文"
            app:attr_text_color="?theme_text_color_black"
            app:attr_text_size="16sp" />


    </LinearLayout>


</LinearLayout>