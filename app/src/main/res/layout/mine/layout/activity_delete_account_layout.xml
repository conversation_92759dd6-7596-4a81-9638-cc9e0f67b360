<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_root_view_bg"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/delete_account_top_title_layout"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_alignParentTop="true"
        android:background="?theme_bg_color_white"
        app:attr_title_text="@string/delete_account" />

    <Button
        android:id="@+id/delete_account_execute_Button"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="30dp"
        android:layout_marginRight="30dp"
        android:layout_marginBottom="40dp"
        android:background="@drawable/selector_all_conner_brown_bg"
        android:enabled="true"
        android:text="@string/delete_my_account"
        android:textColor="?theme_text_color_white"
        android:textSize="16sp" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/delete_account_execute_Button"
        android:layout_below="@id/delete_account_top_title_layout">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <ImageView
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="20dp"
                android:src="@drawable/ic_delete_account_warnning" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="20dp"
                android:gravity="center"
                android:text="@string/delete_account_tip"
                android:textColor="?theme_text_color_black"
                android:textSize="18sp"
                android:textStyle="bold" />


            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:background="?theme_bg_color_white"
                android:paddingLeft="15dp"
                android:paddingTop="25dp"
                android:paddingRight="15dp"
                android:paddingBottom="25dp"
                android:text="@string/delete_account_warning"
                android:textColor="?theme_text_color_black"
                android:textSize="14sp" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</RelativeLayout>