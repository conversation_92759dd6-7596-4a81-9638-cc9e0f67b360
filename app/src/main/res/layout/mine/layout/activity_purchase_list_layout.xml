<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="?theme_bg_color_white"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/purchase_list_top_TitleLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/top_title_height"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="@string/home_mine_my_book" />

    <com.aquila.lib.widget.group.GroupImageTextLayout
        android:id="@+id/purchase_list_empty_icon_Layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:elevation="10dp"
        android:gravity="center"
        android:paddingBottom="100dp"
        android:visibility="gone"
        tools:visibility="gone"
        app:attr_image_height="0dp"
        app:attr_image_width="0dp"
        app:attr_parent_orientation="IMAGE_TOP_TEXT_BOTTOM"
        app:attr_text="@string/have_not_bought_any_book"
        app:attr_text_color="?theme_text_color_black"
        app:attr_text_margin_image_size="20dp"
        app:attr_text_size="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.2"
        app:layout_constraintWidth_percent="0.4" />

    <com.aquila.lib.layout.SmartRefreshLayout
        android:id="@+id/purchase_list_SmartRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:srlEnableRefresh="true"
        app:srlEnableAutoLoadMore="true"
        >

        <com.aquila.lib.widget.view.CustomRecyclerView
            android:id="@+id/purchase_list_data_RecyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginLeft="24dp"
            android:layout_marginRight="20dp"
            android:nestedScrollingEnabled="false"
            app:attr_end_item_margin="50dp"
            app:attr_item_decoration_type="ITEM_SPACE"
            app:attr_item_divide_line_color="@color/divide_line_color"
            app:attr_item_divide_line_size="@dimen/divide_line_size"
            app:attr_item_margin="14dp"
            app:attr_layout_style="list_vertical"
            app:attr_start_item_margin="19dp"
            app:layout_constraintTop_toBottomOf="@id/micro_new_book_title_TextView"
            tools:itemCount="5"
            tools:visibility="visible"
            tools:listitem="@layout/item_micro_store_book_list_layout" />

    </com.aquila.lib.layout.SmartRefreshLayout>


</LinearLayout>