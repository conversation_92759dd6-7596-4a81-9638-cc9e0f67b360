<?xml version="1.0" encoding="utf-8"?>
<com.chauthai.swipereveallayout.SwipeRevealLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:dragEdge="right"
    app:mode="same_level">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent">

        <Button
            android:id="@+id/item_note_detail_delete_Button"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:background="@color/color_red_E53935"
            android:gravity="center"
            android:minWidth="70dp"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:text="@string/label_delete"
            android:textColor="?theme_text_color_white"
            android:textSize="16sp" />

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="10dp"
        android:paddingBottom="10dp">

        <com.aquila.lib.widget.view.DotImageView
            android:id="@+id/item_note_detail_dot_ImageView"
            android:layout_width="10dp"
            android:layout_height="10dp"
            android:layout_margin="15dp"
            app:attr_dot_color="@color/color_select_orange"
            app:attr_dot_radius="5dp"
            app:attr_is_show_dot="true"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/item_note_detail_date_TextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:textColor="?theme_text_color_gray"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="@id/item_note_detail_dot_ImageView"
            app:layout_constraintLeft_toRightOf="@id/item_note_detail_dot_ImageView"
            app:layout_constraintTop_toTopOf="@id/item_note_detail_dot_ImageView"
            tools:text="2019/06/16 11:25" />

        <ImageView
            android:id="@+id/item_note_detail_delete_ImageView"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_margin="10dp"
            android:layout_marginLeft="15dp"
            android:background="?theme_comm_click_bg"
            android:padding="10dp"
            android:scaleType="fitCenter"
            android:src="?theme_ic_note_delete"
            app:layout_constraintBottom_toBottomOf="@id/item_note_detail_dot_ImageView"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/item_note_detail_dot_ImageView" />

        <ImageView
            android:id="@+id/item_note_detail_edit_ImageView"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginLeft="15dp"
            android:background="?theme_comm_click_bg"
            android:padding="10dp"
            android:scaleType="fitCenter"
            android:src="?theme_ic_note_edit"
            app:layout_constraintBottom_toBottomOf="@id/item_note_detail_dot_ImageView"
            app:layout_constraintRight_toLeftOf="@id/item_note_detail_delete_ImageView"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/item_note_detail_dot_ImageView" />


        <ImageView
            android:id="@+id/item_note_detail_read_ImageView"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginLeft="15dp"
            android:background="?theme_comm_click_bg"
            android:padding="10dp"
            android:scaleType="fitCenter"
            android:src="?theme_ic_note_read"
            app:layout_constraintBottom_toBottomOf="@id/item_note_detail_dot_ImageView"
            app:layout_constraintRight_toLeftOf="@id/item_note_detail_edit_ImageView"
            app:layout_constraintTop_toTopOf="@id/item_note_detail_dot_ImageView" />

        <View
            android:id="@+id/item_note_detail_quote_View"
            android:layout_width="2dp"
            android:layout_height="0dp"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            android:background="?theme_text_color_gray_BC_88"
            app:layout_constraintBottom_toBottomOf="@id/item_note_detail_summary_TextView"
            app:layout_constraintLeft_toLeftOf="@id/item_note_detail_dot_ImageView"
            app:layout_constraintTop_toTopOf="@id/item_note_detail_summary_TextView" />

        <TextView
            android:id="@+id/item_note_detail_summary_TextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="left"
            android:textDirection="ltr"
            android:lineSpacingMultiplier="1.05"
            android:maxLines="2"
            android:paddingLeft="5dp"
            android:paddingTop="5dp"
            android:paddingRight="15dp"
            android:paddingBottom="5dp"
            android:textColor="?theme_text_color_note_gray"
            android:textSize="12sp"
            app:layout_constraintLeft_toRightOf="@id/item_note_detail_quote_View"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/item_note_detail_read_ImageView"
            tools:text="引用 | 就旧约时代的祭司职务而言，利未记是一本编辑得相当周详的参考手利未记是一本编辑得相当周详的参考手利未记是一本编辑得相当周详的参考手利未记是一本编辑得相当周详的参考手，" />

        <TextView
            android:id="@+id/item_note_detail_content_TextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="left"
            android:textDirection="ltr"
            android:lineSpacingMultiplier="1.05"
            android:maxLines="3"
            android:paddingLeft="15dp"
            android:paddingTop="5dp"
            android:paddingRight="15dp"
            android:paddingBottom="5dp"
            android:textColor="?theme_text_color_note_black"
            android:textSize="14sp"
            app:layout_constraintTop_toBottomOf="@id/item_note_detail_summary_TextView"
            tools:text="在死海古卷中在死海古卷中在死海古卷中在死海古卷中在死海古卷中在死海古卷中在死海古卷中在死海古卷中在死海古卷中在死海古卷中在死海古卷中在死海古卷中在死海古卷中在死海古卷中在死海古卷中在死海古卷中在死海古卷中在死海古卷中在死海古卷中在死海古卷中在死海古卷中在死海古卷中在死海古卷中在死海古卷中，" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</com.chauthai.swipereveallayout.SwipeRevealLayout>