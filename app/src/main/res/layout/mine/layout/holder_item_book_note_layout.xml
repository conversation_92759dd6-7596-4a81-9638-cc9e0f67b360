<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="10dp"
    tools:layout_width="140dp">

    <com.aquila.lib.widget.view.AdaptiveImageView
        android:id="@+id/item_book_note_cover_ImageView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="3dp"
        android:scaleType="fitXY"
        app:attr_aspect_ratio="92,132"
        app:attr_round_height="0dp"
        app:attr_round_width="0dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/ic_default_book_cover" />

    <TextView
        android:id="@+id/item_book_note_name_TextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="2"
        android:paddingTop="5dp"
        android:paddingBottom="5dp"
        android:textColor="?theme_text_color_black"
        android:textSize="12sp"
        android:visibility="visible"
        android:lines="2"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintLeft_toLeftOf="@id/item_book_note_cover_ImageView"
        app:layout_constraintRight_toRightOf="@id/item_book_note_cover_ImageView"
        app:layout_constraintTop_toBottomOf="@id/item_book_note_cover_ImageView"
        tools:text="丁道尔"
        tools:visibility="visible" />

    <com.aquila.lib.widget.group.GroupImageTextLayout
        android:id="@+id/item_book_note_count_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        app:attr_image_height="20dp"
        app:attr_image_src="?theme_ic_book_note"
        app:attr_image_width="20dp"
        app:attr_parent_orientation="IMAGE_LEFT_TEXT_RIGHT"
        app:attr_scale_type="FIT_CENTER"
        app:attr_text_color="?theme_text_color_gray"
        app:attr_text_margin_image_size="5dp"
        app:attr_text_size="12sp"
        app:layout_constraintLeft_toLeftOf="@id/item_book_note_cover_ImageView"
        app:layout_constraintTop_toBottomOf="@id/item_book_note_name_TextView"
        tools:attr_text="4324条" />

</androidx.constraintlayout.widget.ConstraintLayout>
