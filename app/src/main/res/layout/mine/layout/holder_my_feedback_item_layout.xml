<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.aquila.lib.widget.view.DotView
        android:id="@+id/item_my_feedback_unread_flag_DotView"
        android:layout_width="10dp"
        android:layout_height="10dp"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="10dp"
        android:visibility="visible"
        app:attr_dot_color="?theme_text_color_red"
        app:attr_dot_radius="5dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/item_my_feedback_title_TextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="25dp"
        android:layout_marginTop="5dp"
        android:ellipsize="end"
        android:textStyle="bold"
        android:maxLines="2"

        android:textColor="?theme_text_color_black"
        android:textSize="14sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/item_my_feedback_message_count_Layout"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="微读书城的弟兄姐妹，想问下如何能导出自己的笔记想问下如何能导出自己的笔记想问下如何能导出自己的笔记想问下如何能导出自己的笔记想问下如何能导出自己的笔记想问下如何能导出自己的笔记想问下如何能导出自己的笔记想问下如何能导出自己的笔记？" />

    <TextView
        android:id="@+id/item_my_feedback_time_TextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:textColor="?theme_text_color_gray"
        android:textSize="14sp"
        app:layout_constraintLeft_toLeftOf="@id/item_my_feedback_title_TextView"
        app:layout_constraintRight_toLeftOf="@id/item_my_feedback_message_count_Layout"
        app:layout_constraintTop_toBottomOf="@id/item_my_feedback_title_TextView"
        tools:text="2021-12-1 12:09" />

    <com.aquila.lib.widget.group.GroupImageTextLayout
        android:id="@+id/item_my_feedback_message_count_Layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="12dp"
        android:gravity="center"
        app:attr_image_height="15dp"
        app:attr_image_src="@drawable/ic_my_feedback_message"
        app:attr_image_width="15dp"
        app:attr_parent_orientation="IMAGE_LEFT_TEXT_RIGHT"
        app:attr_text="5"
        app:attr_text_color="?theme_text_color_gray"
        app:attr_text_margin_image_size="3dp"
        app:attr_text_size="12sp"
        android:visibility="visible"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/item_my_feedback_time_TextView"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/item_my_feedback_time_TextView" />


</androidx.constraintlayout.widget.ConstraintLayout>