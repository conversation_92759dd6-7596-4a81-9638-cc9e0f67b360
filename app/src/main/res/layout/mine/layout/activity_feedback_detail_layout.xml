<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_root_view_bg"
    android:fitsSystemWindows="true">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/feedback_detail_top_title_Layout"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="@string/feedback_detail"
        app:layout_constraintTop_toTopOf="parent" />

    <RelativeLayout
        android:id="@+id/feedback_detail_bottom_container_Layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?theme_bg_color_white"
        android:padding="15dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <Button
            android:id="@+id/feedback_detail_send_Button"
            android:layout_width="70dp"
            android:layout_height="40dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:background="?theme_bg_white_orange_btn"
            android:text="@string/send"
            android:textColor="?theme_text_color_white"
            android:textSize="16sp" />

        <EditText
            android:id="@+id/feedback_detail_content_EditText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginRight="10dp"
            android:layout_toLeftOf="@id/feedback_detail_send_Button"
            android:background="?theme_bg_white_conner_6dp_e3"
            android:gravity="center_vertical"
            android:hint="@string/please_input_suggestion"
            android:maxLines="3"
            android:minHeight="40dp"
            android:padding="9dp"
            android:textColor="?theme_text_color_black"
            android:textColorHint="?theme_text_color_gray_BC_88"
            android:textSize="14sp" />
    </RelativeLayout>

    <com.aquila.lib.layout.SmartRefreshLayout
        android:id="@+id/feedback_detail_data_RefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/feedback_detail_bottom_container_Layout"
        app:layout_constraintTop_toBottomOf="@+id/feedback_detail_top_title_Layout"
        app:srlEnableLoadMore="false"
        app:srlEnableRefresh="false">

        <com.aquila.lib.widget.view.CustomRecyclerView
            android:id="@+id/feedback_detail_data_RecyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:attr_divide_left_margin="15dp"
            app:attr_item_decoration_type="ITEM_DIVIDE_LNE"
            app:attr_item_divide_line_color="?theme_bg_color_divide_line_color"
            app:attr_item_divide_line_size="@dimen/divide_line_size"
            app:attr_item_margin="5dp"
            app:attr_layout_style="list_vertical"
            tools:itemCount="4"
            android:background="?theme_bg_color_white"
            tools:listitem="@layout/holder_item_feedback_detail_layout" />

    </com.aquila.lib.layout.SmartRefreshLayout>


</androidx.constraintlayout.widget.ConstraintLayout>