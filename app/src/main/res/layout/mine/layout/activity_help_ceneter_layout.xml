<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_root_view_bg"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/help_center_top_title_Layout"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="@string/help_center">

        <TextView
            android:id="@+id/help_center_i_will_feedback_TextView"
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            android:layout_alignParentRight="true"
            android:background="?theme_comm_click_bg"
            android:foreground="?selectableItemBackground"
            android:gravity="center"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:text="@string/i_will_feedback"
            android:textColor="?theme_text_color_black"
            android:textSize="16sp" />

        <ProgressBar
            android:id="@+id/help_center_loading_ProgressBar"
            style="?android:attr/progressBarStyle"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@id/help_center_i_will_feedback_TextView"
            android:visibility="gone"
            tools:visibility="visible" />


        <com.aquila.lib.widget.view.DotView
            android:id="@+id/help_center_flag_DotView"
            android:layout_width="5dp"
            android:layout_height="5dp"
            android:layout_alignTop="@id/help_center_i_will_feedback_TextView"
            android:layout_alignRight="@id/help_center_i_will_feedback_TextView"
            android:layout_marginTop="8dp"
            android:layout_marginRight="5dp"
            android:visibility="gone"
            app:attr_dot_color="?theme_text_color_red"
            app:attr_dot_radius="5dp"
            tools:visibility="visible" />

    </com.wedevote.wdbook.ui.widgets.CommTopTitleLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <com.aquila.lib.widget.view.CustomRecyclerView
                android:id="@+id/help_center_data_RecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:attr_divide_left_margin="15dp"
                app:attr_is_expanded="true"
                app:attr_item_decoration_type="ITEM_DIVIDE_LNE"
                app:attr_item_divide_line_color="?theme_bg_color_divide_line_color"
                app:attr_item_divide_line_size="@dimen/divide_line_size"
                app:attr_layout_style="list_vertical"
                tools:itemCount="2"
                tools:listitem="@layout/holder_item_faq_category_list_layout"
                tools:visibility="visible" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>