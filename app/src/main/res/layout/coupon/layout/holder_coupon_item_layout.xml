<?xml version="1.0" encoding="utf-8"?>

<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="100dp"
    android:layout_marginLeft="15dp"
    android:layout_marginTop="5dp"
    android:layout_marginRight="15dp"
    android:layout_marginBottom="5dp"
    app:cardBackgroundColor="?theme_bg_color_white"
    app:cardCornerRadius="3dp"
    app:cardElevation="3dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingRight="15dp">

        <TextView
            android:id="@+id/item_coupon_money_TextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:gravity="center"
            android:textColor="?theme_text_color_orange"
            android:textSize="30sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="4.12" />


        <TextView
            android:id="@+id/item_coupon_name_TextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="15dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:text="指定商品优惠券"
            android:textColor="?theme_text_color_black"
            android:textSize="14sp"
            app:layout_constrainedWidth="true"
            app:layout_constraintLeft_toRightOf="@id/item_coupon_money_TextView"
            app:layout_constraintRight_toLeftOf="@id/item_coupon_dash_line_View"
            app:layout_constraintTop_toBottomOf="@id/item_coupon_active_date_TextView"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="指定商品优惠券指" />


        <TextView
            android:id="@+id/item_coupon_active_date_TextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:layout_marginBottom="15dp"
            android:maxLines="2"
            android:text="2022.6.1-2022.10.30"
            android:textColor="?theme_text_color_black"
            android:textSize="12sp"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="@id/item_coupon_name_TextView"
            app:layout_constraintRight_toLeftOf="@id/item_coupon_dash_line_View" />

        <FrameLayout
            android:id="@+id/item_coupon_status_container_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <Button
                android:id="@+id/item_coupon_use_coupon_Button"
                android:layout_width="60dp"
                android:layout_height="22dp"
                android:background="@drawable/selector_stoke_all_conner_orange"
                android:text="@string/use_coupon"
                android:textColor="?theme_text_color_orange"
                android:textSize="12sp"
                android:visibility="visible"
                app:layout_constraintRight_toRightOf="parent"
                tools:ignore="TouchTargetSizeCheck"
                tools:visibility="gone" />

            <ImageView
                android:id="@+id/item_coupon_radio_check_ImageView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="5dp"
                android:scaleType="center"
                android:src="@drawable/selector_ic_radio_check_circle"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/item_coupon_status_ImageView"
                android:layout_width="60dp"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_coupon_has_get"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />
        </FrameLayout>

        <com.wedevote.wdbook.ui.widgets.CouponDashLineView
            android:id="@+id/item_coupon_dash_line_View"
            android:layout_width="20dp"
            android:layout_height="match_parent"
            android:layout_marginRight="10dp"
            app:attr_conner_color="?theme_root_view_bg"
            app:attr_conner_radius="7dp"
            app:attr_dash_color="?theme_bg_color_divide_line_color"
            app:attr_dash_size="5dp"
            app:attr_dash_space="4dp"
            app:attr_dash_width="1dp"
            app:layout_constraintRight_toLeftOf="@id/item_coupon_status_container_layout" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>