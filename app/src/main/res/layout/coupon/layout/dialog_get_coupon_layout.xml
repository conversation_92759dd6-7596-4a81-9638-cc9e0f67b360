<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="?theme_bg_color_white_black"
    android:padding="0dp">

    <TextView
        android:id="@+id/get_coupon_title_TextView"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:gravity="center"
        android:text="@string/can_use_coupon"
        android:textColor="?theme_text_color_black"
        android:textSize="16sp"
        app:layout_constraintTop_toTopOf="parent" />

    <com.aquila.lib.widget.view.CustomRecyclerView
        android:id="@+id/get_coupon_data_RecyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="15dp"
        app:attr_end_item_margin="15dp"
        app:attr_item_margin="15dp"
        app:attr_layout_style="list_vertical"
        app:attr_start_item_margin="15dp"
        app:layout_constraintBottom_toTopOf="@id/get_coupon_ok_Button"
        app:layout_constraintHeight_max="550dp"
        app:layout_constraintTop_toBottomOf="@id/get_coupon_title_TextView"
        tools:itemCount="1"
        tools:listitem="@layout/holder_coupon_item_layout" />

    <Button
        android:id="@+id/get_coupon_ok_Button"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_marginLeft="30dp"
        android:layout_marginRight="30dp"
        android:layout_marginBottom="15dp"
        android:background="@drawable/selector_all_conner_brown_bg"
        android:text="@string/label_OK"
        android:textColor="?theme_text_color_white"
        android:textSize="16sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout>