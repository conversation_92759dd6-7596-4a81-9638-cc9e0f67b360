<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_root_view_bg"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/coupon_detail_top_title_Layout"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="@string/title_coupon_detail" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/coupon_detail_container_ScrollView"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <FrameLayout
                android:id="@+id/coupon_detail_data_container_Layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="15dp"
                android:paddingBottom="15dp">

                <include layout="@layout/holder_coupon_item_layout" />
            </FrameLayout>

            <TextView
                android:id="@+id/coupon_detail_desc_TextView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:lineSpacingMultiplier="1.25"
                android:padding="15dp"
                android:text="@string/coupon_use_desc"
                android:textColor="?theme_text_color_black"
                android:textSize="16sp" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>


</LinearLayout>