<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_root_view_bg"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/coupon_center_top_title_Layout"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="@string/title_coupon"
        app:attr_title_text_color="?theme_text_color_black"
        app:attr_title_text_size="16sp" />

    <com.aquila.lib.widget.view.TabLinearLayout
        android:id="@+id/coupon_center_TabLinearLayout"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="?theme_bg_color_white"
        android:orientation="horizontal"
        app:app_indicator_color="@color/color_orange_FF8A00"
        app:app_indicator_height="2dp"
        app:app_indicator_position="1"
        app:app_indicator_width="35dp"
        app:tabBackground="?theme_bg_color_white"
        app:tabIndicatorAnimationMode="linear"
        app:tabIndicatorHeight="3dp"
        app:tabSelectedTextColor="@color/color_orange_FF8A00">

        <TextView
            android:id="@+id/coupon_center_tab_active_TextView"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:text="@string/coupon_usable"
            android:textColor="@color/color_orange_FF8A00"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/coupon_center_tab_used_TextView"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:text="@string/coupon_has_used"
            android:textColor="@color/color_orange_FF8A00"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/coupon_center_tab_expired_TextView"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:text="@string/coupon_expired"
            android:textColor="@color/color_orange_FF8A00"
            android:textSize="16sp" />

    </com.aquila.lib.widget.view.TabLinearLayout>

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/coupon_center_content_ViewPager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingTop="15dp"
        tools:ignore="SpeakableTextPresentCheck" />


</LinearLayout>