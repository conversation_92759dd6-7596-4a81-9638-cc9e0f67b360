<?xml version="1.0" encoding="utf-8"?>

<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="350dp"
        android:layout_gravity="bottom"
        android:background="?theme_bg_color_white_1E"
        android:paddingLeft="15dp"
        android:paddingTop="15dp"
        android:paddingRight="15dp">

        <TextView
            android:id="@+id/dialog_footnote_title_TextView"
            android:layout_width="0dp"
            android:layout_height="35dp"
            android:layout_marginLeft="9dp"
            android:gravity="center_vertical"
            android:text="@string/footnote"
            android:textColor="?theme_text_color_black_24"
            android:textSize="16sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/dialog_footnote_close_ImageView"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/dialog_footnote_close_ImageView"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_alignParentTop="true"
            android:layout_alignParentRight="true"
            android:background="?theme_comm_click_bg"
            android:foreground="?android:attr/selectableItemBackground"
            android:padding="15dp"
            android:scaleType="center"
            android:src="?theme_dialog_close_ImageView"
            android:textSize="16sp"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:paddingLeft="21dp"
            android:paddingRight="21dp"
            app:layout_constraintTop_toBottomOf="@id/dialog_footnote_title_TextView">

            <TextView
                android:id="@+id/dialog_footnote_content_TextView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:lineSpacingMultiplier="1.1"
                android:paddingBottom="50dp"
                android:textColor="?attr/theme_text_color_black"
                android:textSize="18sp"
                tools:text="这里是标题，这里只有正文没有标题" />
        </androidx.core.widget.NestedScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>