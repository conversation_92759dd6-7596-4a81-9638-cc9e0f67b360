<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_bg_color_white_121417">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/book_shelf_top_title_layout"
        android:layout_width="match_parent"
        android:layout_height="59dp"
        android:background="?theme_bg_color_white_121417"
        app:attr_hide_back="true"
        app:attr_title_text_size="16sp"
        app:attr_is_text_bold="true"
        app:attr_title_text_color="?theme_text_color_black_17191c"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/book_shelf_select_all_TextView"
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            android:layout_alignParentStart="true"
            android:background="@drawable/selector_comm_click_bg"
            android:foreground="?android:attr/selectableItemBackground"
            android:gravity="center"
            android:paddingStart="16dp"
            android:paddingEnd="10dp"
            android:text="@string/select_all"
            android:textColor="?theme_text_color_black"
            android:textSize="16sp"
            android:visibility="gone"
            tools:visibility="gone" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:gravity="center_vertical"
            android:orientation="horizontal">
            <ImageView
                android:id="@+id/book_shelf_search_ImageView"
                android:layout_width="43dp"
                android:layout_height="50dp"
                android:padding="12dp"
                android:background="?theme_comm_click_bg"
                android:foreground="?android:attr/selectableItemBackground"
                android:layout_marginEnd="6dp"
                android:src="@drawable/ic_search_gray"
                app:tint="?theme_icon_tint_color"
                android:visibility="gone"
                tools:visibility="visible"
                tools:ignore="UseAppTint" />
            <TextView
                android:id="@+id/book_shelf_edit_TextView"
                android:layout_width="wrap_content"
                android:layout_height="50dp"
                android:gravity="center"
                android:paddingStart="10dp"
                android:paddingEnd="16dp"
                android:text="@string/edit"
                android:textColor="?theme_text_color_black"
                android:textSize="16sp"
                android:visibility="gone"
                tools:visibility="visible" />
        </LinearLayout>

    </com.wedevote.wdbook.ui.widgets.CommTopTitleLayout>

    <Button
        android:id="@+id/book_shelf_move_folder_Button"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_alignParentBottom="true"
        android:background="?theme_bg_color_white_top_line"
        android:foreground="?android:selectableItemBackground"
        android:paddingLeft="5dp"
        android:paddingRight="5dp"
        android:text="@string/team_up"
        android:textColor="?theme_text_color_blue_book_notes"
        android:textSize="16sp"
        android:visibility="gone"
        tools:visibility="visible" />

    <com.aquila.lib.layout.SmartRefreshLayout
        android:id="@+id/book_shelf_data_container_RefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/book_shelf_move_folder_Button"
        android:layout_below="@id/book_shelf_top_title_layout"
        android:visibility="visible"
        tools:visibility="gone">

        <com.aquila.lib.widget.view.CustomRecyclerView
            android:id="@+id/book_shelf_data_RecyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginLeft="13dp"
            android:layout_marginRight="13dp"
            app:attr_divide_left_margin="15dp"
            app:attr_divide_right_margin="15dp"
            app:attr_item_decoration_type="ITEM_DIVIDE_LNE"
            app:attr_item_divide_line_color="@color/divide_line_color"
            app:attr_item_divide_line_size="@dimen/divide_line_size"
            app:attr_layout_style="grid_vertical"
            app:attr_span_count="3"
            app:attr_end_item_margin="30dp"
            tools:itemCount="13"
            tools:listitem="@layout/holder_book_shelf_item_layout" />

    </com.aquila.lib.layout.SmartRefreshLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/book_shelf_empty_container_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/book_shelf_top_title_layout"
        android:layout_centerHorizontal="true"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="gone">

        <com.aquila.lib.widget.group.GroupImageTextLayout
            android:id="@+id/book_shelf_empty_icon_Layout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:elevation="10dp"
            android:gravity="center_horizontal"
            app:attr_image_height="120dp"
            app:attr_image_src="?theme_ic_empty_book_shelf"
            app:attr_image_width="120dp"
            app:attr_parent_orientation="IMAGE_TOP_TEXT_BOTTOM"
            app:attr_text="@string/no_book_on_bookshelf"
            app:attr_text_color="?theme_text_color_black"
            app:attr_text_margin_image_size="20dp"
            app:attr_text_size="16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.2"
            app:layout_constraintWidth_percent="0.4" />

        <Button
            android:id="@+id/book_shelf_goto_store_Button"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_marginLeft="32dp"
            android:layout_marginTop="42dp"
            android:layout_marginRight="32dp"
            android:background="@drawable/selector_button_bg_brown"
            android:text="@string/goto_book_store"
            android:textColor="?theme_text_color_white"
            android:textSize="16sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/book_shelf_empty_icon_Layout"
            app:layout_constraintWidth_percent="0.4" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.wedevote.wdbook.ui.widgets.WidgetLoadingLayout
        android:id="@+id/book_shelf_loading_Layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:visibility="gone"
        tools:visibility="visible" />

</RelativeLayout>