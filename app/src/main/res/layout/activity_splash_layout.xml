<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_bg_color_white"
    android:fitsSystemWindows="true">

    <ImageView
        android:id="@+id/splash_logo_ImageView"
        android:layout_width="94dp"
        android:layout_height="94dp"
        android:src="@drawable/app_logo"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.3" />

    <TextView
        android:id="@+id/splash_app_name_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/app_name"
        android:textColor="?theme_text_color_black"
        android:textSize="24sp"
        app:layout_constraintLeft_toLeftOf="@id/splash_logo_ImageView"
        app:layout_constraintRight_toRightOf="@id/splash_logo_ImageView"
        app:layout_constraintTop_toBottomOf="@id/splash_logo_ImageView" />

</androidx.constraintlayout.widget.ConstraintLayout>