<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="?theme_bg_color_white"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/comm_web_top_title_Layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/top_title_height"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="标题"
        app:layout_constraintTop_toTopOf="parent" />

    <ProgressBar
        android:id="@+id/comm_web_loading_ProgressBar"
        style="@android:style/Widget.ProgressBar.Horizontal"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:elevation="10dp"
        android:max="100"
        android:progressDrawable="@drawable/layer_list_progress_drawable"
        app:layout_constraintTop_toBottomOf="@id/comm_web_top_title_Layout"
        tools:progress="20" />

    <com.wedevote.wdbook.ui.widgets.JsWebView
        android:id="@+id/comm_web_WebView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/comm_web_top_title_Layout"
        tools:visibility="gone" />


    <com.aquila.lib.widget.group.GroupImageTextLayout
        android:id="@+id/comm_web_empty_Layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingTop="50dp"
        android:paddingBottom="50dp"
        android:visibility="gone"
        app:attr_text="@string/loading_failure_try_again"
        app:attr_text_color="?theme_text_color_dark"
        app:attr_text_size="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/comm_web_top_title_Layout"
        app:layout_constraintVertical_bias="0.25"
        tools:visibility="visible" />


</androidx.constraintlayout.widget.ConstraintLayout>