<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_bg_color_white_1E"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <TextView
        android:id="@+id/activity_edit_note_finish_TextView"
        android:layout_width="wrap_content"
        android:layout_height="50dp"
        android:background="?theme_comm_click_bg"
        android:foreground="?android:attr/selectableItemBackground"
        android:gravity="center"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:text="@string/finish"
        android:textColor="?theme_text_color_black"
        android:textSize="16sp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/activity_edit_note_divide_line_View"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="?theme_bg_color_divide_line_color"
        app:layout_constraintTop_toBottomOf="@id/activity_edit_note_finish_TextView" />

    <View
        android:id="@+id/activity_edit_note_mark_color_View"
        android:layout_width="4dp"
        android:layout_height="0dp"
        android:layout_marginLeft="15dp"
        app:layout_constraintBottom_toBottomOf="@id/activity_edit_note_book_text_TextView"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/activity_edit_note_book_text_TextView"
        tools:background="@color/color_red_C13013" />

    <TextView
        android:id="@+id/activity_edit_note_book_text_TextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="25dp"
        android:layout_marginTop="9dp"
        android:layout_marginRight="15dp"
        android:ellipsize="end"
        android:maxLines="3"
        android:gravity="left"
        android:textDirection="ltr"
        android:textColor="?theme_text_color_note_gray"
        android:textSize="12sp"
        app:layout_constraintLeft_toRightOf="@id/activity_edit_note_mark_color_View"
        app:layout_constraintTop_toTopOf="@id/activity_edit_note_divide_line_View"
        tools:text="盛大开放和代理商卡积分换禄口街道撒化肥禄口街道杀戮空间发多少垃圾筐sfsfasfasfa盛大开放和代理商卡积分换禄口街道撒化肥禄口街道杀戮空间发多少垃圾筐sfsfasfasfa盛大开放和代理商卡积分换禄口街道撒化肥禄口街道杀戮空间发多少垃圾筐s" />

    <TextView
        android:id="@+id/activity_edit_note_warning_TextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginRight="15dp"
        android:gravity="center_vertical"
        android:maxLength="20000"
        android:paddingLeft="15dp"
        android:paddingTop="5dp"
        android:paddingBottom="5dp"
        android:text="@string/text_oversize_warning"
        android:textColor="?theme_text_color_red"
        android:textSize="12sp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/activity_edit_note_book_text_TextView"
        tools:visibility="visible" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:elevation="10dp"
        android:fillViewport="true"
        android:overScrollMode="never"
        android:padding="15dp"
        android:scrollbarSize="4dp"
        android:scrollbarStyle="insideOverlay"
        android:scrollbars="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/activity_edit_note_warning_TextView">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <EditText
                android:id="@+id/activity_edit_note_desc_EditText"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/transparent"
                android:gravity="top|left"
                android:lineSpacingMultiplier="1.05"
                android:maxLength="200001"
                android:textDirection="ltr"
                android:paddingBottom="50dp"
                android:textColor="?theme_text_color_note_black"
                android:textColorHint="?theme_text_color_gray_BC_88"
                android:textSize="14sp"
                tools:text="哈克斯暖风机阿什拉夫了哈克斯暖风机阿什拉夫了哈克斯暖风机阿什拉夫了哈克斯暖风机阿什拉夫了 " />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
    <!--    </androidx.constraintlayout.widget.ConstraintLayout>-->
</androidx.constraintlayout.widget.ConstraintLayout>