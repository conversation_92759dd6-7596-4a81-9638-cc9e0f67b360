<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/text_select_summery_top_TextView"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_gravity="center_horizontal"
        android:background="?theme_shape_black_conner_5dp_bg"
        android:ellipsize="end"
        android:maxLines="1"
        android:padding="10dp"
        android:textColor="?theme_text_color_white"
        android:textSize="12sp"
        android:visibility="visible"
        android:gravity="left"
        android:textDirection="ltr"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@id/text_select_triangle_flag_top_ImageView"
        app:layout_constraintLeft_toLeftOf="@id/text_select_option_main_container_layout"
        app:layout_constraintRight_toRightOf="@id/text_select_option_main_container_layout"
        tools:text="上上上上上上上上上上上上上"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/text_select_triangle_flag_top_ImageView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_gravity="center_horizontal"
        android:rotation="180"
        android:src="@drawable/ic_triangle_black"
        app:tint="?theme_color_black_444"
        android:visibility="visible"
        app:layout_constraintLeft_toLeftOf="@id/text_select_option_main_container_layout"
        app:layout_constraintRight_toRightOf="@id/text_select_option_main_container_layout"
        app:layout_constraintTop_toBottomOf="@id/text_select_summery_top_TextView"
        tools:ignore="UseAppTint"
        tools:visibility="visible" />

    <FrameLayout
        android:id="@+id/text_select_option_main_container_layout"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:background="?theme_shape_black_conner_5dp_bg"
        app:layout_constrainedWidth="true"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/text_select_triangle_flag_top_ImageView">
        <!--选择文字操作的菜单Container layout-->
        <LinearLayout
            android:id="@+id/text_select_option_menu_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:visibility="visible"
            tools:visibility="visible">

            <LinearLayout
                android:id="@+id/text_select_option_first_menu_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:visibility="visible"
                tools:visibility="gone">

                <TextView
                    android:id="@+id/text_select_option_first_copy_TextView"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:minWidth="50dp"
                    android:paddingLeft="5dp"
                    android:paddingRight="5dp"
                    android:text="@string/label_copy"
                    android:textColor="@color/white"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/text_select_option_draw_line_TextView"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:minWidth="50dp"
                    android:paddingLeft="5dp"
                    android:paddingRight="5dp"
                    android:text="@string/label_draw_line"
                    android:textColor="@color/white"
                    android:textSize="14sp" />
            </LinearLayout>
            <!--第二级菜单的UI-->
            <LinearLayout
                android:id="@+id/text_select_option_second_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:visibility="visible"
                tools:visibility="visible">

                <ImageView
                    android:id="@+id/text_select_option_color_combine_ImageView"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_gravity="center_vertical"
                    android:minWidth="50dp"
                    android:scaleType="center"
                    android:src="@drawable/layer_list_highlight_circles" />

                <TextView
                    android:id="@+id/text_select_option_delete_line_TextView"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:minWidth="50dp"
                    android:paddingLeft="5dp"
                    android:paddingRight="5dp"
                    android:text="@string/delete_highlight"
                    android:textColor="@color/white"
                    android:textSize="14sp" />
            </LinearLayout>

            <TextView
                android:id="@+id/text_select_option_note_textView"
                android:layout_width="50dp"
                android:layout_height="40dp"
                android:gravity="center"
                android:minWidth="50dp"
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                android:text="@string/notes"
                android:textColor="@color/white"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/text_select_option_second_copy_TextView"
                android:layout_width="50dp"
                android:layout_height="40dp"
                android:gravity="center"
                android:minWidth="50dp"
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                android:text="@string/label_copy"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:visibility="visible" />

            <TextView
                android:id="@+id/text_select_option_share_TextView"
                android:layout_width="50dp"
                android:layout_height="40dp"
                android:gravity="center"
                android:minWidth="50dp"
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                android:text="@string/share"
                android:textColor="@color/white"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/text_select_option_correction_TextView"
                android:layout_width="50dp"
                android:layout_height="40dp"
                android:gravity="center"
                android:minWidth="50dp"
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                android:text="@string/correct"
                android:textColor="@color/white"
                android:textSize="14sp" />
        </LinearLayout>

        <!--选择高亮颜色的页面-->
        <LinearLayout
            android:id="@+id/text_select_option_highlight_container_layout"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:visibility="gone"
            tools:visibility="gone">

            <com.wedevote.wdbook.ui.widgets.CircleBGImageView
                android:id="@+id/highlight_color_red_View"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="5dp"
                android:layout_weight="1"
                android:padding="5dp"
                app:attr_circle_bg_color="#FDA4A5"
                app:attr_selected="false"
                app:attr_stoke_color="@color/white"
                app:attr_stoke_size="3dp" />

            <com.wedevote.wdbook.ui.widgets.CircleBGImageView
                android:id="@+id/highlight_color_blue_View"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="5dp"
                android:layout_weight="1"
                android:padding="5dp"
                app:attr_circle_bg_color="#A5C5FE"
                app:attr_selected="false"
                app:attr_stoke_color="@color/white"
                app:attr_stoke_size="3dp" />

            <com.wedevote.wdbook.ui.widgets.CircleBGImageView
                android:id="@+id/highlight_color_yellow_View"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="5dp"
                android:layout_weight="1"
                android:padding="5dp"
                app:attr_circle_bg_color="#F2E165"
                app:attr_selected="false"
                app:attr_stoke_color="@color/white"
                app:attr_stoke_size="3dp" />

            <com.wedevote.wdbook.ui.widgets.CircleBGImageView
                android:id="@+id/highlight_color_orange_View"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="5dp"
                android:layout_weight="1"
                android:padding="5dp"
                app:attr_circle_bg_color="#FDB75D"
                app:attr_selected="true"
                app:attr_stoke_color="@color/white"
                app:attr_stoke_size="3dp" />

            <com.wedevote.wdbook.ui.widgets.CircleBGImageView
                android:id="@+id/highlight_color_draw_line_View"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="5dp"
                android:layout_weight="1"
                android:padding="5dp"
                android:src="@drawable/ic_text_select_draw_line"
                app:attr_selected="false" />
        </LinearLayout>
    </FrameLayout>


    <ImageView
        android:id="@+id/text_select_triangle_flag_bottom_ImageView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_gravity="center_horizontal"
        android:src="@drawable/ic_triangle_black"
        app:tint="?theme_color_black_444"
        android:visibility="visible"
        app:layout_constraintLeft_toLeftOf="@id/text_select_option_main_container_layout"
        app:layout_constraintRight_toRightOf="@id/text_select_option_main_container_layout"
        app:layout_constraintTop_toBottomOf="@id/text_select_option_main_container_layout"
        tools:ignore="UseAppTint"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/text_select_summery_bottom_TextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:background="?theme_shape_black_conner_5dp_bg"
        android:ellipsize="end"
        android:maxLines="1"
        android:padding="10dp"
        android:gravity="left"
        android:textDirection="ltr"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:visibility="visible"
        app:layout_constrainedWidth="true"
        app:layout_constraintLeft_toLeftOf="@id/text_select_option_main_container_layout"
        app:layout_constraintRight_toRightOf="@id/text_select_option_main_container_layout"
        app:layout_constraintTop_toBottomOf="@id/text_select_triangle_flag_bottom_ImageView"
        tools:text="下下下下下下下下下下下下"
        tools:visibility="visible" />


</androidx.constraintlayout.widget.ConstraintLayout>





