<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_bg_color_white"
    android:fitsSystemWindows="true">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/top_title_height"
        app:attr_title_text="@string/title_about_us"
        app:layout_constraintTop_toTopOf="parent" />

    <com.aquila.lib.widget.group.GroupImageTextLayout
        android:id="@+id/about_logo_Layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:attr_image_height="94dp"
        app:attr_image_src="@drawable/app_logo"
        app:attr_image_width="94dp"
        app:attr_parent_orientation="IMAGE_TOP_TEXT_BOTTOM"
        app:attr_text="@string/app_name"
        app:attr_text_color="?theme_text_color_black"
        app:attr_text_margin_image_size="20dp"
        app:attr_text_size="24sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.25" />

    <TextView
        android:id="@+id/about_version_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:textColor="?theme_text_color_black_4c"
        android:textSize="14sp"
        app:layout_constraintLeft_toLeftOf="@id/about_logo_Layout"
        app:layout_constraintRight_toRightOf="@id/about_logo_Layout"
        app:layout_constraintTop_toBottomOf="@id/about_logo_Layout"
        tools:text="V1.0" />

    <TextView
        android:id="@+id/about_copyright_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:gravity="center"
        android:lineSpacingMultiplier="1.5"
        android:text="@string/about_all_rights_reserved"
        android:textColor="?theme_text_color_black_8D97A4"
        android:textSize="12sp"
        app:layout_constraintBottom_toTopOf="@id/about_protocol_TextView"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/about_protocol_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="15dp"
        android:paddingLeft="10dp"
        android:paddingTop="10dp"
        android:paddingRight="4dp"
        android:paddingBottom="10dp"
        android:text="@string/text_user_agreement"
        android:textColor="?theme_text_color_black_8D97A4"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/guideline" />

    <TextView
        android:id="@+id/about_privacy_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="15dp"
        android:paddingLeft="4dp"
        android:paddingTop="10dp"
        android:paddingRight="10dp"
        android:paddingBottom="10dp"
        android:text="@string/text_privacy_agreement"
        android:textColor="?theme_text_color_black_8D97A4"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/about_protocol_TextView" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />
</androidx.constraintlayout.widget.ConstraintLayout>