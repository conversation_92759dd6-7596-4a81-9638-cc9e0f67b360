<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="?theme_bg_color_white">

    <com.wedevote.wdbook.ui.home.HomeBottomNavigationLayout
        android:id="@+id/home_main_bottom_navigation_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="?theme_bg_color_white_top_line"
        android:minHeight="60dp"
        android:paddingBottom="5dp" />


    <FrameLayout
        android:id="@+id/home_main_container_Layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/home_main_bottom_navigation_layout" />

</RelativeLayout>