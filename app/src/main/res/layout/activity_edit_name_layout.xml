<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_root_view_bg"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/setting_top_title_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/top_title_height"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="@string/name"
        app:layout_constraintTop_toTopOf="parent" />
<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="21dp"
    android:layout_marginRight="21dp"
    android:layout_marginTop="24dp"
    android:background="?theme_shape_corner_gray"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingLeft="16dp"
    >
    <EditText
        android:id="@+id/edit_name_EditText"
        android:layout_width="match_parent"
        android:layout_weight="1"
        android:layout_height="@dimen/title_height"
        android:background="@color/transparent"
        android:hint="@string/please_input_name"
        android:cursorVisible="true"
        android:maxLength="31"
        android:maxLines="1"
        android:inputType="text"
        android:paddingEnd="16dp"
        android:visibility="visible"
        android:scrollHorizontally="true"
        android:textColor="?theme_text_color_black_373636"
        android:textColorHint="?theme_text_color_gray_BC_88"
        android:textCursorDrawable="@null"
        android:textSize="@dimen/text_size_16" />

    <ImageView
        android:id="@+id/clear_text_ImageView"
        android:layout_width="52dp"
        android:layout_height="match_parent"
        android:adjustViewBounds="true"
        android:scaleType="fitCenter"
        android:paddingRight="16dp"
        android:paddingLeft="16dp"
        android:visibility="gone"
        tools:visibility="visible"
        android:src="@drawable/ic_clear_text" />
</LinearLayout>

    <TextView
        android:id="@+id/edit_name_confirm_TextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginBottom="16dp"
        android:enabled="false"
        android:background="@drawable/selector_all_conner_brown_bg"
        android:gravity="center_horizontal"
        android:padding="13dp"
        android:text="@string/label_OK"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:textSize="16sp" />

</LinearLayout>