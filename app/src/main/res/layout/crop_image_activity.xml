<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#c000"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_height"
        android:background="#111">

        <TextView
            android:id="@+id/crop_back_view"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:background="@drawable/selector_imageview_background"
            android:gravity="center"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:textColor="@color/white" />

        <TextView
            android:id="@+id/crop_save_TextView"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:background="@drawable/selector_imageview_background"
            android:gravity="center"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:textColor="@color/white" />

        <ImageView
            android:id="@+id/crop_rotate_ImageView"
            android:layout_width="@dimen/title_height"
            android:layout_height="@dimen/title_height"
            android:layout_toLeftOf="@id/crop_save_TextView"
            android:background="@drawable/selector_imageview_background"
            android:padding="8dp"
            android:scaleType="fitCenter"
            android:src="@drawable/crop_image_menu_rotate_right" />


    </RelativeLayout>

    <com.wedevote.wdbook.tools.util.crop.CropImageView
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/cropImageView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</LinearLayout>
