<?xml version="1.0" encoding="utf-8"?>


<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    app:cardBackgroundColor="?theme_bg_color_white_1E"
    app:cardCornerRadius="8dp"
    tools:layout_width="300dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="10dp">

        <TextView
            android:id="@+id/new_version_title_TextView"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:gravity="center"
            android:text="@string/find_new_version"
            android:textColor="?theme_text_color_black"
            android:textSize="16sp"
            app:layout_constraintBottom_toTopOf="@id/new_version_container_ScrollView"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/new_version_button_container_Layout"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="?theme_bg_color_white_top_line"
            android:divider="?theme_bg_color_divide_line_color"
            android:orientation="horizontal"
            android:showDividers="middle"
            app:layout_constraintTop_toBottomOf="@id/new_version_container_ScrollView">

            <Button
                android:id="@+id/new_version_cancel_Button"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_weight="1"
                android:background="?theme_comm_click_bg"
                android:gravity="center"
                android:text="@string/next_time_prompt"
                android:textColor="@color/text_color_blue_007AFF"
                android:textSize="16sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@id/new_version_ok_Button"
                app:layout_constraintTop_toBottomOf="@id/new_version_container_ScrollView" />

            <Button
                android:id="@+id/new_version_ignore_Button"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_weight="1"
                android:background="?theme_comm_click_bg"
                android:gravity="center"
                android:text="@string/never_remind"
                android:textColor="@color/text_color_blue_007AFF"
                android:textSize="16sp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@id/new_version_cancel_Button"
                app:layout_constraintRight_toRightOf="parent" />

            <Button
                android:id="@+id/new_version_ok_Button"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_weight="1"
                android:background="?theme_comm_click_bg"
                android:gravity="center"
                android:text="@string/update_now"
                android:textColor="@color/text_color_blue_007AFF"
                android:textSize="16sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/new_version_cancel_Button"
                app:layout_constraintRight_toRightOf="parent" />

        </LinearLayout>


        <androidx.core.widget.NestedScrollView
            android:id="@+id/new_version_container_ScrollView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="60dp"
            app:layout_constrainedHeight="true"
            app:layout_constraintBottom_toBottomOf="@id/new_version_button_container_Layout"
            app:layout_constraintHeight_max="350dp"
            app:layout_constraintTop_toBottomOf="@id/new_version_title_TextView">

            <TextView
                android:id="@+id/new_version_describute_TextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center_vertical"
                android:lineSpacingMultiplier="1.2"
                android:minHeight="50dp"
                android:paddingLeft="25dp"
                android:paddingTop="15dp"
                android:paddingRight="25dp"
                android:paddingBottom="15dp"
                android:textColor="?theme_text_color_black"
                android:textSize="16sp"
                tools:text="发现V1.1版本，是否去升级发现V1.1版本，发现V1.1版本，是否去升级发现V1.1版本，发现V1.1版本，是否去升级发现V1.1版本，发现V1.1版本，是否去升级发现V1.1版本，发现V1.1版本，是否去升级发现V1.1版本，发现V1.1版本，是否去升级发现V1.1版本，发现V1.1版本，是否去升级发现V1.1版本，" />
        </androidx.core.widget.NestedScrollView>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>
