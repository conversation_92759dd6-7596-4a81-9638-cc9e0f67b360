<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="10dp"
    android:gravity="center"
    android:foreground="?android:attr/selectableItemBackground"
    >

    <TextView
        android:id="@+id/item_bottom_single_choice_desc_TextView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:text="这个是标题"
        android:textColor="@color/color_blue_006FFF"
        android:textSize="14sp" />

    <ImageView
        android:id="@+id/item_bottom_single_choice_choice_ImageView"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:visibility="gone"
        android:scaleType="center"
        android:src="@drawable/ic_check_black" />


</LinearLayout>