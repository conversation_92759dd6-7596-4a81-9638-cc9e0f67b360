<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:layout_margin="10dp"
    android:background="?theme_bg_white_conner_8dp">

    <TextView
        android:id="@+id/pay_method_title_TextView"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:gravity="center"
        android:text="@string/select_pay_method"
        android:textColor="?attr/theme_text_color_black"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/pay_method_close_ImageView"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:scaleType="center"
        android:src="?theme_dialog_close_ImageView"
        app:layout_constraintBottom_toBottomOf="@id/pay_method_title_TextView"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/pay_method_title_TextView" />


    <com.aquila.lib.widget.group.GroupImageTextLayout
        android:id="@+id/pay_method_alipay_layout"
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:background="?theme_comm_click_bg"
        android:foreground="?attr/selectableItemBackground"
        android:gravity="center_vertical"
        android:paddingLeft="20dp"
        android:paddingRight="20dp"
        app:attr_image_src="@drawable/ic_payment_alipay"
        app:attr_parent_orientation="IMAGE_LEFT_TEXT_RIGHT"
        app:attr_text="@string/dialog_payment_info_alipay"
        app:attr_text_color="?theme_text_color_black"
        app:attr_text_margin_image_size="15dp"
        app:attr_text_size="16sp"
        app:layout_constraintTop_toBottomOf="@id/pay_method_title_TextView">

        <ImageView
            android:id="@+id/pay_method_alipay_check_status_ImageView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:src="@drawable/selected_paytype_select_box_yellow" />
    </com.aquila.lib.widget.group.GroupImageTextLayout>


    <com.aquila.lib.widget.group.GroupImageTextLayout
        android:id="@+id/pay_method_paypal_layout"
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:background="?theme_comm_click_bg"
        android:foreground="?attr/selectableItemBackground"
        android:gravity="center_vertical"
        android:paddingLeft="20dp"
        android:paddingRight="20dp"
        app:attr_image_src="@drawable/ic_payment_paypal"
        app:attr_parent_orientation="IMAGE_LEFT_TEXT_RIGHT"
        app:attr_text="@string/dialog_payment_info_paypal_pay"
        app:attr_text_color="?theme_text_color_black"
        app:attr_text_margin_image_size="15dp"
        app:attr_text_size="16sp"
        app:layout_constraintTop_toBottomOf="@id/pay_method_alipay_layout">

        <ImageView
            android:id="@+id/pay_method_paypal_check_status_ImageView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:src="@drawable/selected_paytype_select_box_yellow" />
    </com.aquila.lib.widget.group.GroupImageTextLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/pay_method_stripe_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="50dp"
        android:paddingLeft="20dp"
        android:paddingTop="10dp"
        android:paddingRight="20dp"
        android:paddingBottom="10dp"
        app:layout_constraintTop_toBottomOf="@id/pay_method_paypal_layout">

        <ImageView
            android:id="@+id/pay_method_credit_card_ImageView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="?theme_ic_credit_card"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/pay_method_credit_TextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:text="@string/dialog_payment_info_card_pay"
            android:textColor="?theme_text_color_black"
            android:textSize="16sp"
            app:layout_constraintLeft_toRightOf="@id/pay_method_credit_card_ImageView"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/pay_method_unbind_help_TextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:paddingTop="5dp"
            android:paddingBottom="5dp"
            android:text="@string/how_to_unbind_card"
            android:textColor="@color/text_color_blue_007AFF"
            app:layout_constraintLeft_toLeftOf="@id/pay_method_credit_TextView"
            app:layout_constraintTop_toBottomOf="@id/pay_method_credit_TextView" />

        <ImageView
            android:id="@+id/pay_method_stripe_check_status_ImageView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/selected_paytype_select_box_yellow"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <Button
        android:id="@+id/pay_method_pay_action_Button"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="20dp"
        android:layout_marginRight="15dp"
        android:background="@drawable/selector_all_conner_brown_bg"
        android:text="@string/ensure_pay"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:layout_marginBottom="15dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/pay_method_stripe_layout" />
</androidx.constraintlayout.widget.ConstraintLayout>