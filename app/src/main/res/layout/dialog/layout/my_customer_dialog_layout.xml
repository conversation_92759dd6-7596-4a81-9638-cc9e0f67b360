<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/transparent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?theme_bg_white_conner_8dp"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:divider="?theme_divide_line_horizontal"
            android:orientation="vertical"
            android:showDividers="middle">

            <TextView
                android:id="@+id/customer_dialog_title_TextView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:minHeight="@dimen/title_height"
                android:paddingLeft="@dimen/activity_horizontal_margin"
                android:paddingTop="@dimen/activity_horizontal_margin"
                android:paddingRight="@dimen/activity_horizontal_margin"
                android:paddingBottom="@dimen/activity_horizontal_margin"
                android:textColor="?theme_text_color_black_373636"
                android:textSize="@dimen/text_size_16" />

            <TextView
                android:id="@+id/customer_dialog_ok_TextView"
                android:layout_width="match_parent"
                android:layout_height="@dimen/title_height"
                android:gravity="center"
                android:paddingLeft="@dimen/activity_horizontal_margin"
                android:paddingRight="@dimen/activity_horizontal_margin"
                android:text="@string/label_OK"
                android:textColor="?theme_text_color_black_373636"
                android:textSize="@dimen/text_size_16" />
        </LinearLayout>

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="0.7dp"
            android:background="?theme_divide_line_horizontal" />
        <TextView
            android:layout_width="match_parent"
            android:layout_height="12dp"
            android:background="?theme_divide_line_color_17191C" />

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="0.7dp"
            android:background="?theme_divide_line_horizontal" />

        <TextView
            android:id="@+id/customer_dialog_cancel_TextView"
            android:layout_width="match_parent"
            android:layout_height="@dimen/title_height"
            android:gravity="center"
            android:paddingLeft="@dimen/activity_horizontal_margin"
            android:paddingRight="@dimen/activity_horizontal_margin"
            android:textColor="?theme_text_color_black_373636"
            android:textSize="@dimen/text_size_16" />
    </LinearLayout>
</FrameLayout>