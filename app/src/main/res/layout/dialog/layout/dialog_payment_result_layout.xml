<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:layout_margin="10dp"
    android:background="?theme_bg_white_conner_8dp"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="10dp">

    <TextView
        android:id="@+id/payment_result_label_TextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:minHeight="50dp"
        android:textColor="?theme_text_color_black"
        android:textSize="16sp"
        tools:text="购买成功" />

    <ImageView
        android:id="@+id/payment_result_icon_ImageView"
        android:layout_width="wrap_content"
        android:layout_height="90dp"
        android:scaleType="fitCenter"
        android:src="?theme_ic_pay_result_success" />

    <Button
        android:id="@+id/payment_result_OK_Button"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="15dp"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:background="?theme_bg_white_orange_btn"
        android:text="@string/label_OK"
        android:textColor="?theme_text_color_white"
        android:textSize="16sp" />


</LinearLayout>