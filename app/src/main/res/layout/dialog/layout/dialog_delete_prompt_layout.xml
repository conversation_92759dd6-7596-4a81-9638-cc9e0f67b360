<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_marginLeft="50dp"
    android:layout_marginRight="50dp"
    android:background="?theme_bg_white_conner_8dp"
    android:orientation="vertical"
    app:layout_constraintWidth_percent="0.7">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="25dp"
        android:gravity="center"
        android:minHeight="40dp"
        android:text="@string/delete_account_dialog_title"
        android:textColor="?theme_text_color_black"
        android:textSize="18sp"
        android:textStyle="bold" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:gravity="center"
        android:text="@string/deletet_account_tips"
        android:textColor="?theme_text_color_black"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/divide_line_size"
        android:layout_marginTop="20dp"
        android:background="?theme_bg_color_divide_line_color" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:divider="?theme_divide_line_vertical"
        android:orientation="horizontal"
        android:showDividers="middle">

        <TextView
            android:id="@+id/delete_account_cancel_TextView"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/label_cancel"
            android:textColor="?theme_text_color_blue"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/delete_account_request_TextView"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/request_delete_account"
            android:textColor="?theme_text_color_red"
            android:textSize="16sp" />


    </LinearLayout>


</LinearLayout>