<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="?theme_bg_white_conner_8dp"
    android:maxHeight="500dp"
    android:padding="10dp"
    tools:layout_margin="10dp">

    <TextView
        android:id="@+id/purchase_item_title_TextView"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:gravity="center"
        android:text="@string/dialog_purchased_title"
        android:textColor="?theme_text_color_black"
        android:textSize="16sp"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/divide_line_size"
        android:background="?theme_bg_color_divide_line_color"
        app:layout_constraintTop_toBottomOf="@id/purchase_item_title_TextView" />

    <ImageView
        android:id="@+id/purchase_item_close_ImageView"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:scaleType="center"
        android:src="?theme_ic_arrow_down"
        app:layout_constraintBottom_toBottomOf="@id/purchase_item_title_TextView"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/purchase_item_title_TextView"
        app:tint="?theme_icon_tint_color_white" />

    <com.aquila.lib.widget.view.CustomRecyclerView
        android:id="@+id/purchase_item_data_RecyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:attr_item_decoration_type="ITEM_DIVIDE_LNE"
        app:attr_item_divide_line_color="?theme_bg_color_divide_line_color"
        app:attr_item_divide_line_size="@dimen/divide_line_size"
        app:attr_layout_style="list_vertical"
        app:layout_constraintTop_toBottomOf="@id/purchase_item_title_TextView"
        tools:itemCount="13"
        tools:listitem="@layout/item_purchased_product_layout" />


</androidx.constraintlayout.widget.ConstraintLayout>