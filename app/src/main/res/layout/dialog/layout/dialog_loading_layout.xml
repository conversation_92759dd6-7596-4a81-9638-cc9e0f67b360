<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="140dp"
    android:layout_height="90dp"
    android:layout_gravity="center"
    android:background="@drawable/shape_conner_gray_8"
    android:gravity="center">

    <ImageView
        android:id="@+id/loading_progress_ImageView"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_centerHorizontal="true"
        android:background="@drawable/dialog_spinner"
        app:tint="@color/color_dark_1E1E1E" />

    <TextView
        android:id="@+id/loading_content_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/loading_progress_ImageView"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="10dp"
        android:text="@string/label_dialog_loading"
        android:textColor="?theme_text_color_white"
        android:textSize="14sp" />

</RelativeLayout>