<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?theme_bg_white_conner_8dp"
    android:orientation="vertical"
    tools:layout_gravity="center"
    tools:layout_width="350dp">

    <TextView
        android:id="@+id/book_group_title_TextView"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="?theme_bg_color_white_bottom_line"
        android:gravity="center"
        android:text="@string/dialog_book_group_title"
        android:textColor="?theme_text_color_black"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <Button
        android:id="@+id/book_group_cancel_Button"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="?theme_bg_color_white_top_line"
        android:gravity="center"
        android:text="@string/label_cancel"
        android:textColor="@color/text_color_blue_007AFF"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent" />

    <com.aquila.lib.widget.group.GroupImageTextLayout
        android:id="@+id/book_group_move_Layout"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:background="?theme_comm_click_bg"
        android:foreground="?android:selectableItemBackground"
        android:gravity="center_vertical"
        android:visibility="gone"
        app:attr_child_view_margin_start="20dp"
        app:attr_image_height="20dp"
        app:attr_image_src="@drawable/ic_remove_current_group"
        app:attr_image_width="20dp"
        app:attr_parent_orientation="IMAGE_LEFT_TEXT_RIGHT"
        app:attr_text="@string/remove_from_group"
        app:attr_text_color="@color/text_color_blue_007AFF"
        app:attr_text_margin_image_size="15dp"
        app:attr_text_size="16sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/book_group_title_TextView"
        tools:visibility="visible" />

    <com.aquila.lib.widget.group.GroupImageTextLayout
        android:id="@+id/book_group_create_new_Layout"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:background="?theme_comm_click_bg"
        android:foreground="?android:selectableItemBackground"
        android:gravity="center_vertical"
        app:attr_child_view_margin_start="20dp"
        app:attr_image_height="20dp"
        app:attr_image_src="@drawable/ic_create_book_group"
        app:attr_image_width="20dp"
        app:attr_parent_orientation="IMAGE_LEFT_TEXT_RIGHT"
        app:attr_text="@string/dialog_book_group_create_new"
        app:attr_text_color="@color/text_color_blue_007AFF"
        app:attr_text_margin_image_size="15dp"
        app:attr_text_size="16sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/book_group_move_Layout" />

    <com.aquila.lib.widget.view.CustomRecyclerView
        android:id="@+id/book_group_data_RecyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:attr_layout_style="list_vertical"
        app:layout_constrainedHeight="true"
        app:layout_constraintBottom_toTopOf="@id/book_group_cancel_Button"
        app:layout_constraintHeight_max="320dp"
        app:layout_constraintTop_toBottomOf="@id/book_group_create_new_Layout"
        tools:itemCount="10"
        tools:listitem="@layout/item_book_group_layout" />

</androidx.constraintlayout.widget.ConstraintLayout>