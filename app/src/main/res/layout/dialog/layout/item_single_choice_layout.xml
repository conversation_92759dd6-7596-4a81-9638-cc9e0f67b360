<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:orientation="vertical"
    android:paddingLeft="10dp"
    android:foreground="?android:attr/selectableItemBackground"
    android:paddingRight="10dp">

    <TextView
        android:id="@+id/item_bottom_single_choice_desc_TextView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:text="这个是标题"
        android:textColor="?theme_text_color_black"
        android:textSize="16sp" />

    <ImageView
        android:id="@+id/item_bottom_single_choice_choice_ImageView"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:scaleType="center"
        android:src="@drawable/ic_check_black" />


</RelativeLayout>