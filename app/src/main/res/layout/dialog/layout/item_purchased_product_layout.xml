<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:maxHeight="60dp"
    android:minHeight="50dp"
    android:paddingLeft="15dp"
    android:paddingRight="15dp">

    <TextView
        android:id="@+id/item_purchased_title_TextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_toLeftOf="@id/item_purchased_price_TextView"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="2"
        android:minHeight="50dp"
        android:textColor="?theme_text_color_black"
        android:textSize="16sp"
        tools:text="购买《马太福音》" />

    <TextView
        android:id="@+id/item_purchased_price_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:gravity="center_vertical"
        android:minHeight="50dp"
        android:paddingLeft="5dp"
        android:paddingRight="5dp"
        android:textColor="?theme_text_color_black"
        android:textSize="16sp"
        tools:text="-$231.04" />


</RelativeLayout>