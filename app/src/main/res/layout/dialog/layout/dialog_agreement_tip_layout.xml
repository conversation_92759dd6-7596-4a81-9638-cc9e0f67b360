<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="?theme_bg_white_conner_8dp_top"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="24dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/user_agreement_title"
        android:textColor="?theme_text_color_black"
        android:textSize="18sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/register_use_protocol_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="21dp"
        android:gravity="center"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="8dp"
        android:text="@string/please_first_read_and_agree"
        android:textColor="?theme_text_color_gray"
        android:textSize="16sp" />


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="8dp"
        android:orientation="horizontal">

        <Button
            android:id="@+id/tip_content_cancel_Button"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginBottom="20dp"
            android:layout_weight="1"
            android:background="@drawable/selector_all_conner_brown_stroke_bg"
            android:text="@string/not_agree"
            android:textColor="@color/color_orange_FF8A00"
            android:textSize="14sp" />

        <Button
            android:id="@+id/tip_content_OK_Button"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginLeft="15dp"
            android:layout_marginBottom="20dp"
            android:layout_weight="1"
            android:background="?theme_bg_white_orange_btn"
            android:text="@string/agree_and_continue"
            android:textColor="?theme_text_color_white"
            android:textSize="14sp" />
    </LinearLayout>

</LinearLayout>