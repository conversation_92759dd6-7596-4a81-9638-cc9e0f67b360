<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_marginLeft="25dp"
    android:layout_marginRight="25dp"
    android:background="?theme_bg_white_conner_8dp"
    android:orientation="vertical"
    android:padding="8dp">

    <TextView
        android:id="@+id/bottom_author_choice_title_TextView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:padding="10dp"
        tools:text="作者"
        android:textStyle="bold"
        android:layout_gravity="center"
        android:textColor="?theme_text_color_black_373636"
        android:textSize="@dimen/text_size_16" />

    <TextView
        android:id="@+id/login_country_divide_TextView"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="?theme_divide_line_horizontal" />

    <com.aquila.lib.widget.view.CustomRecyclerView
        android:id="@+id/bottom_single_choice_data_RecyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:attr_item_decoration_type="NONE"
        app:attr_layout_style="list_vertical"
        tools:itemCount="3"
        tools:listitem="@layout/item_author_choice_layout" />


    <Button
        android:id="@+id/bottom_single_choice_cancel_Button"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="10dp"
        android:visibility="gone"
        android:background="?theme_bg_white_conner_8dp"
        android:text="@string/label_cancel"
        android:textColor="?theme_text_color_black"
        android:textSize="16sp" />


</LinearLayout>