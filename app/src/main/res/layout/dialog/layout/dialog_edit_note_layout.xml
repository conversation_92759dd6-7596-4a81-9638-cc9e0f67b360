<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_bg_color_white_1E">

    <TextView
        android:id="@+id/note_edit_finish_TextView"
        android:layout_width="wrap_content"
        android:layout_height="50dp"
        android:layout_alignParentRight="true"
        android:background="?theme_comm_click_bg"
        android:foreground="?android:attr/selectableItemBackground"
        android:gravity="center"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:text="@string/finish"
        android:textColor="?theme_text_color_black"
        android:textSize="16sp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/note_edit_divide_line_View"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="?theme_bg_color_divide_line_color"
        app:layout_constraintBottom_toBottomOf="@id/note_edit_finish_TextView" />

    <View
        android:id="@+id/note_edit_mark_color_View"
        android:layout_width="4dp"
        android:layout_height="0dp"
        android:layout_marginLeft="15dp"
        app:layout_constraintBottom_toBottomOf="@id/note_edit_book_text_TextView"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/note_edit_book_text_TextView"
        tools:background="@color/color_red_C13013" />

    <TextView
        android:id="@+id/note_edit_book_text_TextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="25dp"
        android:layout_marginTop="9dp"
        android:layout_marginRight="15dp"
        android:ellipsize="end"
        android:maxLines="3"
        android:textColor="?theme_text_color_note_gray"
        android:textSize="12sp"
        app:layout_constraintLeft_toRightOf="@id/note_edit_mark_color_View"
        app:layout_constraintTop_toBottomOf="@+id/note_edit_divide_line_View"
        tools:text="fsfasfasfa盛大开放和代理商卡积分换禄口街道撒化肥禄口街道杀戮空间发多少垃圾筐sfsfasfasfa盛大开放和代理商卡积分换禄口街道撒化肥禄口街道杀戮空间发多少垃圾筐sfsfasfasfa盛大开放和代理商卡积分换禄口街道撒化肥禄口街道杀戮空间发多少垃圾筐s" />


    <TextView
        android:id="@+id/note_warning_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center_vertical"
        android:maxLength="20000"
        android:text="@string/text_oversize_warning"
        android:textColor="?theme_text_color_red"
        android:textSize="12sp"
        android:visibility="visible"
        app:layout_constraintLeft_toLeftOf="@id/note_edit_mark_color_View"
        app:layout_constraintTop_toBottomOf="@id/note_edit_book_text_TextView" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        android:scrollbarSize="4dp"
        android:scrollbarStyle="insideOverlay"
        android:scrollbars="vertical"
        android:padding="15dp"
        app:layout_constrainedHeight="true"
        android:background="?theme_bg_color_white_1E"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/note_warning_TextView">

        <EditText
            android:id="@+id/note_edit_desc_EditText"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/transparent"
            android:focusable="true"
            android:gravity="top"
            android:lineSpacingMultiplier="1.05"
            android:paddingBottom="50dp"
            android:textColor="?theme_text_color_note_black"
            android:textColorHint="?theme_text_color_gray_BC_88"
            android:textSize="14sp"
            tools:text="哈克斯暖风机阿什拉夫了哈克斯暖风机阿什拉夫了哈克斯暖风机阿什拉夫了哈克斯暖风机阿什拉夫了 " />
    </ScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>