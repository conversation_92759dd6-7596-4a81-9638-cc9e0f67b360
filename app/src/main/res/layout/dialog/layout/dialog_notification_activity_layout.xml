<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="35dp"
    android:layout_marginRight="35dp"
    android:background="?theme_bg_white_conner_8dp"
    android:orientation="vertical">

    <TextView
        android:id="@+id/notification_activity_title_TextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:minHeight="50dp"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:textColor="?theme_text_color_black"
        android:textSize="20sp"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="《颠覆性祷告》限免中" />

    <TextView
        android:id="@+id/notification_activity_sub_title_TextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:minHeight="40dp"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:textColor="?theme_text_color_black"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintTop_toBottomOf="@id/notification_activity_title_TextView"
        tools:text="圣诞特惠｜第1波" />

    <!--    <androidx.core.widget.NestedScrollView-->
    <!--        android:id="@+id/notification_activity_content_ScrollView"-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        app:layout_constraintHeight_max="400dp"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/notification_activity_sub_title_TextView">-->

    <!--        <LinearLayout-->
    <!--            android:layout_width="match_parent"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:orientation="vertical">-->

    <!--            <TextView-->
    <!--                android:id="@+id/notification_activity_content_TextView"-->
    <!--                android:layout_width="match_parent"-->
    <!--                android:layout_height="wrap_content"-->
    <!--                android:lineSpacingMultiplier="1.15"-->
    <!--                android:minHeight="40dp"-->
    <!--                android:paddingLeft="15dp"-->
    <!--                android:paddingTop="5dp"-->
    <!--                android:paddingRight="15dp"-->
    <!--                android:paddingBottom="15dp"-->
    <!--                android:textColor="?theme_text_color_black"-->
    <!--                android:textSize="16sp"-->
    <!--                tools:text="每一个上帝国度运动都始于祷告。经典的祷告辅助材料《颠覆性祷告》限时免费中！优惠仅到12/8，机会难得！本书使用圣经作为注意的资源及素材，目的再于赢得各种各样的人来加入这项系列的祷告操练之中，并亲历祷告的大能。于祷告。经典的祷告辅助材料《颠覆性祷告》限时免费中！优惠仅到12/8，机会难得！本书使用圣经作为注意的资源及素材，目的再于赢得各种各样的人来加入这项系列的祷告操练之中，并亲历祷告的大能。于祷告。经典的祷告辅助材料《颠覆性祷告》限时免费中！优惠仅到12/8，机会难得！本书使用圣经作为注意的资源及素材，目的再于赢得各种各样的人来加入这项系列的祷告操练之中，并亲历祷告的大能。于祷告。经典的祷告辅助材料《颠覆性祷告》限时免费中！优惠仅到12/8，机会难得！本书使用圣经作为注意的资源及素材，目的再于赢得各种各样的人来加入这项系列的祷告操练之中，并亲历祷告的大能。于祷告。经典的祷告辅助材料《颠覆性祷告》限时免费中！优惠仅到12/8，机会难得！本书使用圣经作为注意的资源及素材，目的再于赢得各种各样的人来加入这项系列的祷告操练之中，并亲历祷告的大能。于祷告。经典的祷告辅助材料《颠覆性祷告》限时免费中！优惠仅到12/8，机会难得！本书使用圣经作为注意的资源及素材，目的再于赢得各种各样的人来加入这项系列的祷告操练之中，并亲历祷告的大能。于祷告。经典的祷告辅助材料《颠覆性祷告》限时免费中！优惠仅到12/8，机会难得！本书使用圣经作为注意的资源及素材，目的再于赢得各种各样的人来加入这项系列的祷告操练之中，并亲历祷告的大能。" />-->
    <!--        </LinearLayout>-->
    <!--    </androidx.core.widget.NestedScrollView>-->

    <WebView
        android:id="@+id/notification_activity_content_WebView"
        android:layout_width="match_parent"
        android:layout_height="100dp"
        app:layout_constraintHeight_max="400dp"
        android:minHeight="200dp"
        app:layout_constraintTop_toBottomOf="@id/notification_activity_sub_title_TextView"
        tools:ignore="WebViewLayout" />

    <View
        android:id="@+id/notification_activity_horizontal_Line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/divide_line_size"
        android:background="?theme_bg_color_divide_line_color"
        app:layout_constraintTop_toBottomOf="@id/notification_activity_content_WebView" />

    <View
        android:id="@+id/notification_activity_vertical_Line"
        android:layout_width="@dimen/divide_line_size"
        android:layout_height="0dp"
        android:background="?theme_bg_color_divide_line_color"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/notification_activity_horizontal_Line" />

    <TextView
        android:id="@+id/notification_activity_close_TextView"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:background="?theme_comm_click_bg"
        android:forceDarkAllowed="?selectableItemBackground"
        android:gravity="center"
        android:text="@string/label_close"
        android:textColor="?theme_text_color_blue"
        android:textSize="17sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/notification_activity_vertical_Line"
        app:layout_constraintTop_toBottomOf="@id/notification_activity_horizontal_Line" />


    <TextView
        android:id="@+id/notification_activity_view_TextView"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:background="?theme_comm_click_bg"
        android:forceDarkAllowed="?selectableItemBackground"
        android:gravity="center"
        android:text="@string/label_check_view"
        android:textColor="?theme_text_color_blue"
        android:textSize="17sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/notification_activity_vertical_Line"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/notification_activity_horizontal_Line" />


</androidx.constraintlayout.widget.ConstraintLayout>