<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:layout_margin="15dp"
    android:background="?theme_bg_white_conner_8dp"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:paddingLeft="15dp"
    android:paddingTop="25dp"
    android:paddingRight="15dp"
    android:paddingBottom="25dp">


    <TextView
        android:id="@+id/purchase_book_name_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:textColor="?theme_text_color_package_black"
        android:textSize="18sp"
        tools:text="丁道尔圣经注释-摩西五经" />


    <TextView
        android:id="@+id/purchase_total_amount_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:gravity="center_vertical"
        android:textColor="?theme_text_color_black"
        android:textSize="16sp"
        android:visibility="gone"
        tools:text="总额: $56.99"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/purchase_purchased_amount_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:drawableRight="@drawable/ic_arrow_right_dark"
        android:drawablePadding="15dp"
        android:gravity="center_vertical"
        android:textColor="?theme_text_color_black"
        android:textSize="18sp"
        android:visibility="gone"
        tools:text="商品已购: -$56.99"
        tools:visibility="visible" />


    <TextView
        android:id="@+id/purchase_price_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:textStyle="bold"
        android:gravity="center_horizontal"
        android:textColor="?theme_text_color_black_4c"
        android:textSize="24sp"
        tools:text="应付：$23.4" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="20dp"
        android:background="@color/divide_line_color" />

    <RelativeLayout
        android:id="@+id/purchase_pay_method_container_layout"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:visibility="gone"
        android:layout_marginTop="10dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:text="@string/dialog_payment_info_mode"
            android:textColor="?theme_text_color_black"
            android:textSize="18sp" />

        <ImageView
            android:id="@+id/purchase_arrow_right_ImageView"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:rotation="-90"
            android:scaleType="center"
            android:src="?theme_ic_arrow_down" />

        <com.aquila.lib.widget.group.GroupImageTextLayout
            android:id="@+id/purchase_payment_method_Layout"
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            android:layout_toLeftOf="@id/purchase_arrow_right_ImageView"
            android:gravity="center_vertical"
            android:paddingLeft="10dp"
            android:paddingRight="0dp"
            app:attr_image_height="20dp"
            app:attr_image_src="?theme_ic_credit_card"
            app:attr_image_width="20dp"
            app:attr_parent_orientation="IMAGE_LEFT_TEXT_RIGHT"
            app:attr_scale_type="CENTER"
            app:attr_text="@string/dialog_payment_info_card_pay"
            app:attr_text_color="?theme_text_color_black"
            app:attr_text_gravity="center"
            app:attr_text_margin_image_size="10dp"
            app:attr_text_size="16sp" />


    </RelativeLayout>


    <Button
        android:id="@+id/purchase_buy_Button"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="20dp"
        android:layout_marginRight="15dp"
        android:background="@drawable/selector_all_conner_brown_bg"
        android:text="@string/dialog_payment_info_btn"
        android:textColor="@color/white"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/purchase_tips_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="15dp"
        android:layout_marginRight="15dp"
        android:text="@string/dialog_payment_info_tip_bottom"
        android:textColor="?theme_text_color_gray"
        android:textSize="12sp" />

</LinearLayout>