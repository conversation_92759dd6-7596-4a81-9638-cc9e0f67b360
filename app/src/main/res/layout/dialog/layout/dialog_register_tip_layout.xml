<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:layout_margin="30dp"
    android:background="?theme_bg_white_conner_8dp"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="24dp">

    <TextView
        android:id="@+id/tip_title_TextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/have_send_to_email"
        android:textColor="?theme_text_color_black"
        android:textSize="18sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tip_content_TextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="center"
        android:textColor="?theme_text_color_black"
        android:textSize="16sp"
        tools:text="购买成功" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:orientation="horizontal">

        <Button
            android:id="@+id/tip_content_cancel_Button"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:background="@drawable/selector_all_conner_brown_stroke_bg"
            android:text="@string/label_cancel"
            android:layout_marginRight="15dp"
            android:textColor="@color/color_orange_FF8A00"
            android:visibility="gone"
            tools:visibility="visible"
            android:textSize="14sp" />

        <Button
            android:id="@+id/payment_result_OK_Button"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:background="?theme_bg_white_orange_btn"
            android:text="@string/label_OK"
            android:textColor="?theme_text_color_white"
            android:textSize="14sp" />

    </LinearLayout>

</LinearLayout>