<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_marginLeft="5dp"
    android:layout_marginRight="5dp"
    android:background="?theme_bg_white_conner_8dp">

    <TextView
        android:id="@+id/in_payment_title_TextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:paddingTop="24dp"
        android:paddingBottom="16dp"
        android:text="@string/waiting_to_pay"
        android:textColor="?theme_text_color_black"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintTop_toTopOf="parent" />


    <androidx.core.widget.NestedScrollView
        android:id="@+id/in_payment_content_ScrollView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintHeight_max="400dp"
        app:layout_constraintTop_toBottomOf="@id/in_payment_title_TextView">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/in_payment_content_TextView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:lineSpacingMultiplier="1.15"
                android:minHeight="40dp"
                android:paddingLeft="24dp"
                android:paddingTop="5dp"
                android:paddingRight="24dp"
                android:paddingBottom="20dp"
                android:textColor="?theme_text_color_black"
                android:textSize="16sp"
                tools:text="您在以往订单中购买过《丁道尔新约圣经注释-使徒行传》，请点击 [继续支付] 完成该订单，该订单将于 22分34秒 后关闭。" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/in_payment_content_ScrollView"
        android:orientation="horizontal">

        <Button
            android:id="@+id/in_payment_cancel_TextView"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:background="@drawable/selector_all_conner_brown_stroke_bg"
            android:text="@string/cancel_order"
            android:layout_marginRight="20dp"
            android:layout_marginLeft="24dp"
            android:layout_marginBottom="24dp"
            android:textColor="@color/color_orange_FF8A00"
            android:textSize="14sp" />

        <Button
            android:id="@+id/in_payment_continue_pay_TextView"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:layout_marginBottom="24dp"
            android:layout_marginRight="24dp"
            android:background="?theme_bg_white_orange_btn"
            android:text="@string/continue_pay"
            android:textColor="?theme_text_color_white"
            android:textSize="14sp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>