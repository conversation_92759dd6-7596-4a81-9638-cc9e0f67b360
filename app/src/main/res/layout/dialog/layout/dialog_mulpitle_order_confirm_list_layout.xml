<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="?theme_bg_white_conner_8dp"
    android:padding="10dp"
    tools:layout_margin="10dp">

    <TextView
        android:id="@+id/multiple_confirm_title_TextView"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:gravity="center_vertical"
        android:text="商品清单"
        android:textColor="?theme_text_color_black"
        android:textSize="16sp"
        app:layout_constraintTop_toTopOf="parent" />


    <ImageView
        android:id="@+id/multiple_confirm_close_ImageView"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:scaleType="center"
        android:src="?theme_dialog_close_ImageView"
        android:tint="?theme_icon_tint_color"
        app:layout_constraintBottom_toBottomOf="@id/multiple_confirm_title_TextView"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/multiple_confirm_title_TextView"
        tools:ignore="UseAppTint" />

    <com.aquila.lib.widget.view.CustomRecyclerView
        android:id="@+id/multiple_confirm_data_RecyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:attr_end_item_margin="30dp"
        app:attr_item_margin="15dp"
        app:attr_layout_style="list_vertical"
        app:attr_start_item_margin="15dp"
        app:layout_constrainedHeight="true"
        app:layout_constraintHeight_max="600dp"
        app:layout_constraintTop_toBottomOf="@id/multiple_confirm_title_TextView"
        tools:itemCount="13"
        tools:listitem="@layout/item_holder_single_confirm_product_layout" />


</androidx.constraintlayout.widget.ConstraintLayout>