<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingTop="10dp"
    android:paddingBottom="10dp">

    <com.aquila.lib.widget.view.AdaptiveImageView
        android:id="@+id/dialog_activity_cover_ImageView"
        android:layout_width="40dp"
        android:layout_height="53dp"
        android:layout_marginLeft="15dp"
        android:src="@drawable/demo_01"
        app:attr_fit_type="FIT_WIDTH"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/dialog_activity_book_name_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:textColor="?theme_text_color_black"
        android:textSize="14sp"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/dialog_activity_cover_ImageView"
        app:layout_constraintLeft_toRightOf="@id/dialog_activity_cover_ImageView"
        app:layout_constraintRight_toLeftOf="@id/dialog_activity_price_TextView"
        app:layout_constraintTop_toTopOf="@id/dialog_activity_cover_ImageView"
        tools:text="立定根基：依循古道，建造信徒" />

    <TextView
        android:id="@+id/dialog_activity_price_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:gravity="center"
        android:text="$23.3"
        android:textColor="?theme_text_color_black"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="@id/dialog_activity_cover_ImageView"
        app:layout_constraintRight_toLeftOf="@id/dialog_activity_delete_TextView"
        app:layout_constraintTop_toTopOf="@id/dialog_activity_cover_ImageView" />

    <TextView
        android:id="@+id/dialog_activity_delete_TextView"
        android:layout_width="60dp"
        android:layout_height="50dp"
        android:gravity="center"
        android:text="@string/label_delete"
        android:textColor="?theme_text_color_red"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>