<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#3000"
    android:paddingLeft="40dp"
    android:paddingTop="80dp"
    android:paddingRight="40dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/note_book_download_container_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="50dp"
        android:background="?theme_bg_white_conner_8dp"
        android:paddingLeft="15dp"
        android:paddingTop="15dp"
        android:paddingRight="15dp"
        android:paddingBottom="50dp">

        <ImageView
            android:id="@+id/note_book_download_close_ImageView"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/ic_close_gray"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.aquila.lib.widget.view.AdaptiveImageView
            android:id="@+id/note_book_download_cover_ImageView"
            android:layout_width="140dp"
            android:layout_height="wrap_content"
            android:scaleType="fitXY"
            android:src="@drawable/ic_default_book_cover"
            app:attr_aspect_ratio="120,174"
            app:attr_mask_color="#3000"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/note_book_download_close_ImageView" />

        <ProgressBar
            android:id="@+id/note_book_download_ProgressBar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="0dp"
            android:layout_height="3dp"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:max="100"
            android:progress="0"
            android:progressDrawable="@drawable/layer_list_progress_drawable"
            android:visibility="visible"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="@id/note_book_download_cover_ImageView"
            app:layout_constraintLeft_toLeftOf="@id/note_book_download_cover_ImageView"
            app:layout_constraintRight_toRightOf="@id/note_book_download_cover_ImageView"
            app:layout_constraintTop_toTopOf="@id/note_book_download_cover_ImageView"
            tools:progress="0" />

        <TextView
            android:id="@+id/note_book_download_percent_TextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:elevation="5dp"
            android:textColor="?theme_text_color_white"
            android:textSize="10sp"
            android:visibility="visible"
            app:layout_constraintLeft_toLeftOf="@id/note_book_download_ProgressBar"
            app:layout_constraintRight_toRightOf="@id/note_book_download_ProgressBar"
            app:layout_constraintTop_toBottomOf="@id/note_book_download_ProgressBar"
            tools:text="80%" />

        <TextView
            android:id="@+id/note_book_download_failure_TextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:elevation="5dp"
            android:textColor="?theme_text_color_white"
            android:textSize="16sp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/note_book_download_cover_ImageView"
            app:layout_constraintLeft_toLeftOf="@id/note_book_download_cover_ImageView"
            app:layout_constraintRight_toRightOf="@id/note_book_download_cover_ImageView"
            app:layout_constraintTop_toTopOf="@id/note_book_download_cover_ImageView"
            tools:text="下载失败" />

        <TextView
            android:id="@+id/note_book_download_name_TextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:ellipsize="end"
            android:maxLines="3"
            android:textColor="?theme_text_color_black"
            android:textSize="16sp"
            app:layout_constrainedWidth="true"
            app:layout_constraintLeft_toLeftOf="@id/note_book_download_cover_ImageView"
            app:layout_constraintRight_toRightOf="@id/note_book_download_cover_ImageView"
            app:layout_constraintTop_toBottomOf="@id/note_book_download_cover_ImageView"
            tools:text="丁道尔圣经注释哥林多前书" />

        <TextView
            android:id="@+id/note_book_download_prompt_TextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:ellipsize="end"
            android:maxLines="3"
            android:text="@string/download_finish_jump_tips"
            android:textColor="?theme_text_color_dark"
            android:textSize="14sp"
            app:layout_constraintLeft_toLeftOf="@id/note_book_download_cover_ImageView"
            app:layout_constraintRight_toRightOf="@id/note_book_download_cover_ImageView"
            app:layout_constraintTop_toBottomOf="@id/note_book_download_name_TextView" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>