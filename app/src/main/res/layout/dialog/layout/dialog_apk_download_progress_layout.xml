<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_margin="60dp"
    android:padding="15dp"
    app:cardBackgroundColor="?theme_bg_color_white_1E"
    app:cardCornerRadius="10dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingLeft="15dp"
        android:paddingTop="10dp"
        android:paddingRight="15dp"
        android:paddingBottom="10dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:gravity="center"
            android:text="@string/downloading"
            android:textColor="?theme_text_color_black"
            android:textSize="16sp" />

        <ProgressBar
            android:id="@+id/apk_download_progress_ProgressBar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:max="100"
            android:maxHeight="3dp"
            android:minHeight="3dp"
            android:progress="50"
            android:progressDrawable="@drawable/layer_list_progressbar_style" />

        <TextView
            android:id="@+id/apk_download_percent_TextView"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:gravity="center"
            android:text="43%"
            android:textColor="@color/color_red_FF3B30"
            android:textSize="16sp" />


    </LinearLayout>


</androidx.cardview.widget.CardView>