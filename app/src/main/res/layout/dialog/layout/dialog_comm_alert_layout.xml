<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_marginLeft="30dp"
    android:layout_marginRight="30dp"
    android:background="?theme_bg_white_conner_8dp"

    >

    <TextView
        android:id="@+id/dialog_alert_title_TextView"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:gravity="center"
        android:singleLine="true"
        android:text="标题"
        android:textColor="?theme_text_color_black"
        android:textSize="18sp"
        android:textStyle="bold"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/dialog_alert_bottom_button_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="dialog_alert_bottom_button_container_layout,dialog_alert_message_line_View" />

    <View
        android:id="@+id/dialog_alert_message_line_View"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="?theme_divide_line_horizontal"
        app:layout_constraintBottom_toTopOf="@id/dialog_alert_bottom_button_container_layout"
        app:layout_constraintTop_toBottomOf="@id/dialog_alert_message_container_layout" />


    <LinearLayout
        android:id="@+id/dialog_alert_bottom_button_container_layout"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:divider="?theme_divide_line_vertical"
        android:orientation="horizontal"
        android:showDividers="middle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/dialog_alert_message_container_layout">

        <Button
            android:id="@+id/dialog_alert_left_Button"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/selector_comm_click_bg"
            android:text="@string/label_OK"
            android:textColor="#007AFF"
            android:textSize="16sp"
            android:visibility="gone"
            tools:visibility="visible" />

        <Button
            android:id="@+id/dialog_alert_middle_Button"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/selector_comm_click_bg"
            android:text="@string/never_remind"
            android:textColor="#9b9b9b"
            android:textSize="16sp"
            android:visibility="gone"
            tools:visibility="visible" />

        <Button
            android:id="@+id/dialog_alert_right_Button"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/selector_comm_click_bg"
            android:text="@string/label_cancel"
            android:textColor="#007AFF"
            android:textSize="16sp"
            android:visibility="gone"
            tools:visibility="visible" />
    </LinearLayout>


    <androidx.core.widget.NestedScrollView
        android:id="@+id/dialog_alert_message_container_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="1dp"
        android:layout_marginBottom="1dp"
        android:maxHeight="100dp"
        android:orientation="vertical"
        app:layout_constrainedHeight="true"
        app:layout_constraintBottom_toTopOf="@id/dialog_alert_bottom_button_container_layout"
        app:layout_constraintTop_toBottomOf="@id/dialog_alert_title_TextView">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingTop="10dp"
            android:paddingBottom="10dp">

            <EditText
                android:id="@+id/dialog_alert_message_EditText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:background="#0fff"
                android:cursorVisible="true"
                android:gravity="left|top"
                android:hint=""
                android:minHeight="50dp"
                android:padding="10dp"
                android:text=""
                android:textColor="#333"
                android:textCursorDrawable="@null"
                android:textSize="16sp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/dialog_alert_message_TextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:layout_marginRight="15dp"
                android:gravity="center"
                android:lineSpacingMultiplier="1.2"
                android:minHeight="50dp"
                android:textColor="?theme_text_color_black"
                android:textSize="16sp"
                android:visibility="visible"
                tools:text="这个是消息内容这" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>