<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_marginLeft="30dp"
    android:layout_marginRight="30dp"
    android:background="?theme_bg_white_conner_8dp"
    android:orientation="vertical">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:gravity="center"
        android:text="@string/correct"
        android:textColor="?theme_text_color_black"
        android:textSize="16sp"
        android:textStyle="bold" />

    <com.aquila.lib.widget.view.CustomRecyclerView
        android:id="@+id/correction_data_RecyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:attr_divide_left_margin="10dp"
        app:attr_item_decoration_type="ITEM_DIVIDE_LNE"
        app:attr_item_divide_line_color="?theme_bg_color_divide_line_color"
        app:attr_item_divide_line_size="@dimen/divide_line_size"
        app:attr_layout_style="list_vertical"
        tools:itemCount="5"
        tools:listitem="@layout/item_correction_dialog_layout" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:orientation="horizontal">

        <Button
            android:id="@+id/correction_cancel_Button"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/selector_left_bottom_radius_orange_bg"
            android:foreground="?selectableItemBackground"
            android:text="@string/label_cancel"
            android:textColor="?theme_text_color_white"
            android:textSize="16sp" />

        <Button
            android:id="@+id/correction_submit_Button"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/selector_right_bottom_radius_orange_bg"
            android:foreground="?selectableItemBackground"
            android:text="@string/label_submit"
            android:textColor="?theme_text_color_white"
            android:textSize="16sp" />

    </LinearLayout>


</LinearLayout>