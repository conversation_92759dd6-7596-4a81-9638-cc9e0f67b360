<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_bg_color_white">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/dialog_note_detail_container_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
            android:id="@+id/dialog_note_detail_title_layout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/top_title_height"
            app:attr_title_text="@string/note_detail"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:fillViewport="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/dialog_note_detail_title_layout">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="15dp"
                    android:paddingTop="15dp"
                    android:paddingRight="15dp">

                    <com.aquila.lib.widget.view.DotView
                        android:id="@+id/dialog_note_detail_color_DotView"
                        android:layout_width="10dp"
                        android:layout_height="10dp"
                        app:attr_dot_color="@color/text_color_FDA4A5"
                        app:attr_dot_radius="5dp"
                        app:layout_constraintBottom_toBottomOf="@id/dialog_note_detail_date_TextView"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="@id/dialog_note_detail_date_TextView" />

                    <TextView
                        android:id="@+id/dialog_note_detail_date_TextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:paddingLeft="8dp"
                        android:paddingRight="8dp"
                        android:singleLine="true"
                        android:textColor="?theme_text_color_black_4c_8a"
                        android:textSize="12sp"
                        app:layout_constraintBottom_toBottomOf="@id/dialog_note_detail_delete_ImageView"
                        app:layout_constraintLeft_toRightOf="@id/dialog_note_detail_color_DotView"
                        app:layout_constraintTop_toTopOf="@id/dialog_note_detail_delete_ImageView"
                        tools:text="2019/06/16 11:25" />

                    <ImageView
                        android:id="@+id/dialog_note_detail_delete_ImageView"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:background="?theme_comm_click_bg"
                        android:padding="5dp"
                        android:scaleType="fitCenter"
                        android:src="?theme_ic_note_delete"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/dialog_note_detail_quote_TextView"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:lineSpacingMultiplier="1.05"
                        android:paddingTop="5dp"
                        android:paddingBottom="5dp"
                        android:gravity="left"
                        android:textDirection="ltr"
                        android:textColor="?theme_text_color_note_gray"
                        android:textSize="16sp"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/dialog_note_detail_delete_ImageView"
                        tools:text="引用 | 就旧约时代的祭司职务而言，利未记是一本编辑得相当周详的参考手利未记是一本编辑得相当周详的参考手利未记是一本编辑得相当周详的参考手利未记是一本编辑得相当周详的参考手，" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/divide_line_size"
                    android:layout_marginLeft="15dp"
                    android:layout_marginRight="15dp"
                    android:layout_marginTop="3dp"
                    android:layout_marginBottom="3dp"
                    android:background="?theme_bg_color_divide_line_color" />

                <TextView
                    android:id="@+id/dialog_note_detail_note_TextView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/transparent"
                    android:gravity="top|left"
                    android:textDirection="ltr"
                    android:lineSpacingMultiplier="1.05"
                    android:paddingLeft="15dp"
                    android:paddingTop="5dp"
                    android:paddingRight="15dp"
                    android:paddingBottom="80dp"
                    android:textColor="?theme_text_color_note_black"
                    android:textSize="16sp"
                    tools:text="就旧约时代的祭司职务而言，就旧约时代的祭司职务而言，" />
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

        <Button
            android:id="@+id/dialog_note_detail_next_Button"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:layout_marginBottom="20dp"
            android:background="?theme_round_button_gray_stoke_click_bg_4e"
            android:text="@string/next"
            android:textColor="?theme_text_color_dark"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>