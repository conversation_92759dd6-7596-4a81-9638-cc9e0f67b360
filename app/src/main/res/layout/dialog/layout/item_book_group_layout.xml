<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/selector_comm_click_bg"
    android:gravity="center_vertical"
    android:minHeight="50dp"
    android:orientation="horizontal"
    android:paddingLeft="15dp"
    android:paddingRight="5dp">

    <ImageView
        android:id="@+id/item_book_group_icon_ImageView"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:scaleType="center"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:src="@drawable/ic_folder_black"
        app:tint="?theme_text_color_black" />

    <TextView
        android:id="@+id/item_book_group_name_TextView"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="2"
        android:paddingLeft="10dp"
        android:textColor="?theme_text_color_black"
        android:textSize="16sp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/item_book_group_icon_ImageView"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginRight="40dp"
        tools:text="书籍分组名称书籍分组名称书籍分组名称书籍分组名称书籍分组名称书籍分组名称书籍分组名称书籍分组名称" />

    <ImageView
        android:id="@+id/item_book_group_check_ImageView"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:src="@drawable/ic_check_yellow"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:visibility="gone"
        tools:visibility="visible"
        />


</androidx.constraintlayout.widget.ConstraintLayout>