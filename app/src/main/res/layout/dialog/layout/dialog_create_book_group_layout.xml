<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?theme_bg_white_conner_8dp"
    android:orientation="vertical"
    tools:layout_gravity="center"
    tools:layout_width="350dp">

    <TextView
        android:id="@+id/create_book_group_title_TextView"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:gravity="center"
        android:text="@string/dialog_book_group_create_new"
        android:textColor="?theme_text_color_black"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <Button
        android:id="@+id/create_book_group_cancel_Button"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:background="@color/transparent"
        android:gravity="center"
        android:text="@string/label_cancel"
        android:textColor="@color/text_color_blue_007AFF"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/create_book_group_ok_Button" />

    <Button
        android:id="@+id/create_book_group_ok_Button"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:background="@color/transparent"
        android:gravity="center"
        android:text="@string/label_OK"
        android:textColor="@color/text_color_blue_007AFF"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/create_book_group_cancel_Button"
        app:layout_constraintRight_toRightOf="parent" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/divide_line_size"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:background="?theme_bg_color_divide_line_8E8E93"
        app:layout_constraintTop_toBottomOf="@id/create_book_group_input_EditText" />

    <TextView
        android:id="@+id/create_book_group_label_TextView"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:gravity="center_vertical"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:text="@string/dialog_book_group_name"
        android:textColor="?theme_text_color_package_black"
        android:textSize="12sp"
        app:layout_constraintBottom_toTopOf="@id/create_book_group_input_EditText"
        app:layout_constraintTop_toBottomOf="@id/create_book_group_title_TextView" />

    <EditText
        android:id="@+id/create_book_group_input_EditText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="15dp"
        android:background="@color/transparent"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:hint="@string/hint_input_book_group"
        android:minHeight="50dp"
        android:paddingLeft="15dp"
        android:paddingRight="59dp"
        android:singleLine="true"
        android:textColor="?theme_text_color_package_black"
        android:textColorHint="?theme_text_color_gray_BC_88"
        android:textSize="16sp"
        tools:text=""
        app:layout_constrainedHeight="true"
        app:layout_constraintBottom_toTopOf="@id/create_book_group_ok_Button"
        app:layout_constraintHeight_max="200dp"
        app:layout_constraintTop_toBottomOf="@id/create_book_group_label_TextView" />

    <ImageView
        android:id="@+id/create_book_group_clear_input_ImageView"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginRight="10dp"
        android:padding="10dp"
        android:src="@drawable/ic_clear_input_gray"
        app:layout_constraintBottom_toBottomOf="@id/create_book_group_input_EditText"
        app:layout_constraintRight_toRightOf="@id/create_book_group_input_EditText"
        app:layout_constraintTop_toTopOf="@id/create_book_group_input_EditText" />

</androidx.constraintlayout.widget.ConstraintLayout>