<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="50dp">

    <TextView
        android:id="@+id/item_correction_type_Name_TextView"
        android:layout_width="wrap_content"
        android:layout_height="50dp"
        android:layout_alignParentLeft="true"
        android:layout_centerVertical="true"
        android:layout_toLeftOf="@id/item_correction_check_ImageView"
        android:gravity="center_vertical"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:text="章节错乱"
        android:textColor="?theme_text_color_black"
        android:textSize="16sp" />

    <ImageView
        android:id="@+id/item_correction_check_ImageView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="10dp"
        android:src="@drawable/selected_favorite_check_box_yellow" />


</RelativeLayout>