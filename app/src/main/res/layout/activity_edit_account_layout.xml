<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_root_view_bg"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/top_title_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/top_title_height"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="@string/edit_account"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintTop_toBottomOf="@id/top_title_layout">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/edit_account_LinerLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="?theme_bg_color_white"
                android:gravity="left|center_vertical"
                android:orientation="horizontal"
                android:visibility="visible">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="15dp"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:gravity="left|center_vertical"
                    android:maxLines="1"
                    android:minHeight="30dp"
                    android:text="@string/avatar"
                    android:textColor="?attr/theme_text_color_black_373636"
                    android:textSize="18sp" />

                <ImageView
                    android:id="@+id/edit_account_avatar_ImageView"
                    android:layout_width="90dp"
                    android:layout_height="90dp"
                    android:layout_margin="16dp"
                    android:src="@drawable/ic_default_avatar" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="15dp"
                    android:src="@drawable/ic_arrow_right_gray" />

            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="?theme_bg_color_white_double_line"
                android:orientation="vertical">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.7dp"
                    android:background="?theme_bg_color_divide_line_color" />

                <com.aquila.lib.widget.group.GroupImageTextLayout
                    android:id="@+id/edit_account_name_layout"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:background="@drawable/selector_comm_click_bg"
                    android:foreground="?android:attr/selectableItemBackground"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    app:attr_image_src="@drawable/ic_arrow_right_gray"
                    app:attr_parent_orientation="IMAGE_RIGHT_TEXT_LEFT_SIDE_ALIGN"
                    app:attr_text="@string/name"
                    app:attr_text_color="?theme_text_color_black"
                    app:attr_text_size="16sp">

                    <TextView
                        android:id="@+id/edit_account_name_TextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="60dp"
                        android:layout_alignParentRight="true"
                        android:layout_marginRight="20dp"
                        android:lines="1"
                        android:ellipsize="end"
                        android:gravity="right"
                        android:textSize="14sp"
                        android:textColor="?theme_text_color_black"
                        tools:text="john" />
                </com.aquila.lib.widget.group.GroupImageTextLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.7dp"
                    android:layout_marginLeft="16dp"
                    android:visibility="gone"
                    android:background="?theme_bg_color_divide_line_color" />

                <com.aquila.lib.widget.group.GroupImageTextLayout
                    android:id="@+id/edit_account_birth_layout"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:visibility="gone"
                    android:background="@drawable/selector_comm_click_bg"
                    android:foreground="?android:attr/selectableItemBackground"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    app:attr_image_src="@drawable/ic_arrow_right_gray"
                    app:attr_parent_orientation="IMAGE_RIGHT_TEXT_LEFT_SIDE_ALIGN"
                    app:attr_text="@string/birth_year"
                    app:attr_text_color="?theme_text_color_black"
                    app:attr_text_size="16sp">

                    <TextView
                        android:id="@+id/edit_account_birth_TextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_marginRight="20dp"
                        android:textColor="?theme_text_color_black"
                        tools:text="1993年" />
                </com.aquila.lib.widget.group.GroupImageTextLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.7dp"
                    android:layout_marginLeft="16dp"
                    android:visibility="gone"
                    android:background="?theme_bg_color_divide_line_color" />

                <com.aquila.lib.widget.group.GroupImageTextLayout
                    android:id="@+id/edit_account_believe_year_layout"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:visibility="gone"
                    android:background="@drawable/selector_comm_click_bg"
                    android:foreground="?android:attr/selectableItemBackground"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    app:attr_image_src="@drawable/ic_arrow_right_gray"
                    app:attr_parent_orientation="IMAGE_RIGHT_TEXT_LEFT_SIDE_ALIGN"
                    app:attr_text="@string/believe_year"
                    app:attr_text_color="?theme_text_color_black"
                    app:attr_text_size="16sp">

                    <TextView
                        android:id="@+id/edit_account_believe_year_TextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_marginRight="20dp"
                        tools:text="2017年"
                        android:textColor="?theme_text_color_black" />
                </com.aquila.lib.widget.group.GroupImageTextLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.7dp"
                    android:background="?theme_bg_color_divide_line_color" />
            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>