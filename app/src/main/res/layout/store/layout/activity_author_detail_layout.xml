<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_root_view_bg"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/book_list_top_TitleLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/top_title_height"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_is_text_bold="true"
        app:attr_title_text="@string/category_name"></com.wedevote.wdbook.ui.widgets.CommTopTitleLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?theme_bg_color_white"
        >

        <LinearLayout
            android:id="@+id/book_list_container_CoordinatorLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="?theme_bg_color_white"
            android:orientation="vertical">

            <com.google.android.material.appbar.AppBarLayout
                android:id="@+id/book_list_AppBarLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?theme_bg_color_white">

                <com.google.android.material.appbar.CollapsingToolbarLayout
                    android:id="@+id/book_list_CollapsingToolbarLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_scrollFlags="scroll|enterAlways"
                    tools:visibility="visible">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/book_list_filter_container_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        app:layout_collapseMode="parallax"
                        app:layout_collapseParallaxMultiplier="0.7"
                        tools:visibility="visible">

                        <LinearLayout
                            android:id="@+id/author_detail_top_layout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="?theme_root_view_bg"
                            android:fitsSystemWindows="true"
                            android:orientation="vertical"
                            app:layout_constraintBottom_toTopOf="@id/book_list_sort_container_Layout"
                            app:layout_constraintTop_toTopOf="parent">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="?theme_bg_color_white"
                                android:gravity="left|center_vertical"
                                android:orientation="horizontal"
                                android:paddingLeft="24dp"
                                android:paddingTop="24dp"
                                android:paddingRight="24dp"
                                android:visibility="visible">

                                <ImageView
                                    android:id="@+id/author_avatar_ImageView"
                                    android:layout_width="48dp"
                                    android:layout_height="48dp"
                                    android:layout_marginRight="16dp"
                                    android:src="@drawable/ic_default_author" />

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="48dp"
                                    android:orientation="vertical">

                                    <TextView
                                        android:id="@+id/author_name_TextView"
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_weight="1"
                                        android:ellipsize="end"
                                        android:gravity="left|top"
                                        android:maxLines="1"
                                        android:textColor="?attr/theme_text_color_black_373636"
                                        android:textSize="16sp"
                                        android:textStyle="bold"
                                        tools:text="这个是昵称" />

                                    <TextView
                                        android:id="@+id/author_book_number_TextView"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:ellipsize="end"
                                        android:gravity="left|bottom"
                                        android:maxLines="1"
                                        android:textColor="?attr/theme_text_color_gray_BC_88"
                                        android:textSize="14sp"
                                        tools:text="1个作品" />
                                </LinearLayout>
                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/author_detail_summary_LinearLayout"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="8dp"
                                android:background="?theme_bg_color_white_bottom_line"
                                android:orientation="vertical"
                                android:paddingLeft="24dp"
                                android:paddingRight="24dp"
                                android:paddingBottom="24dp">

                                <com.wedevote.wdbook.ui.widgets.ExpandTextViewTwo
                                    android:id="@+id/author_detail_summary_ExpandTextViewTwo"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="20dp"
                                    app:bottom_margin="3dp"
                                    app:content_bold="false"
                                    app:content_color="@color/color_gray_707070"
                                    app:content_size="14sp"
                                    app:current_text=""
                                    app:expand_prifix_text="..."
                                    app:expand_text="@string/expand"
                                    app:max_line="4"
                                    app:shrink_text="@string/pack_up"
                                    app:special_bold="false"
                                    app:special_color="@color/color_blue_006FFF"
                                    app:special_horizon_click_more="60dp"
                                    app:special_left_margin="10dp"
                                    app:special_right_margin="80dp"
                                    app:special_size="14sp"
                                    app:special_vertical_click_more="20dp" />
                            </LinearLayout>

                        </LinearLayout>

                        <TextView
                            android:id="@+id/book_list_sub_category_divide"
                            android:layout_width="match_parent"
                            android:layout_height="0.7dp"
                            android:background="?theme_divide_line_horizontal"
                            android:text=""
                            app:layout_constraintTop_toBottomOf="@id/book_list_sort_container_Layout" />

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/book_list_sort_container_Layout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="?theme_bg_color_white"
                            android:paddingLeft="24dp"
                            android:paddingTop="16dp"
                            android:paddingRight="24dp"
                            android:paddingBottom="16dp"
                            app:layout_constraintTop_toBottomOf="@id/author_detail_top_layout">

                            <TextView
                                android:id="@+id/book_list_sort_default_TextView"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:paddingRight="12dp"
                                android:text="@string/new_release"
                                android:textColor="?theme_category_item_text_color_selector"
                                android:textSize="12sp"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintLeft_toLeftOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />


                            <TextView
                                android:id="@+id/book_list_sort_sold_TextView"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:paddingLeft="12dp"
                                android:paddingRight="12dp"
                                android:text="@string/sort_by_sell"
                                android:textColor="?theme_category_item_text_color_selector"
                                android:textSize="12sp"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintLeft_toRightOf="@id/book_list_sort_default_TextView"
                                app:layout_constraintTop_toTopOf="parent" />

                            <TextView
                                android:id="@+id/book_list_sort_price_TextView"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:paddingLeft="12dp"
                                android:paddingRight="10dp"
                                android:text="@string/sort_by_price"
                                android:textColor="?theme_category_item_text_color_selector"
                                android:textSize="12sp"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintLeft_toRightOf="@id/book_list_sort_sold_TextView"
                                app:layout_constraintTop_toTopOf="parent" />

                            <com.aquila.lib.widget.group.GroupImageTextLayout
                                android:id="@+id/book_list_language_layout"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="?theme_shape_all_conner_gray"
                                android:gravity="center"
                                android:minHeight="25dp"
                                android:paddingLeft="15dp"
                                android:paddingRight="15dp"
                                app:attr_image_height="10dp"
                                app:attr_image_src="@drawable/ic_arrow_down_gray"
                                app:attr_image_src_tint="?theme_icon_tint_color"
                                app:attr_image_width="10dp"
                                app:attr_parent_orientation="IMAGE_RIGHT_TEXT_LEFT"
                                app:attr_scale_type="FIT_CENTER"
                                app:attr_text="@string/all_language"
                                app:attr_text_color="?theme_category_item_text_color_selector"
                                app:attr_text_margin_image_size="5dp"
                                app:attr_text_size="12sp"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintRight_toRightOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />
                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.appcompat.widget.Toolbar
                        android:id="@+id/book_list_collapsing_title_Toolbar"
                        android:layout_width="match_parent"
                        android:layout_height="50dp"
                        android:paddingLeft="10dp"
                        android:paddingRight="10dp"
                        app:layout_collapseMode="pin"
                        tools:visibility="gone">

                        <com.aquila.lib.widget.group.GroupImageTextLayout
                            android:id="@+id/book_list_top_collapsing_content_Layout"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:visibility="gone"
                            app:attr_image_height="12dp"
                            app:attr_image_src="@drawable/ic_arrow_down_gray"
                            app:attr_image_src_tint="?theme_icon_tint_color"
                            app:attr_image_width="12dp"
                            app:attr_parent_orientation="IMAGE_RIGHT_TEXT_LEFT"
                            app:attr_text_color="?theme_text_color_black"
                            app:attr_text_margin_image_size="10dp"
                            app:attr_text_size="16sp"
                            tools:attr_text="神学类.全部.最新上架.繁体中文" />
                    </androidx.appcompat.widget.Toolbar>
                </com.google.android.material.appbar.CollapsingToolbarLayout>
            </com.google.android.material.appbar.AppBarLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_behavior="@string/appbar_scrolling_view_behavior">

                <TextView
                    android:id="@+id/book_list_empty_prompt_TextView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:gravity="center"
                    android:padding="20dp"
                    android:text="@string/no_book_data_prompt"
                    android:textColor="?theme_text_color_black"
                    android:textSize="16sp"
                    android:visibility="gone"
                    app:layout_behavior="@string/appbar_scrolling_view_behavior"
                    app:layout_constrainedHeight="true"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.2"
                    tools:visibility="visible" />

                <com.aquila.lib.layout.SmartRefreshLayout
                    android:id="@+id/book_list_SmartRefreshLayout"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:srlEnableLoadMore="false"
                    tools:visibility="gone">

                    <com.aquila.lib.widget.view.CustomRecyclerView
                        android:id="@+id/book_list_data_RecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:nestedScrollingEnabled="true"
                        android:paddingLeft="24dp"
                        android:paddingTop="5dp"
                        android:paddingRight="24dp"
                        app:attr_end_item_margin="50dp"
                        app:attr_item_decoration_type="ITEM_SPACE"
                        app:attr_item_divide_line_color="@color/divide_line_color"
                        app:attr_item_divide_line_size="@dimen/divide_line_size"
                        app:attr_item_margin="14dp"
                        app:attr_layout_style="list_vertical"
                        app:layout_constraintTop_toBottomOf="@id/micro_new_book_title_TextView"
                        tools:itemCount="5"
                        tools:listitem="@layout/item_micro_store_book_list_layout" />
                </com.aquila.lib.layout.SmartRefreshLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>