<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="?theme_bg_color_white"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/book_list_top_TitleLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/top_title_height"
        app:attr_is_text_bold="true"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="@string/category_name">

        <ImageView
            android:id="@+id/book_list_search_ImageView"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_alignParentRight="true"
            android:background="?theme_comm_click_bg"
            android:foreground="?android:selectableItemBackground"
            android:scaleType="center"
            android:src="@drawable/ic_home_tab_search"
            app:tint="?theme_icon_tint_color"
            tools:ignore="UseAppTint" />
    </com.wedevote.wdbook.ui.widgets.CommTopTitleLayout>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/book_list_container_CoordinatorLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?theme_bg_color_white">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/book_list_AppBarLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?theme_bg_color_white">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/book_list_CollapsingToolbarLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_scrollFlags="scroll|enterAlways|exitUntilCollapsed"
                tools:visibility="visible">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/book_list_filter_container_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingLeft="10dp"
                    android:paddingTop="10dp"
                    android:paddingRight="10dp"
                    app:layout_collapseMode="parallax"
                    app:layout_collapseParallaxMultiplier="0.7"
                    tools:visibility="visible">

                    <ImageView
                        android:id="@+id/book_list_category_expand_ImageView"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:elevation="5dp"
                        android:scaleType="center"
                        android:src="@drawable/ic_arrow_down_gray"
                        app:tint="?theme_icon_tint_color"
                        app:layout_constraintBottom_toBottomOf="@id/book_list_category_RecyclerView"
                        app:layout_constraintRight_toRightOf="@id/book_list_category_RecyclerView"
                        tools:ignore="UseAppTint" />

                    <com.aquila.lib.widget.view.CustomRecyclerView
                        android:id="@+id/book_list_category_RecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingRight="30dp"
                        app:attr_flex_direction="row"
                        app:attr_flex_wrap="wrap"
                        app:attr_layout_style="flexbox"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:itemCount="2"
                        tools:listitem="@layout/cell_category_item_layout" />


                    <androidx.constraintlayout.widget.Group
                        android:id="@+id/book_list_sub_category_Group"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:constraint_referenced_ids="book_list_sub_category_RecyclerView,book_list_sub_category_expand_ImageView" />

                    <com.aquila.lib.widget.view.CustomRecyclerView
                        android:id="@+id/book_list_sub_category_RecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:paddingRight="30dp"
                        app:attr_flex_direction="row"
                        app:attr_flex_wrap="wrap"
                        app:attr_layout_style="flexbox"
                        app:layout_constraintTop_toBottomOf="@id/book_list_category_RecyclerView"
                        tools:itemCount="3"
                        tools:listitem="@layout/cell_category_item_layout" />

                    <ImageView
                        android:id="@+id/book_list_sub_category_expand_ImageView"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:elevation="5dp"
                        android:scaleType="center"
                        android:src="@drawable/ic_arrow_down_gray"
                        app:tint="?theme_icon_tint_color"
                        app:layout_constraintBottom_toBottomOf="@id/book_list_sub_category_RecyclerView"
                        app:layout_constraintRight_toRightOf="@id/book_list_sub_category_RecyclerView"
                        tools:ignore="UseAppTint" />

                    <TextView
                        android:id="@+id/book_list_sub_category_divide"
                        android:layout_height="1dp"
                        android:layout_marginTop="6dp"
                        android:layout_width="match_parent"
                        android:text=""
                        android:background="?theme_divide_line_horizontal"
                        app:layout_constraintTop_toBottomOf="@id/book_list_sub_category_RecyclerView"/>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/book_list_sort_container_Layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:paddingTop="10dp"
                        android:paddingBottom="10dp"
                        app:layout_constraintTop_toBottomOf="@id/book_list_sub_category_divide">

                        <TextView
                            android:id="@+id/book_list_sort_default_TextView"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:minHeight="30dp"
                            android:paddingLeft="10dp"
                            android:paddingRight="10dp"
                            android:text="@string/new_release"
                            android:textColor="?theme_category_item_text_color_selector"
                            android:textSize="14sp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />


                        <TextView
                            android:id="@+id/book_list_sort_sold_TextView"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:minHeight="30dp"
                            android:paddingLeft="10dp"
                            android:paddingRight="10dp"
                            android:text="@string/sort_by_sell"
                            android:textColor="?theme_category_item_text_color_selector"
                            android:textSize="14sp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toRightOf="@id/book_list_sort_default_TextView"
                            app:layout_constraintTop_toTopOf="parent" />


                        <TextView
                            android:id="@+id/book_list_sort_price_TextView"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:minHeight="30dp"
                            android:paddingLeft="10dp"
                            android:paddingRight="10dp"
                            android:text="@string/sort_by_price"
                            android:textColor="?theme_category_item_text_color_selector"
                            android:textSize="14sp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toRightOf="@id/book_list_sort_sold_TextView"
                            app:layout_constraintTop_toTopOf="parent" />

                        <com.aquila.lib.widget.group.GroupImageTextLayout
                            android:id="@+id/book_list_language_layout"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="?theme_shape_all_conner_gray"
                            android:gravity="center"
                            android:minHeight="35dp"
                            android:paddingLeft="15dp"
                            android:paddingRight="15dp"
                            app:attr_image_height="10dp"
                            app:attr_image_src="@drawable/ic_arrow_down_gray"
                            app:attr_image_src_tint="?theme_icon_tint_color"
                            app:attr_image_width="10dp"
                            app:attr_parent_orientation="IMAGE_RIGHT_TEXT_LEFT"
                            app:attr_scale_type="FIT_CENTER"
                            app:attr_text="@string/all_language"
                            app:attr_text_color="?theme_category_item_text_color_selector"
                            app:attr_text_margin_image_size="5dp"
                            app:attr_text_size="14sp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/book_list_collapsing_title_Toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    app:layout_collapseMode="pin"
                    tools:visibility="gone">

                    <com.aquila.lib.widget.group.GroupImageTextLayout
                        android:id="@+id/book_list_top_collapsing_content_Layout"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        app:attr_image_height="12dp"
                        app:attr_image_src="@drawable/ic_arrow_down_gray"
                        app:attr_image_src_tint="?theme_icon_tint_color"
                        app:attr_image_width="12dp"
                        app:attr_parent_orientation="IMAGE_RIGHT_TEXT_LEFT"
                        tools:attr_text="神学类.全部.最新上架.繁体中文"
                        app:attr_text_color="?theme_text_color_black"
                        app:attr_text_margin_image_size="10dp"
                        app:attr_text_size="16sp" />
                </androidx.appcompat.widget.Toolbar>
            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <TextView
                android:id="@+id/book_list_empty_prompt_TextView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="20dp"
                android:gravity="center"
                android:layout_centerInParent="true"
                android:text="@string/no_book_data_prompt"
                android:textColor="?theme_text_color_black"
                android:textSize="18sp"
                tools:visibility="visible"
                android:visibility="gone"
                app:layout_behavior="@string/appbar_scrolling_view_behavior"
                app:layout_constrainedHeight="true"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.2" />

            <com.aquila.lib.layout.SmartRefreshLayout
                android:id="@+id/book_list_SmartRefreshLayout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:srlEnableLoadMore="false"
                tools:visibility="gone">

                <com.aquila.lib.widget.view.CustomRecyclerView
                    android:id="@+id/book_list_data_RecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:nestedScrollingEnabled="true"
                    android:paddingLeft="15dp"
                    android:paddingTop="5dp"
                    android:paddingRight="15dp"
                    app:attr_end_item_margin="50dp"
                    app:attr_item_decoration_type="ITEM_SPACE"
                    app:attr_item_divide_line_color="@color/divide_line_color"
                    app:attr_item_divide_line_size="@dimen/divide_line_size"
                    app:attr_item_margin="14dp"
                    app:attr_layout_style="list_vertical"
                    app:layout_constraintTop_toBottomOf="@id/micro_new_book_title_TextView"
                    tools:itemCount="5"
                    tools:listitem="@layout/item_micro_store_book_list_layout" />
            </com.aquila.lib.layout.SmartRefreshLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</LinearLayout>