<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_root_view_bg"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/activity_top_title_layout"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="?theme_bg_color_white_bottom_line"
        tools:attr_title_text="标题" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/activity_bottom_container_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="?theme_bg_color_white_top_line">

        <CheckBox
            android:id="@+id/activity_select_all_CheckBox"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:button="@drawable/selected_check_box_gray_yellow"
            android:checked="false"
            android:padding="5dp"
            android:text="@string/select_all"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.aquila.lib.widget.group.GroupImageTextLayout
            android:id="@+id/activity_select_all_ImageTextLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="20dp"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:paddingRight="20dp"
            app:attr_image_src="@drawable/selected_check_box_gray_yellow"
            app:attr_parent_orientation="IMAGE_LEFT_TEXT_RIGHT"
            app:attr_text="@string/select_all"
            app:attr_text_color="?theme_text_color_black"
            app:attr_text_margin_image_size="5dp"
            app:attr_text_size="14sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <Button
            android:id="@+id/activity_purchase_immediately_Button"
            android:layout_width="wrap_content"
            android:layout_height="70dp"
            android:background="?theme_text_color_orange"
            android:text="@string/pay_now"
            android:textColor="?theme_text_color_white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/activity_total_label_TextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="5dp"
            android:paddingRight="5dp"
            android:text="@string/product_need_pay"
            android:textColor="?theme_text_color_black"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="@id/activity_total_price_TextView"
            app:layout_constraintRight_toLeftOf="@id/activity_total_price_TextView"
            app:layout_constraintTop_toTopOf="@id/activity_total_price_TextView" />

        <TextView
            android:id="@+id/activity_total_price_TextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="10dp"
            android:minWidth="70dp"
            android:textColor="?theme_text_color_red"
            android:textSize="16sp"
            app:layout_constraintBottom_toTopOf="@id/activity_select_book_list_layout"
            app:layout_constraintRight_toLeftOf="@id/activity_purchase_immediately_Button"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="$2322.10" />


        <com.aquila.lib.widget.group.GroupImageTextLayout
            android:id="@+id/activity_select_book_list_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:drawableRight="@drawable/ic_arrow_right_black"
            android:padding="5dp"
            app:attr_image_height="10dp"
            app:attr_image_src="@drawable/ic_arrow_right_black"
            app:attr_image_src_tint="?theme_icon_tint_color"
            app:attr_image_width="10dp"
            app:attr_parent_orientation="IMAGE_RIGHT_TEXT_LEFT"
            app:attr_text_color="?theme_text_color_black"
            app:attr_text_margin_image_size="5dp"
            app:attr_text_size="14sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="@id/activity_total_label_TextView"
            app:layout_constraintTop_toBottomOf="@id/activity_total_price_TextView"
            tools:attr_text="已选8件" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.aquila.lib.layout.SmartRefreshLayout
        android:id="@+id/activity_data_container_Layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/activity_bottom_container_layout"
        android:layout_below="@id/activity_top_title_layout"
        app:srlEnableAutoLoadMore="false"
        app:srlEnableLoadMore="false">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"

                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/activity_title_container_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?theme_bg_color_white"
                    android:orientation="vertical"
                    android:paddingBottom="15dp"
                    android:visibility="visible">

                    <TextView
                        android:id="@+id/activity_sub_title_TextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:gravity="center_vertical"
                        android:minHeight="40dp"
                        android:paddingLeft="15dp"
                        android:paddingRight="15dp"
                        android:visibility="gone"
                        tools:visibility="visible"
                        android:textColor="?theme_text_color_black"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        tools:text="复活节专题活动，全场下单 5 折起" />


                    <TextView
                        android:id="@+id/activity_time_TextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:minHeight="30dp"
                        android:paddingLeft="15dp"
                        android:paddingRight="15dp"
                        android:textColor="?theme_text_color_black"
                        android:textSize="14sp"
                        tools:text="活动时间：4月1日 - 4月18日" />

                    <TextView
                        android:id="@+id/activity_desc_TextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:minHeight="30dp"
                        android:paddingLeft="15dp"
                        android:paddingRight="15dp"
                        android:textColor="?theme_text_color_black"
                        android:textSize="14sp"
                        tools:text="活动介绍：您可以在专题页面加购多本书籍或单本购买书籍，离开该界面无法统一结算。" />
                </LinearLayout>

                <TextView
                    android:id="@+id/activity_expire_TextView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:minHeight="30dp"
                    android:paddingLeft="15dp"
                    android:paddingTop="15dp"
                    android:paddingRight="15dp"
                    android:paddingBottom="15dp"
                    android:text="优惠价格活动已结束"
                    android:textColor="?theme_text_color_black"
                    android:textSize="14sp"
                    android:visibility="gone" />

                <com.aquila.lib.widget.view.CustomRecyclerView
                    android:id="@+id/activity_book_list_RecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?theme_bg_color_white"
                    app:attr_end_item_margin="25dp"
                    app:attr_item_margin="15dp"
                    app:attr_layout_style="list_vertical"
                    app:attr_start_item_margin="5dp"
                    tools:itemCount="4"
                    tools:listitem="@layout/item_activity_book_layout" />

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </com.aquila.lib.layout.SmartRefreshLayout>


</RelativeLayout>