<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="?theme_bg_color_white"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/book_list_top_TitleLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/top_title_height"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="@string/category_name" />

    <com.aquila.lib.layout.SmartRefreshLayout
        android:id="@+id/book_list_SmartRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        >
        <com.aquila.lib.widget.view.CustomRecyclerView
            android:id="@+id/book_list_data_RecyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="19dp"
            android:layout_marginLeft="24dp"
            android:layout_marginRight="24dp"
            android:nestedScrollingEnabled="false"
            app:attr_item_margin="14dp"
            app:attr_end_item_margin="50dp"
            app:attr_item_decoration_type="ITEM_SPACE"
            app:attr_item_divide_line_color="@color/divide_line_color"
            app:attr_item_divide_line_size="@dimen/divide_line_size"
            app:attr_layout_style="list_vertical"
            app:layout_constraintTop_toBottomOf="@id/micro_new_book_title_TextView"
            tools:itemCount="5"
            tools:listitem="@layout/item_micro_store_book_list_layout"
            />

    </com.aquila.lib.layout.SmartRefreshLayout>

</LinearLayout>