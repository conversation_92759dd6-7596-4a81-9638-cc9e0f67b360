<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?theme_comm_click_bg"
    android:minHeight="60dp"
    android:orientation="horizontal"
    android:paddingLeft="15dp"
    android:paddingRight="15dp">

    <TextView
        android:id="@+id/item_category_title_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="5dp"
        android:maxLines="2"
        android:textColor="?theme_text_color_black"
        android:textSize="17sp"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/item_category_count_TextView"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="圣经注释圣经注释圣经圣经注释" />

    <TextView
        android:id="@+id/item_category_count_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:layout_toRightOf="@id/item_category_title_TextView"
        android:textColor="?theme_text_color_gray"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintRight_toLeftOf="@+id/item_category_arrow_ImageView"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="234" />

    <ImageView
        android:id="@+id/item_category_arrow_ImageView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_arrow_right_gray"
        app:tint="?theme_icon_tint_color"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"

        tools:ignore="UseAppTint" />


</androidx.constraintlayout.widget.ConstraintLayout>