<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="?theme_bg_color_white">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/unbind_help_title_layout"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="@string/label_help"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/unbind_help_label_title_TextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:minHeight="50dp"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:text="@string/how_to_unbind_card"
        android:textColor="?theme_text_color_black"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/unbind_help_title_layout" />

    <TextView
        android:id="@+id/unbind_help_desc_TextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:minHeight="50dp"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:text="@string/unbind_card_prompt"
        android:textColor="?theme_text_color_dark"
        android:textSize="16sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/unbind_help_label_title_TextView" />


    <com.aquila.lib.widget.view.AdaptiveImageView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="30dp"
        android:src="@drawable/ic_unbind_card_prompt"
        app:attr_fit_type="FIT_WIDTH"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/unbind_help_desc_TextView" />

</androidx.constraintlayout.widget.ConstraintLayout>