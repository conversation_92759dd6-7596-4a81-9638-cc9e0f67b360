<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_bg_color_white"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/book_category_top_title_Layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/top_title_height"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="@string/all_categories"
        app:attr_is_text_bold="true"
        app:attr_title_text_color="?theme_text_color_black" />

    <com.aquila.lib.layout.SmartRefreshLayout
        android:id="@+id/book_category_RefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:srlEnableLoadMore="false">

        <com.aquila.lib.widget.view.CustomRecyclerView
            android:id="@+id/book_category_data_RecyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="16dp"
            app:attr_divide_left_margin="15dp"
            app:attr_item_decoration_type="ITEM_DIVIDE_LNE"
            app:attr_item_divide_line_color="?theme_bg_color_divide_line_D3"
            app:attr_item_divide_line_size="@dimen/divide_line_size"
            app:attr_layout_style="list_vertical"
            tools:listitem="@layout/holder_item_category_layout" />

    </com.aquila.lib.layout.SmartRefreshLayout>

</LinearLayout>