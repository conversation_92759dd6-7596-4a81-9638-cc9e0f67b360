<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_root_view_bg"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/order_confirm_title_layout"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="@string/confirm_order"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/order_confirm_bottom_container_Layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?theme_bg_color_white"
        android:orientation="vertical"
        android:paddingLeft="25dp"
        android:paddingTop="15dp"
        android:paddingRight="15dp"
        android:paddingBottom="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <com.aquila.lib.widget.group.GroupTwoTextLayout
            android:id="@+id/confirm_activity_need_pay_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:visibility="gone"
            app:attr_first_text="应付:"
            app:attr_first_text_color="?theme_text_color_black"
            app:attr_first_text_gravity="center"
            app:attr_first_text_height="40dp"
            app:attr_first_text_size="16sp"
            app:attr_is_linkage="false"
            app:attr_layout_orientation="HORIZONTAL"
            app:attr_second_text_bold="true"
            app:attr_second_text_color="?theme_text_color_red"
            app:attr_second_text_height="40dp"
            app:attr_second_text_size="24sp"
            tools:attr_second_text="￥34.2" />

        <TextView
            android:id="@+id/confirm_activity_need_pay_title_TextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/product_need_pay"
            android:textColor="?theme_text_color_black"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/confirm_activity_need_pay_price_TextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:textColor="?theme_text_color_red"
            android:textSize="24sp"
            app:layout_constraintBaseline_toBaselineOf="@id/confirm_activity_need_pay_title_TextView"
            app:layout_constraintLeft_toRightOf="@id/confirm_activity_need_pay_title_TextView"
            tools:text="$23.5" />

        <Button
            android:id="@+id/order_confirm_buy_Button"
            android:layout_width="150dp"
            android:layout_height="40dp"
            android:layout_alignParentRight="true"
            android:layout_marginBottom="10dp"
            android:background="?theme_bg_white_orange_btn"
            android:text="@string/confirm_buy"
            android:textColor="?theme_text_color_white"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/confirm_activity_container_ScrollView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/order_confirm_bottom_container_Layout"
        app:layout_constraintTop_toBottomOf="@id/order_confirm_title_layout">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingBottom="50dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/order_confirm_single_container_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?theme_bg_color_white"
                android:orientation="vertical"
                android:paddingTop="15dp"
                android:paddingBottom="15dp"
                tools:visibility="visible">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/order_confirm_single_product_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:visibility="visible">

                    <com.aquila.lib.widget.view.AdaptiveImageView
                        android:id="@+id/order_confirm_single_book_cover_ImageView"
                        android:layout_width="70dp"
                        android:layout_height="wrap_content"
                        app:attr_aspect_ratio="56,74"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:src="@drawable/demo_02" />

                    <TextView
                        android:id="@+id/order_confirm_single_book_name_TextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="85dp"
                        android:text=""
                        android:textColor="?theme_text_color_black"
                        android:textSize="16sp"
                        android:maxLines="2"
                        android:ellipsize="end"
                        app:layout_constraintTop_toTopOf="@id/order_confirm_single_book_cover_ImageView"
                        app:layout_constraintRight_toRightOf="parent"
                        tools:text="福音：教会如何彰显基督的荣美" />

                    <TextView
                        android:id="@+id/order_confirm_single_book_price_TextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:text=""
                        android:textColor="?theme_text_color_black"
                        android:textSize="16sp"
                        app:layout_constraintLeft_toLeftOf="@id/order_confirm_single_book_name_TextView"
                        app:layout_constraintTop_toBottomOf="@id/order_confirm_single_book_name_TextView"
                        tools:text="$37.1" />

                </androidx.constraintlayout.widget.ConstraintLayout>


                <com.aquila.lib.widget.group.GroupImageTextLayout
                    android:id="@+id/order_confirm_total_product_layout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    android:visibility="visible"
                    app:attr_image_src="@drawable/ic_arrow_right_gray"
                    app:attr_image_src_tint="?theme_icon_tint_color"
                    app:attr_parent_orientation="IMAGE_RIGHT_TEXT_LEFT"
                    app:attr_text_color="?theme_text_color_dark"
                    app:attr_text_margin_image_size="5dp"
                    app:attr_text_size="14sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:attr_text="共7件"
                    tools:visibility="gone" />

                <com.aquila.lib.widget.view.CustomRecyclerView
                    android:id="@+id/order_confirm_data_RecyclerView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="10dp"
                    android:visibility="visible"
                    app:attr_end_item_margin="10dp"
                    app:attr_item_margin="10dp"
                    app:attr_layout_style="list_horizontal"
                    app:attr_start_item_margin="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintHeight_max="400dp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toLeftOf="@id/order_confirm_total_product_layout"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:itemCount="13"
                    tools:listitem="@layout/item_holder_confirm_multiple_book_layout"
                    tools:visibility="gone" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="?theme_bg_color_white"
                android:orientation="vertical"
                tools:visibility="visible">

                <!--                <TextView-->
                <!--                    android:id="@+id/order_confirm_price_detail_TextView"-->
                <!--                    android:layout_width="match_parent"-->
                <!--                    android:layout_height="50dp"-->
                <!--                    android:background="?theme_bg_color_white_bottom_line"-->
                <!--                    android:gravity="center_vertical"-->
                <!--                    android:minHeight="40dp"-->
                <!--                    android:paddingLeft="15dp"-->
                <!--                    android:text="价格明细"-->
                <!--                    android:textColor="?theme_text_color_black"-->
                <!--                    android:textSize="16sp"-->
                <!--                    android:textStyle="bold"-->
                <!--                    app:layout_constraintTop_toBottomOf="@id/order_confirm_title_layout" />-->

                <com.aquila.lib.widget.group.GroupSideAlignLayout
                    android:id="@+id/order_confirm_total_amount_Layout"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    app:attr_left_text="@string/product_total_amount"
                    app:attr_left_text_color="?theme_text_color_dark"
                    app:attr_left_text_size="16sp"

                    app:attr_right_text_color="?theme_text_color_dark"
                    app:attr_right_text_size="16sp"
                    tools:attr_right_text="$232.1" />

                <com.wedevote.wdbook.ui.widgets.WidgetMineItemLayout
                    android:id="@+id/order_confirm_use_coupon_layout"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:background="?theme_comm_click_bg"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    app:attr_arrow_image_tint="@color/text_color_gray_8A8A8A"
                    app:attr_data_text="@string/no_useable_coupon"
                    app:attr_data_text_color="?theme_text_color_red"
                    app:attr_image_src="@color/transparent"
                    app:attr_show_image="false"
                    app:attr_title_text="优惠券"

                    app:layout_constraintTop_toBottomOf="@id/order_confirm_has_brought_Layout" />

                <RelativeLayout
                    android:id="@+id/order_confirm_has_brought_Layout"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_centerVertical="true"
                        android:gravity="center"
                        android:text="@string/product_purchased"
                        android:textColor="?theme_text_color_dark"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/order_confirm_purchased_TextView"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="10dp"
                        android:layout_toLeftOf="@id/order_confirm_arrow_ImageView"
                        android:gravity="center"
                        android:textColor="?theme_text_color_dark"
                        android:textSize="16sp"
                        tools:text="$23.1" />

                    <ImageView
                        android:id="@+id/order_confirm_arrow_ImageView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:scaleType="center"
                        android:src="@drawable/ic_arrow_right_gray" />
                </RelativeLayout>


                <RelativeLayout
                    android:id="@+id/order_confirm_actual_pay_Layout"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="10dp"
                        android:layout_toLeftOf="@id/order_confirm_need_pay_TextView"
                        android:gravity="center"
                        android:text="@string/product_need_pay"
                        android:textColor="?theme_text_color_dark"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/order_confirm_need_pay_TextView"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:gravity="center"
                        android:textColor="?theme_text_color_red"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        tools:text="$23.1" />
                </RelativeLayout>
            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="?theme_bg_color_white"
                android:orientation="vertical">

                <!--                <TextView-->
                <!--                    android:id="@+id/order_confirm_pay_method_TextView"-->
                <!--                    android:layout_width="match_parent"-->
                <!--                    android:layout_height="50dp"-->
                <!--                    android:background="?theme_bg_color_white_bottom_line"-->
                <!--                    android:gravity="center_vertical"-->
                <!--                    android:minHeight="40dp"-->
                <!--                    android:paddingLeft="15dp"-->
                <!--                    android:text="支付方式"-->
                <!--                    android:textColor="?theme_text_color_black"-->
                <!--                    android:textSize="16sp"-->
                <!--                    android:textStyle="bold" />-->

                <com.aquila.lib.widget.group.GroupImageTextLayout
                    android:id="@+id/order_confirm_alipay_layout"
                    android:layout_width="match_parent"
                    android:layout_height="55dp"
                    android:background="?theme_comm_click_bg"
                    android:foreground="?attr/selectableItemBackground"
                    android:gravity="center_vertical"
                    android:paddingLeft="20dp"
                    android:paddingRight="20dp"
                    android:visibility="gone"
                    tools:visibility="visible"
                    app:attr_image_src="@drawable/ic_payment_alipay"
                    app:attr_parent_orientation="IMAGE_LEFT_TEXT_RIGHT"
                    app:attr_text="@string/dialog_payment_info_alipay"
                    app:attr_text_color="?theme_text_color_black"
                    app:attr_text_margin_image_size="15dp"
                    app:attr_text_size="16sp"
                    app:layout_constraintTop_toBottomOf="@id/pay_method_title_TextView">

                    <ImageView
                        android:id="@+id/order_confirm_alipay_check_status_ImageView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:src="@drawable/selected_paytype_select_box_yellow" />
                </com.aquila.lib.widget.group.GroupImageTextLayout>

                <com.aquila.lib.widget.group.GroupImageTextLayout
                    android:id="@+id/order_confirm_paypal_layout"
                    android:layout_width="match_parent"
                    android:layout_height="55dp"
                    android:background="?theme_comm_click_bg"
                    android:foreground="?attr/selectableItemBackground"
                    android:gravity="center_vertical"
                    android:paddingLeft="20dp"
                    android:paddingRight="20dp"
                    android:visibility="gone"
                    tools:visibility="visible"
                    app:attr_image_src="@drawable/ic_payment_paypal"
                    app:attr_parent_orientation="IMAGE_LEFT_TEXT_RIGHT"
                    app:attr_text="@string/dialog_payment_info_paypal_pay"
                    app:attr_text_color="?theme_text_color_black"
                    app:attr_text_margin_image_size="15dp"
                    app:attr_text_size="16sp"
                    app:layout_constraintTop_toBottomOf="@id/pay_method_alipay_layout">

                    <ImageView
                        android:id="@+id/order_confirm_paypal_check_status_ImageView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:src="@drawable/selected_paytype_select_box_yellow" />
                </com.aquila.lib.widget.group.GroupImageTextLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/order_confirm_stripe_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    tools:visibility="visible"
                    android:minHeight="50dp"
                    android:paddingLeft="20dp"
                    android:paddingTop="10dp"
                    android:paddingRight="20dp"
                    android:paddingBottom="10dp"
                    app:layout_constraintTop_toBottomOf="@id/pay_method_paypal_layout">

                    <ImageView
                        android:id="@+id/order_confirm_credit_card_ImageView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="?theme_ic_credit_card"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/pay_method_credit_TextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="15dp"
                        android:text="@string/dialog_payment_info_card_pay"
                        android:textColor="?theme_text_color_black"
                        android:textSize="16sp"
                        app:layout_constraintLeft_toRightOf="@id/order_confirm_credit_card_ImageView"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/order_confirm_unbind_help_TextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:paddingTop="5dp"
                        android:paddingBottom="5dp"
                        android:text="@string/how_to_unbind_card"
                        android:textColor="@color/text_color_blue_007AFF"
                        app:layout_constraintLeft_toLeftOf="@id/pay_method_credit_TextView"
                        app:layout_constraintTop_toBottomOf="@id/pay_method_credit_TextView" />

                    <ImageView
                        android:id="@+id/order_confirm_stripe_check_status_ImageView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/selected_paytype_select_box_yellow"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="?theme_bg_color_white"
                android:orientation="vertical"
                android:padding="15dp">

                <TextView
                    android:id="@+id/order_confirm_prompt_label_TextView"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:gravity="center_vertical"
                    android:minHeight="40dp"
                    android:text="@string/label_description"
                    android:textColor="?theme_text_color_black"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintTop_toBottomOf="@id/order_confirm_title_layout" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:minHeight="30dp"
                    android:orientation="horizontal"
                    android:paddingLeft="10dp">

                    <com.aquila.lib.widget.view.DotView
                        android:layout_width="2dp"
                        android:layout_height="2dp"
                        app:attr_dot_color="?theme_text_color_black" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:gravity="center_vertical"
                        android:text="@string/purchase_prompt_1"
                        android:textColor="?theme_text_color_black"
                        android:textSize="16sp"
                        app:layout_constraintTop_toBottomOf="@id/order_confirm_title_layout" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:minHeight="30dp"
                    android:orientation="horizontal"
                    android:paddingLeft="10dp">

                    <com.aquila.lib.widget.view.DotView
                        android:layout_width="3dp"
                        android:layout_height="3dp"
                        app:attr_dot_color="?theme_text_color_black" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:gravity="center_vertical"
                        android:lineSpacingMultiplier="1.2"
                        android:text="@string/purchase_prompt_2"
                        android:textColor="?theme_text_color_black"
                        android:textSize="16sp"
                        app:layout_constraintTop_toBottomOf="@id/order_confirm_title_layout" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:minHeight="30dp"
                    android:orientation="horizontal"
                    android:paddingLeft="10dp">

                    <com.aquila.lib.widget.view.DotView
                        android:layout_width="2dp"
                        android:layout_height="2dp"
                        android:layout_marginTop="10dp"
                        app:attr_dot_color="?theme_text_color_black" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:gravity="center_vertical"
                        android:lineSpacingMultiplier="1.2"
                        android:text="@string/purchase_prompt_3"
                        android:textColor="?theme_text_color_black"
                        android:textSize="16sp"
                        app:layout_constraintTop_toBottomOf="@id/order_confirm_title_layout" />
                </LinearLayout>
            </LinearLayout>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>