<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="50dp">

    <TextView
        android:id="@+id/reference_left_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_red_conner_reference_bg"
        android:paddingLeft="7dp"
        android:paddingTop="3dp"
        android:paddingRight="7dp"
        android:paddingBottom="3dp"
        android:text="@string/title_suit"
        android:textColor="@color/white"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/ic_mine_book" />

    <TextView
        android:id="@+id/reference_title_TextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8dp"
        android:ellipsize="end"
        android:paddingRight="3dp"
        android:singleLine="true"
        android:textColor="?attr/theme_text_color_black"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintLeft_toRightOf="@id/reference_left_TextView"
        app:layout_constraintRight_toLeftOf="@id/reference_right_TextView"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="标题" />

    <ImageView
        android:id="@+id/reference_right_ImageView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="5dp"
        android:scaleType="center"
        android:src="@drawable/ic_arrow_right_gray"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/reference_right_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="5dp"
        android:layout_marginRight="8dp"
        android:text="@string/go_buy"
        android:textColor="?theme_text_color_dark_4c"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toLeftOf="@id/reference_right_ImageView"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>