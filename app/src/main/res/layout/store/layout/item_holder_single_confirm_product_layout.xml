<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/order_confirm_single_product_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingLeft="15dp"
    android:paddingRight="15dp"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintTop_toTopOf="parent">


    <com.aquila.lib.widget.view.AdaptiveImageView
        android:id="@+id/order_confirm_single_book_cover_ImageView"
        android:layout_width="70dp"
        android:layout_height="wrap_content"
        android:src="@drawable/demo_02"
        app:attr_aspect_ratio="56,74"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/order_confirm_single_book_name_TextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:layout_marginLeft="85dp"
        android:maxLines="2"
        android:text=""
        android:textColor="?theme_text_color_black"
        android:textSize="16sp"
        app:layout_constraintTop_toTopOf="@id/order_confirm_single_book_cover_ImageView"
        tools:text="福音：教会如何彰显基督的荣美" />

    <TextView
        android:id="@+id/order_confirm_single_book_price_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text=""
        android:textColor="?theme_text_color_red"
        android:textSize="16sp"
        app:layout_constraintLeft_toLeftOf="@id/order_confirm_single_book_name_TextView"
        app:layout_constraintTop_toBottomOf="@id/order_confirm_single_book_name_TextView"
        tools:text="$37.1" />

</androidx.constraintlayout.widget.ConstraintLayout>

