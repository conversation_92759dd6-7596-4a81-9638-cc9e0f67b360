<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="horizontal"
    android:paddingLeft="16dp"
    android:paddingRight="16dp">

    <TextView
        android:id="@+id/book_detail_publisher_label_TextView"
        style="@style/style_book_detail_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="right"
        android:minWidth="80dp"
        android:maxWidth="100dp"
        tools:text="版社:"
        android:textColor="?theme_text_color_black" />

    <TextView
        android:id="@+id/book_detail_publisher_value_TextView"
        style="@style/style_book_detail_value"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:text="信望爱出版社信望爱出版信望爱出版"
        android:textColor="?theme_text_color_black" />

</LinearLayout>