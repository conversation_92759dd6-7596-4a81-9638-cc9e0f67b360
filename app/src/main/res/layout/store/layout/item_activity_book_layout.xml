<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingRight="15dp">


    <ImageView
        android:id="@+id/item_activity_check_ImageView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        android:paddingTop="40dp"
        android:paddingBottom="40dp"
        android:src="@drawable/selected_check_box_gray_yellow"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@id/item_activity_cover_Layout"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/item_activity_cover_Layout"
        tools:src="@drawable/selected_check_box_gray_yellow" />

    <RelativeLayout
        android:id="@+id/item_activity_cover_Layout"
        android:layout_width="83dp"
        android:layout_height="110dp"
        app:layout_constraintLeft_toRightOf="@id/item_activity_check_ImageView"
        app:layout_constraintTop_toTopOf="parent"
        android:background="?theme_text_color_black_e3_4e"
        >
        <com.aquila.lib.widget.view.AdaptiveImageView
            android:id="@+id/item_activity_cover_ImageView"
            android:layout_width="81dp"
            android:layout_height="108dp"
            android:layout_centerInParent="true"
            android:adjustViewBounds="true"
            android:scaleType="centerCrop"
            tools:src="@drawable/ic_default_book_cover" />

    </RelativeLayout>
    <TextView
        android:id="@+id/item_activity_discount_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dp"
        android:background="?theme_text_color_red"
        android:elevation="10dp"
        android:gravity="center"
        android:minHeight="20dp"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:textColor="?theme_text_color_white"
        android:textSize="10sp"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="@id/item_activity_cover_Layout"
        app:layout_constraintTop_toTopOf="@id/item_activity_cover_Layout"
        tools:text="9折"
        tools:visibility="visible" />


    <TextView
        android:id="@+id/item_activity_book_name_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="?theme_text_color_black"
        android:textSize="16sp"
        app:layout_constrainedWidth="true"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintLeft_toRightOf="@id/item_activity_cover_Layout"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/item_activity_cover_Layout"
        tools:text="立定根基：依循古道，" />

    <TextView
        android:id="@+id/item_activity_author_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="10dp"
        android:textColor="?theme_text_color_black"
        android:maxLines="1"
        android:ellipsize="end"
        android:textSize="12sp"
        app:layout_constrainedWidth="true"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintLeft_toRightOf="@id/item_activity_cover_Layout"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/item_activity_book_name_TextView"
        tools:text="雷.奥特伦（Ray Aever）雷.奥特伦（Ray Aever）雷.奥特伦（Ray Aever）雷.奥特伦（Ray Aever）雷.奥特伦（Ray Aever）" />


    <TextView
        android:id="@+id/item_activity_price_label_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="10dp"
        android:text="@string/title_activity_price"
        android:textColor="?theme_text_color_black"
        android:textSize="12sp"
        app:layout_constraintLeft_toRightOf="@id/item_activity_cover_Layout"
        app:layout_constraintTop_toBottomOf="@id/item_activity_author_TextView" />

    <TextView
        android:id="@+id/item_activity_price_value_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="3dp"
        android:textColor="?theme_text_color_red"
        android:textSize="12sp"
        app:layout_constrainedWidth="true"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintBottom_toBottomOf="@id/item_activity_price_label_TextView"
        app:layout_constraintLeft_toRightOf="@id/item_activity_price_label_TextView"
        app:layout_constraintTop_toTopOf="@id/item_activity_price_label_TextView"
        app:layout_constraintRight_toRightOf="parent"
        tools:text="$2.76（约¥18.55)" />

    <TextView
        android:id="@+id/item_activity_original_price_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="10dp"
        android:textColor="?theme_text_color_black"
        android:textSize="12sp"
        app:layout_constraintLeft_toRightOf="@id/item_activity_cover_Layout"
        app:layout_constraintTop_toBottomOf="@id/item_activity_price_value_TextView"
        tools:text="原     价:$4.32" />


</androidx.constraintlayout.widget.ConstraintLayout>