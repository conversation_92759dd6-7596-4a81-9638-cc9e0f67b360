<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_bg_color_white_1E"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/book_contents_title_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/top_title_height"
        android:background="?theme_bg_color_white_1E"
        app:attr_title_text="@string/catalogue" />

    <com.aquila.lib.widget.view.ElasticScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingLeft="20dp"
            android:paddingTop="13dp"
            android:paddingRight="20dp"
            android:paddingBottom="20dp">

            <TextView
                android:id="@+id/book_contents_details_TextView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:lineSpacingMultiplier="1.8"
                android:paddingLeft="10dp"
                android:textColor="?theme_text_color_dark"
                android:textSize="20sp"
                tools:text="版权\n编者序\n作者序\n导论\n综览\n   第一卷\n       第一章\n   第二卷\n       第一章\n       第二章\n尾声\n结语" />

        </LinearLayout>
    </com.aquila.lib.widget.view.ElasticScrollView>
</LinearLayout>