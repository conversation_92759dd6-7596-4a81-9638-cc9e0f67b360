<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="60dp"
    android:paddingLeft="15dp"
    android:paddingRight="15dp">

    <TextView
        android:id="@+id/item_activity_confirm_book_name_TextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginRight="32dp"
        android:ellipsize="end"
        android:padding="5dp"
        android:textColor="?theme_text_color_dark"
        android:textSize="16sp"
        android:maxLines="2"
        app:layout_constraintBottom_toTopOf="@id/item_activity_confirm_has_purchase_TextView"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/item_activity_confirm_price_TextView"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="仅此一时仅此一时仅1111一仅此一fadsa时仅此一时仅此一仅此一时仅此一时仅此一" />

    <TextView
        android:id="@+id/item_activity_confirm_has_purchase_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableRight="@drawable/ic_arrow_right_gray"
        android:drawablePadding="5dp"
        android:drawableTint="?theme_icon_tint_color"
        android:padding="5dp"
        android:textColor="?theme_text_color_dark"
        android:textSize="14sp"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/item_activity_confirm_book_name_TextView"
        tools:text="已购商品:-$78.1" />

    <TextView
        android:id="@+id/item_activity_confirm_price_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="5dp"
        android:textColor="?theme_text_color_dark"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="@id/item_activity_confirm_book_name_TextView"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/item_activity_confirm_book_name_TextView"
        tools:text="$32.34" />

</androidx.constraintlayout.widget.ConstraintLayout>