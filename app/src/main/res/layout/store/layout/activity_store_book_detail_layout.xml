<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_bg_color_white"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/book_detail_top_TitleLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/top_title_height"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_title_text="@string/book_review"
        app:layout_constraintTop_toTopOf="parent"
        tools:attr_title_text="书籍详情页">

        <ImageView
            android:id="@+id/book_detail_shopping_cart_ImageView"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_alignParentRight="true"
            android:background="?theme_comm_click_bg"
            android:scaleType="center"
            android:src="@drawable/ic_shopping_cart"
            android:visibility="gone"
            app:tint="?theme_icon_tint_color" />

        <TextView
            android:id="@+id/book_detail_shopping_count_TextView"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:layout_alignTop="@id/book_detail_shopping_cart_ImageView"
            android:layout_alignRight="@id/book_detail_shopping_cart_ImageView"
            android:layout_marginTop="8dp"
            android:layout_marginRight="8dp"
            android:background="?theme_circle_bg_red"
            android:gravity="center"
            android:textColor="?theme_text_color_white"
            android:textSize="8sp"
            android:visibility="gone"
            tools:text="2"
            tools:visibility="visible" />
    </com.wedevote.wdbook.ui.widgets.CommTopTitleLayout>

    <LinearLayout
        android:id="@+id/book_detail_bottom_buy_container_Layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?theme_bg_color_white_top_line"
        android:gravity="center_vertical"
        android:orientation="vertical"
        android:paddingTop="5dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:visibility="visible">

        <TextView
            android:id="@+id/book_detail_status_TextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:gravity="center"
            android:minHeight="30dp"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:text="@string/not_support_single_purchase"
            android:textColor="@color/text_color_gray_8D97A4"
            android:textSize="16sp"
            android:visibility="gone" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingLeft="15dp">

            <ImageView
                android:id="@+id/book_detail_favorite_ImageView"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_marginBottom="5dp"
                android:background="?theme_comm_click_bg"
                android:foreground="?selectableItemBackground"
                android:scaleType="center"
                android:src="@drawable/selector_ic_favorite" />

            <com.aquila.lib.widget.group.GroupImageTextLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:layout_marginBottom="5dp"
                android:padding="5dp"
                android:visibility="gone"
                app:attr_image_height="20dp"
                app:attr_image_src="@drawable/ic_shoppping_add"
                app:attr_image_src_tint="?theme_icon_tint_color"
                app:attr_image_width="20dp"
                app:attr_parent_orientation="IMAGE_TOP_TEXT_BOTTOM"
                app:attr_text="加入购物车"
                app:attr_text_color="?theme_text_color_black"
                app:attr_text_size="12sp" />

            <com.wedevote.wdbook.ui.widgets.ProgressButton
                android:id="@+id/book_detail_buy_Button"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="15dp"
                android:layout_marginBottom="5dp"
                android:background="@drawable/selector_all_conner_brown_bg"
                android:gravity="center"
                android:text="@string/buy"
                android:textColor="?theme_text_color_white"
                android:textSize="16sp"
                app:attr_bg_color="#D7DADE"
                app:attr_button_progress="0"
                app:attr_need_show_Progress="false"
                app:attr_progress_color="@color/color_E9973E"
                tools:visibility="visible" />

        </LinearLayout>

    </LinearLayout>

    <!--商品不存在的提示页面-->
    <LinearLayout
        android:id="@+id/book_detail_not_exist_container_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="?theme_bg_color_white"
        android:elevation="10dp"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/book_detail_top_TitleLayout"
        tools:visibility="gone">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="?theme_not_exist_icon"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.3" />

        <TextView
            android:id="@+id/book_detail_service_error"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="50dp"
            android:text="@string/service_error"
            android:textColor="?theme_text_color_black"
            android:textSize="16sp" />

        <Button
            android:id="@+id/book_detail_back_to_home_Button"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginLeft="50dp"
            android:layout_marginTop="50dp"
            android:layout_marginRight="50dp"
            android:background="@drawable/selector_all_conner_brown_bg"
            android:text="@string/label_back"
            android:textColor="?theme_text_color_white"
            android:textSize="16sp" />
    </LinearLayout>

    <com.aquila.lib.layout.SmartRefreshLayout
        android:id="@+id/book_detail_RefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/book_detail_bottom_buy_container_Layout"
        app:layout_constraintTop_toBottomOf="@id/book_detail_top_TitleLayout"
        app:srlEnableLoadMore="false"
        tools:visibility="visible">

        <com.aquila.lib.widget.view.ElasticScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:paddingBottom="20dp"
                android:showDividers="middle">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?theme_bg_color_white_bottom_line"
                    android:orientation="vertical">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="16dp"
                        >

                        <ImageView
                            android:id="@+id/book_detail_cover_ImageView"
                            android:layout_width="100dp"
                            android:layout_height="133dp"
                            android:scaleType="centerCrop"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:src="@drawable/ic_default_book_cover" />

                        <ImageView
                            android:id="@+id/book_detail_purchase_flag_ImageView"
                            android:layout_width="30dp"
                            android:layout_height="30dp"
                            android:scaleType="fitCenter"
                            android:src="@drawable/ic_book_purchased"
                            android:visibility="gone"
                            app:layout_constraintRight_toRightOf="@id/book_detail_cover_ImageView"
                            app:layout_constraintTop_toTopOf="@id/book_detail_cover_ImageView"
                            tools:visibility="visible" />

                        <TextView
                            android:id="@+id/book_detail_name_TextView"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="15dp"
                            android:textColor="?theme_text_color_black"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:ellipsize="end"
                            app:layout_constraintLeft_toRightOf="@id/book_detail_cover_ImageView"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toTopOf="@id/book_detail_cover_ImageView"
                            tools:text="精灵宝钻(精装插图本)" />

                        <TextView
                            android:id="@+id/book_detail_author_TextView"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:lines="1"
                            android:textColor="@color/color_blue_006FFF"
                            android:textSize="14sp"
                            app:layout_constraintLeft_toLeftOf="@id/book_detail_name_TextView"
                            app:layout_constraintRight_toLeftOf="@id/book_detail_author_ect_TextView"
                            app:layout_constraintTop_toBottomOf="@id/book_detail_name_TextView"
                            app:layout_constraintBottom_toTopOf="@id/book_detail_price_TextView"
                            app:maxCollapsedLines="3"
                            tools:text="J.R.R.托尔金J.R.R.托尔金J.R.R.托尔金J.R.R.托尔金J.R.R.托尔金J.R.R.托尔金J.R.R.托尔金J.R.R.托尔金J.R.R.托尔金J.R.R.托尔金" />

                        <TextView
                            android:id="@+id/book_detail_author_ect_TextView"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:lines="1"
                            android:textColor="?theme_text_color_black"
                            android:textSize="14sp"
                            app:arrowAlign="center"
                            app:arrowPadding="5dp"
                            app:arrowPosition="below"
                            app:collapseDrawable="?theme_ic_arrow_up"
                            app:expandDrawable="?theme_ic_arrow_down"
                            app:layout_constraintLeft_toRightOf="@id/book_detail_author_TextView"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toTopOf="@id/book_detail_author_TextView"
                            app:maxCollapsedLines="3"
                            android:visibility="gone"
                            android:text="等"
                            />

                        <TextView
                            android:id="@+id/book_detail_price_TextView"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="?theme_text_color_black"
                            android:textSize="14sp"
                            app:layout_constrainedWidth="true"
                            app:layout_constraintHorizontal_bias="0"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintLeft_toLeftOf="@id/book_detail_name_TextView"
                            app:layout_constraintTop_toBottomOf="@id/book_detail_author_TextView"
                            app:layout_constraintBottom_toTopOf="@id/book_detail_price_before_TextView"
                            tools:text="现价：是的v范德萨v发电设备v的方式的方式割发代首是的风格" />

                        <TextView
                            android:id="@+id/book_detail_price_before_TextView"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="?theme_text_color_black"
                            android:textSize="14sp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toLeftOf="@id/book_detail_name_TextView"
                            app:layout_constraintTop_toBottomOf="@id/book_detail_price_TextView"
                            tools:text="原价：" />

                    </androidx.constraintlayout.widget.ConstraintLayout>


                    <TextView
                        android:id="@+id/book_detail_disable_pay_TextView"
                        android:layout_width="match_parent"
                        android:layout_height="32dp"
                        android:layout_marginLeft="16dp"
                        android:layout_marginRight="16dp"
                        android:gravity="center"
                        android:text="@string/disable_payment_tips"
                        android:textColor="#FF342A"
                        android:textSize="12sp"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/book_detail_price_before_TextView"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/book_detail_tip_TextView"
                        android:layout_width="match_parent"
                        android:layout_height="32dp"
                        android:layout_marginLeft="16dp"
                        android:layout_marginRight="16dp"
                        android:background="@drawable/shape_brown_conner_bg"
                        android:gravity="center"
                        android:text="@string/this_book_can_use_in_wedevote"
                        android:textColor="@color/text_color_black_33"
                        android:textSize="12sp"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/book_detail_price_before_TextView"
                        tools:visibility="visible" />

                    <!--优惠券的UI-->
                    <LinearLayout
                        android:id="@+id/book_detail_coupon_container_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?theme_bg_color_white_bottom_line"
                        android:gravity="center_vertical"
                        android:minHeight="60dp"
                        android:orientation="horizontal"
                        android:visibility="visible"
                        tools:visibility="visible">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:minHeight="50dp"
                            android:paddingLeft="15dp"
                            android:paddingRight="15dp"
                            android:text="@string/label_discount"
                            android:textColor="?theme_text_color_black"
                            android:textSize="16sp" />

                        <com.aquila.lib.widget.view.CustomRecyclerView
                            android:id="@+id/book_detail_coupon_RecyclerView"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:attr_end_item_margin="10dp"
                            app:attr_item_margin="10dp"
                            app:attr_layout_style="list_horizontal"
                            app:attr_start_item_margin="0dp"
                            tools:itemCount="2"
                            tools:listitem="@layout/item_book_detail_coupon_layout" />
                    </LinearLayout>


                </LinearLayout>

                <com.aquila.lib.widget.view.CustomRecyclerView
                    android:id="@+id/book_detail_reference_RecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?theme_bg_color_white_bottom_line"
                    android:nestedScrollingEnabled="false"
                    android:paddingLeft="16dp"
                    android:paddingRight="16dp"
                    app:attr_layout_style="list_vertical"
                    tools:itemCount="1"
                    tools:listitem="@layout/item_book_detail_reference_layout" />

                <LinearLayout
                    android:id="@+id/book_detail_activity_container_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?theme_bg_color_white_bottom_line"
                    android:orientation="horizontal"
                    android:paddingLeft="15dp"
                    android:paddingTop="10dp"
                    android:paddingRight="15dp"
                    android:paddingBottom="10dp"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/book_detail_activity_title_TextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="活动：复活节专题活动，全场下单 5 折起 , 全场下单 5 折起 "
                        android:textColor="?theme_text_color_black"
                        android:textSize="16sp"
                        />

                    <com.aquila.lib.widget.group.GroupImageTextLayout
                        android:id="@+id/book_detail_view_Layout"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingLeft="5dp"
                        app:attr_image_src="@drawable/ic_arrow_right_gray"
                        app:attr_parent_orientation="IMAGE_RIGHT_TEXT_LEFT"
                        app:attr_text="@string/label_check_view"
                        app:attr_text_color="?theme_text_color_dark"
                        app:attr_text_margin_image_size="5dp"
                        app:attr_text_size="12sp"
                        />
                </LinearLayout>


                <com.aquila.lib.widget.view.CustomRecyclerView
                    android:id="@+id/book_detail_attribute_RecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    android:visibility="gone"
                    app:attr_divide_bottom_margin="10dp"
                    app:attr_divide_top_margin="10dp"
                    app:attr_item_decoration_type="ITEM_DIVIDE_LNE"
                    app:attr_item_divide_line_color="?theme_text_color_black"
                    app:attr_item_divide_line_size="@dimen/divide_line_size"
                    app:attr_item_margin="15dp"
                    app:attr_layout_style="list_horizontal"
                    tools:listitem="@layout/item_book_attribute_layout"
                    tools:visibility="visible" />

                <LinearLayout
                    android:id="@+id/book_detail_summary_LinearLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?theme_bg_color_white_bottom_line"
                    android:orientation="vertical"
                    android:paddingLeft="16dp"
                    android:paddingTop="10dp"
                    android:paddingRight="16dp"
                    android:paddingBottom="10dp">

                    <com.wedevote.wdbook.ui.widgets.ExpandTextView
                        android:id="@+id/book_detail_summary_TextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:lineSpacingMultiplier="1.2"
                        android:textColor="?theme_text_color_black"
                        android:textSize="16sp"
                        app:arrowAlign="center"
                        app:arrowPadding="10dp"
                        app:arrowPosition="below"
                        app:collapseDrawable="?theme_ic_arrow_up"
                        app:collapsed="false"
                        app:expandDrawable="?theme_ic_arrow_down"
                        app:maxCollapsedLines="5"
                        tools:text="丁道尔圣经注释是合乎时代的解经丛书丁道尔圣经注释是合乎时代的解经丛书丁道尔圣经注释是合乎时代的解经丛书丁道尔圣经注释是合乎时代的解经丛书丁道尔圣经注释是合乎时代的解经丛书丁道尔圣经注释是合乎时代的解经丛书" />
                </LinearLayout>

                <TextView
                    android:id="@+id/book_detail_catalog_TextView"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:background="?theme_bg_color_white_1E"
                    android:gravity="center"
                    android:text="@string/check_directory"
                    android:textColor="?theme_bg_color_white_blue"
                    android:textSize="14sp"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <com.aquila.lib.widget.view.CustomRecyclerView
                    android:id="@+id/book_detail_property_RecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?theme_bg_color_white_1E"
                    android:nestedScrollingEnabled="false"
                    android:paddingTop="16dp"
                    android:paddingBottom="16dp"
                    app:attr_layout_style="list_vertical"
                    tools:itemCount="3"
                    tools:listitem="@layout/item_book_detail_entry_layout" />

            </LinearLayout>
        </com.aquila.lib.widget.view.ElasticScrollView>
    </com.aquila.lib.layout.SmartRefreshLayout>

</androidx.constraintlayout.widget.ConstraintLayout>