<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/notes_recycle_mask_View"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#00000000"
        android:visibility="invisible"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/notes_recycle_container_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="50dp"
        android:background="?theme_bg_white_conner_top"
        android:orientation="vertical">

        <TextView
            android:id="@+id/notes_recycle_title_TextView"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:foreground="?android:attr/selectableItemBackground"
            android:gravity="center"
            android:text="@string/note_recycler"
            android:visibility="visible"
            android:textColor="?theme_text_color_black"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/notes_recycle_close_ImageView"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:background="@drawable/selector_item_click_circle_bg"
            android:foreground="?android:attr/selectableItemBackground"
            android:scaleType="center"
            android:visibility="visible"
            android:src="?theme_dialog_close_ImageView"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="?theme_bg_color_divide_line_color"
            app:layout_constraintBottom_toBottomOf="@id/notes_recycle_title_TextView"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

        <LinearLayout
            android:id="@+id/notes_recycle_no_data_LinearLayout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/notes_recycle_clear_Button"
            app:layout_constraintTop_toBottomOf="@id/notes_recycle_title_TextView"
            tools:visibility="visible">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="156dp"
                android:gravity="center_horizontal"
                android:text="@string/no_notes"
                android:textColor="?attr/theme_text_color_gray_8A8A"
                android:textSize="18sp"
                android:textStyle="bold" />
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:gravity="center_horizontal"
                android:text="@string/delete_notes_can_be_find_here"
                android:textColor="?attr/theme_text_color_gray_8A8A"
                android:textSize="14sp" />
        </LinearLayout>

        <com.aquila.lib.layout.SmartRefreshLayout
            android:id="@+id/notes_recycle_data_SmartRefreshLayout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@id/notes_recycle_clear_Button"
            app:layout_constraintTop_toBottomOf="@id/notes_recycle_title_TextView"
            app:srlEnableLoadMore="false"
            tools:visibility="gone">

            <com.aquila.lib.widget.view.CustomRecyclerView
                android:id="@+id/notes_recycle_data_RecyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:paddingLeft="16dp"
                android:paddingRight="16dp"
                android:visibility="visible"
                app:attr_item_decoration_type="ITEM_DIVIDE_LNE"
                app:attr_item_divide_line_color="?theme_bg_color_divide_line_D3"
                app:attr_item_divide_line_size="@dimen/divide_line_size"
                app:attr_layout_style="list_vertical"
                tools:listitem="@layout/holder_item_book_notes_recycle_layout" />
        </com.aquila.lib.layout.SmartRefreshLayout>

        <View
            android:id="@+id/notes_recycle_bottom_line_View"
            android:layout_width="match_parent"
            android:layout_height="@dimen/divide_line_size"
            android:background="?theme_bg_color_divide_line_color"
            app:layout_constraintBottom_toTopOf="@id/notes_recycle_clear_Button" />

        <Button
            android:id="@+id/notes_recycle_clear_Button"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="?theme_comm_click_bg"
            android:foreground="?android:attr/selectableItemBackground"
            android:gravity="center"
            android:text="@string/clean_recyclerbin"
            android:visibility="gone"
            android:textColor="@color/color_red_C13013"
            android:textSize="14sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>