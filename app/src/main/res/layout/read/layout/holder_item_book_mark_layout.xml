<?xml version="1.0" encoding="utf-8"?>
<com.chauthai.swipereveallayout.SwipeRevealLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:dragEdge="right"
    app:mode="same_level">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent">

        <Button
            android:id="@+id/book_mark_delete_Button"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:background="@color/color_red_FF3B30"
            android:gravity="center"
            android:minWidth="70dp"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:text="@string/label_delete"
            android:textColor="?theme_text_color_white"
            android:textSize="16sp" />

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/book_mark_ConstraintLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/selector_comm_click_bg"
        android:foreground="?android:attr/selectableItemBackground"
        android:paddingTop="10dp"
        android:paddingRight="8dp"
        android:paddingBottom="10dp">

        <ImageView
            android:id="@+id/book_mark_flag_ImageView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="30dp"
            android:minHeight="30dp"
            android:scaleType="center"
            android:src="@drawable/ic_bookmark_gray"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="@id/book_mark_name_TextView" />


        <TextView
            android:id="@+id/book_mark_name_TextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:minHeight="30dp"
            android:singleLine="true"
            android:textColor="?theme_text_color_black"
            android:textSize="14sp"
            android:textStyle="bold"
            app:layout_constraintLeft_toRightOf="@id/book_mark_flag_ImageView"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="第一章" />

        <TextView
            android:id="@+id/book_mark_summary_TextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="3dp"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:maxLines="2"
            android:minHeight="30dp"
            android:textColor="?theme_text_color_black"
            android:textSize="14sp"
            app:layout_constraintLeft_toLeftOf="@id/book_mark_name_TextView"
            app:layout_constraintRight_toRightOf="@id/book_mark_name_TextView"
            app:layout_constraintTop_toBottomOf="@+id/book_mark_name_TextView"
            tools:text="第一章 树大根深　　“再会吧！再会吧！亲爱的妈妈、爸爸！回头见！”　　这个十岁的孩子有着成年人般的气质，穿着黑色西服套装和浆洗过的衬衣，袖口带" />

        <TextView
            android:id="@+id/book_mark_date_TextView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:gravity="center_vertical|right"
            android:textColor="@color/text_color_gray_8A8A8A"
            android:textSize="12sp"
            app:layout_constraintLeft_toLeftOf="@id/book_mark_name_TextView"
            app:layout_constraintRight_toRightOf="@id/book_mark_name_TextView"
            app:layout_constraintTop_toBottomOf="@+id/book_mark_summary_TextView"
            tools:text="2020-09-10 10:32" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.chauthai.swipereveallayout.SwipeRevealLayout>