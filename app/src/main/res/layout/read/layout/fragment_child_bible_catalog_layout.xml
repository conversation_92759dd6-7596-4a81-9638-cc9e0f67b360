<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/child_bible_container_layout"
    android:paddingLeft="14dp"
    android:paddingRight="14dp"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <GridView
        android:id="@+id/child_bible_book_name_GridView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:horizontalSpacing="2dp"
        android:numColumns="5"
        android:padding="3dp"
        android:verticalSpacing="2dp"
        tools:listitem="@layout/item_bible_catalog_layout"></GridView>

    <GridView
        android:id="@+id/child_bible_chapter_GridView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:horizontalSpacing="2dp"
        android:numColumns="5"
        android:padding="3dp"
        android:verticalSpacing="2dp"
        android:visibility="gone"
        tools:listitem="@layout/item_bible_catalog_layout"></GridView>

    <GridView
        android:id="@+id/child_bible_verse_GridView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:horizontalSpacing="2dp"
        android:numColumns="5"
        android:visibility="gone"
        android:padding="3dp"
        android:verticalSpacing="2dp"
        tools:listitem="@layout/item_bible_catalog_layout"></GridView>


</FrameLayout>