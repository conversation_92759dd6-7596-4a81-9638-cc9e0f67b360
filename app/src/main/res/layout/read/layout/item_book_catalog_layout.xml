<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/item_catalog_triangle_ImageView"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:scaleType="center"
        android:src="?theme_ic_triangle_black_down"
        android:visibility="invisible"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/item_catalog_title_TextView"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:ellipsize="end"
        android:gravity="center_vertical|left"
        android:textDirection="ltr"
        android:singleLine="true"
        android:text="次级目录标题"
        android:textColor="?theme_text_color_black"
        android:textSize="16sp"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/item_catalog_triangle_ImageView"
        app:layout_constraintLeft_toRightOf="@id/item_catalog_triangle_ImageView"
        app:layout_constraintRight_toLeftOf="@id/item_catalog_desc_TextView"
        app:layout_constraintTop_toTopOf="@id/item_catalog_triangle_ImageView" />

    <TextView
        android:id="@+id/item_catalog_desc_TextView"
        android:layout_width="wrap_content"
        android:layout_height="50dp"
        android:gravity="center_vertical"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:singleLine="true"
        android:text="3425"
        android:textColor="?theme_text_color_gray"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="@id/item_catalog_triangle_ImageView"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/item_catalog_triangle_ImageView" />

</androidx.constraintlayout.widget.ConstraintLayout>