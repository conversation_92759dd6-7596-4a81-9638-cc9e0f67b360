<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical">

    <androidx.cardview.widget.CardView
        android:id="@+id/scope_last_title_CardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="20dp"
        android:visibility="gone"
        app:cardCornerRadius="7.5dp"
        tools:visibility="visible"
        app:cardElevation="5dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="5dp"
            android:paddingBottom="5dp"
            android:background="?theme_bg_color_white_44">

            <TextView
                android:id="@+id/scope_index_TextView"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:gravity="center_vertical"
                android:paddingLeft="15dp"
                android:paddingRight="15dp"
                android:text="102/345"
                android:textColor="?theme_text_color_black_11243D"
                android:textSize="12sp"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toTopOf="@id/scope_last_chapter_TextView"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@id/scope_line_View"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/scope_last_chapter_TextView"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:gravity="center_vertical"
                android:paddingLeft="15dp"
                android:paddingRight="15dp"
                android:text="当前标题"
                android:textColor="?theme_text_color_black_11243D"
                android:textSize="16sp"
                android:textDirection="ltr"
                android:lines="1"
                android:ellipsize="end"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@id/scope_line_View"
                app:layout_constraintTop_toBottomOf="@id/scope_index_TextView" />

            <ImageView
                android:id="@+id/scope_revoke_ImageView"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:scaleType="center"
                android:src="?theme_selector_book_scope_revoke"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/scope_line_View"
                android:layout_width="@dimen/divide_line_size"
                android:layout_height="0dp"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:background="?theme_bg_color_divide_line_8E8E93"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toLeftOf="@id/scope_revoke_ImageView"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:background="?theme_bg_color_white_top_line">

        <ImageView
            android:id="@+id/scope_prev_ImageView"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:background="@drawable/selector_comm_click_bg"
            android:rotation="90"
            android:scaleType="center"
            android:src="?theme_ic_arrow_down"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/scope_next_ImageView"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:background="@drawable/selector_comm_click_bg"
            android:rotation="-90"
            android:scaleType="center"
            android:src="?theme_ic_arrow_down"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <SeekBar
            android:id="@+id/scope_chapter_SeekBar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:max="100"
            android:maxHeight="2.0dp"
            android:minHeight="2.0dp"
            android:progress="30"
            android:progressDrawable="?theme_bg_scope_chapter_SeekBar"
            android:thumb="@drawable/ic_seekbar_thumb"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/scope_prev_ImageView"
            app:layout_constraintRight_toLeftOf="@+id/scope_next_ImageView"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>