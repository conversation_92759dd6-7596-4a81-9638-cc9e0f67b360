<?xml version="1.0" encoding="utf-8"?>

<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:fitsSystemWindows="true"
    android:layout_height="match_parent">

    <View
        android:id="@+id/catalog_tool_mask_View"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#8000"
        android:visibility="invisible"
        tools:visibility="visible" />

    <LinearLayout
        android:id="@+id/catalog_tool_root_container_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="50dp"
        android:background="?theme_bg_white_conner_top"
        android:orientation="vertical"
        android:visibility="invisible"
        tools:visibility="visible">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="60dp">

            <ImageView
                android:id="@+id/catalog_tool_back_ImageView"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:padding="8dp"
                android:background="@drawable/selector_item_click_circle_bg"
                android:foreground="?android:attr/selectableItemBackground"
                app:tint="?theme_icon_tint_color"
                tools:ignore="UseAppTint"
                android:src="@drawable/ic_in_book_left_arrow"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/catalog_tool_close_ImageView"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:background="@drawable/selector_item_click_circle_bg"
                android:foreground="?android:attr/selectableItemBackground"
                android:scaleType="center"
                android:src="?theme_dialog_close_ImageView"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/catalog_tool_bible_title_TextView"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:gravity="center"
                android:text="@string/catalogue"
                android:textColor="?theme_text_color_black"
                android:textSize="16sp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/catalog_tool_close_ImageView"
                app:layout_constraintLeft_toRightOf="@id/catalog_tool_back_ImageView"
                app:layout_constraintRight_toLeftOf="@id/catalog_tool_close_ImageView"
                app:layout_constraintTop_toTopOf="@id/catalog_tool_close_ImageView" />

            <LinearLayout
                android:id="@+id/catalog_tool_tab_container_layout"
                android:layout_width="wrap_content"
                android:layout_height="30dp"
                android:layout_marginTop="15dp"
                android:background="?theme_bg_white_conner_6dp_e3"
                android:orientation="horizontal"
                android:padding="2dp"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="@id/catalog_tool_close_ImageView"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@id/catalog_tool_close_ImageView">

                <TextView
                    android:id="@+id/catalog_tool_book_contents_TextView"
                    android:layout_width="124dp"
                    android:layout_height="match_parent"
                    android:background="?theme_bg_selector_white_conner_6dp"
                    android:gravity="center"
                    android:text="@string/book_catalogue"
                    android:textColor="?theme_text_color_black"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/catalog_tool_bible_contents_TextView"
                    android:layout_width="124dp"
                    android:layout_height="match_parent"
                    android:background="?theme_bg_selector_white_conner_6dp"
                    android:gravity="center"
                    android:text="@string/table_of_contents"
                    android:textColor="?theme_text_color_black"
                    android:textSize="14sp" />
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/catalog_tool_ViewPager"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />


    </LinearLayout>
</FrameLayout>
