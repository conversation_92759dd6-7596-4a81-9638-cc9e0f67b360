<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?theme_bg_color_white_top_line"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="60dp"

        android:padding="10dp">

        <ImageView
            android:id="@+id/brightness_down_ImageView"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_centerVertical="true"
            android:padding="13dp"
            android:scaleType="fitCenter"
            android:src="?theme_ic_brightness" />


        <ImageView
            android:id="@+id/brightness_up_ImageView"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:padding="10dp"
            android:scaleType="fitCenter"
            android:src="?theme_ic_brightness" />

        <SeekBar
            android:id="@+id/brightness_set_SeekBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@id/brightness_up_ImageView"
            android:layout_toRightOf="@id/brightness_down_ImageView"
            android:max="255"
            android:maxHeight="2.0dp"
            android:minHeight="2.0dp"
            android:progressDrawable="?theme_bg_brightness_set_SeekBar"
            android:thumb="@drawable/ic_seekbar_thumb"
            tools:progress="50" />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="85dp"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/brightness_theme_white_ImageView"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_margin="15dp"
            android:background="@drawable/selector_circle_theme_white"
            android:scaleType="center" />

        <ImageView
            android:id="@+id/brightness_theme_night_ImageView"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_margin="15dp"
            android:background="@drawable/selector_circle_theme_night_bg"
            android:padding="15dp"
            android:scaleType="fitCenter"
            android:src="@drawable/ic_theme_black_mode" />

    </LinearLayout>


</LinearLayout>