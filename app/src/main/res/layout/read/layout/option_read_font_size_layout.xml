<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?theme_bg_color_white_top_line"
    android:layout_gravity="bottom"
    android:orientation="vertical">

    <!--<androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white">

        <TextView
            android:id="@+id/option_jump_last_chapter_TextView"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:gravity="center_vertical"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:text="当前标题"
            android:textColor="@color/text_color_dark_11243D"
            android:textSize="16sp"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/option_jump_line_View"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/option_jump_revoke_ImageView"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:scaleType="center"
            android:src="@drawable/ic_revoke_black"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/option_jump_line_View"
            android:layout_width="@dimen/divide_line_size"
            android:layout_height="20dp"
            android:background="@color/divide_line_color"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toLeftOf="@id/option_jump_revoke_ImageView"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>-->
    <!-- <LinearLayout
         android:layout_width="match_parent"
         android:layout_height="50dp"
         android:orientation="horizontal"
         android:background="@color/white">
         <TextView
             android:id="@+id/option_jump_zoom_in"
             android:layout_height="match_parent"
             android:layout_width="match_parent"
             android:textSize="16sp"
             android:gravity="center"
             android:text = "A+"
             android:background="@drawable/selector_button_bg_white_gray_stoke"
             android:layout_weight="1"
             android:layout_margin="5dp"/>
         <TextView
             android:id="@+id/option_jump_zoom_out"
             android:layout_height="match_parent"
             android:layout_width="match_parent"
             android:textSize="14sp"
             android:gravity="center"
             android:text = "A-"
             android:background="@drawable/selector_button_bg_white_gray_stoke"
             android:layout_weight="1"
             android:layout_margin="5dp"/>
     </LinearLayout>-->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:paddingRight="10dp"
        android:paddingLeft="10dp">

        <TextView
            android:id="@+id/option_text_size_minus_TextView"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:text="A"
            android:textColor="?theme_text_color_black"
            android:textSize="20sp" />


        <TextView
            android:id="@+id/option_text_size_plus_TextView"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:text="A"
            android:textColor="?attr/theme_text_color_black"
            android:textSize="30sp" />

        <SeekBar
            android:id="@+id/option_text_size_SeekBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@id/option_text_size_plus_TextView"
            android:layout_toRightOf="@id/option_text_size_minus_TextView"
            android:max="9"
            android:maxHeight="2.0dp"
            android:minHeight="2.0dp"
            android:progress="3"
            android:progressDrawable="?theme_bg_scope_chapter_SeekBar"
            android:thumb="@drawable/ic_seekbar_thumb" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="63dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:background="?theme_bg_color_white_top_line"
        android:layout_marginLeft="24dp"
        >
        <TextView
            android:layout_height="wrap_content"
            android:layout_width="wrap_content"
            android:textSize="16sp"
            android:gravity="center"
            android:text = "@string/font"
            android:textColor="?theme_text_color_black_33"
            />
        <TextView
            android:id="@+id/read_system_font_TextView"
            android:layout_height="36dp"
            android:layout_width="0dp"
            android:textSize="16sp"
            android:gravity="center"
            android:text = "@string/system_font"
            android:textColor="?theme_selector_font_text_color"
            android:background="?theme_selector_font_bg"
            android:layout_marginLeft="14dp"
            android:layout_weight="1"/>
        <TextView
            android:id="@+id/read_serif_font_TextView"
            android:layout_height="36dp"
            android:layout_width="0dp"
            android:textSize="16sp"
            android:gravity="center"
            android:textColor="?theme_selector_font_text_color"
            android:text = "@string/source_han_serif"
            android:layout_marginRight="24dp"
            android:layout_marginLeft="21dp"
            android:background="?theme_selector_font_bg"
            android:layout_weight="1"/>
    </LinearLayout>

</LinearLayout>