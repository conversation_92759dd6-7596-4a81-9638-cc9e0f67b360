<?xml version="1.0" encoding="utf-8"?>
<com.wedevote.wdbook.ui.widgets.SquaredLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/selector_white_bg_gray_stoke_bg"
    android:gravity="center"
    android:orientation="vertical">

    <TextView
        android:id="@+id/item_bible_abbr_name_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:singleLine="true"
        android:text="提前"
        android:textColor="?theme_selector_text_color_black_and_blue"
        android:textSize="24sp" />

    <TextView
        android:id="@+id/item_bible_full_name_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:singleLine="true"
        android:text="帖撒罗尼迦前书"
        android:textColor="?theme_selector_text_color_selected_gray_and_blue"
        android:textSize="10sp" />

</com.wedevote.wdbook.ui.widgets.SquaredLinearLayout>