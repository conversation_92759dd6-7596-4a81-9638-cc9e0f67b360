<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/book_read_tool_ConstraintLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="false">

    <com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
        android:id="@+id/book_read_tool_title_Layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/top_title_height"
        android:background="?theme_bg_color_white_bottom_line"
        app:attr_max_title_width="200dp"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/book_search_ImageView"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:padding="12dp"
            android:layout_toStartOf="@id/book_note_ImageView"
            android:background="?theme_comm_click_bg"
            android:foreground="?android:attr/selectableItemBackground"
            android:src="@drawable/ic_in_book_search"
            app:tint="?theme_icon_tint_color_black"
            tools:ignore="UseAppTint" />

        <ImageView
            android:id="@+id/book_note_ImageView"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_toStartOf="@id/book_mark_ImageView"
            android:background="?theme_comm_click_bg"
            android:foreground="?android:attr/selectableItemBackground"
            android:scaleType="center"
            android:src="?theme_ic_book_note" />

        <ImageView
            android:id="@+id/book_mark_ImageView"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_alignParentEnd="true"
            android:background="?theme_comm_click_bg"
            android:foreground="?android:attr/selectableItemBackground"
            android:scaleType="center"
            android:src="?theme_ic_bookmark_outlined" />
    </com.wedevote.wdbook.ui.widgets.CommTopTitleLayout>

    <LinearLayout
        android:id="@+id/book_read_tool_bottom_container_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent">

        <FrameLayout
            android:id="@+id/book_read_tool_option_container_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/book_read_tool_contents_ImageView"
            tools:visibility="visible">

            <com.wedevote.wdbook.ui.read.widgets.OptionBrightnessLayout
                android:id="@+id/book_read_tool_brightness_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone" />

            <com.wedevote.wdbook.ui.read.widgets.OptionFontLayout
                android:id="@+id/book_read_tool_read_jump_Layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone" />

            <com.wedevote.wdbook.ui.read.widgets.OptionScopeLayout
                android:id="@+id/book_read_tool_scope_Layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </FrameLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/book_read_tool_menu_layout"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="?theme_bg_color_white_top_line">

            <ImageView
                android:id="@+id/book_read_tool_contents_ImageView"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:scaleType="center"
                android:src="?theme_ic_arrange_list_black"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHorizontal_chainStyle="spread"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@id/book_read_tool_scope_ImageView"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/book_read_tool_scope_ImageView"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:scaleType="center"
                android:src="?theme_ic_book_scope"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@id/book_read_tool_contents_ImageView"
                app:layout_constraintRight_toLeftOf="@id/book_read_tool_font_ImageView"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/book_read_tool_font_ImageView"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:scaleType="center"
                android:src="?theme_ic_font_black"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@id/book_read_tool_scope_ImageView"
                app:layout_constraintRight_toLeftOf="@id/book_read_tool_bright_ImageView"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/book_read_tool_bright_ImageView"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:scaleType="center"
                android:src="?theme_ic_brightness_bottom"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@id/book_read_tool_font_ImageView"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>


    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
