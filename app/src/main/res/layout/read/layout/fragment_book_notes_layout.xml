<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/book_notes_no_data_LinearLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:orientation="vertical"
        android:layout_above="@id/notes_recycle_bin_TextView"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="156dp"
            android:gravity="center_horizontal"
            android:text="@string/no_notes"
            android:textColor="?attr/theme_text_color_gray_8A8A"
            android:textSize="18sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:gravity="center_horizontal"
            android:text="@string/click_note_to_create"
            android:textColor="?attr/theme_text_color_gray_8A8A"
            android:textSize="14sp" />
    </LinearLayout>

    <com.aquila.lib.layout.SmartRefreshLayout
        android:id="@+id/book_notes_data_SmartRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:srlEnableLoadMore="false"
        android:layout_above="@id/notes_recycle_bin_TextView"
        tools:visibility="gone">
        <com.aquila.lib.widget.view.CustomRecyclerView
            android:id="@+id/book_notes_data_RecyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingLeft="16dp"
            android:paddingRight="16dp"
            android:visibility="visible"
            app:attr_item_decoration_type="ITEM_DIVIDE_LNE"
            app:attr_item_divide_line_color="?theme_bg_color_divide_line_D3"
            app:attr_item_divide_line_size="@dimen/divide_line_size"
            app:attr_layout_style="list_vertical"
            tools:listitem="@layout/holder_book_item_note_layout" />
    </com.aquila.lib.layout.SmartRefreshLayout>

    <TextView
        android:id="@+id/notes_recycle_bin_TextView"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="?theme_bg_color_white_top_line"
        android:foreground="?android:attr/selectableItemBackground"
        android:gravity="center"
        android:text="@string/note_recyclerBin"
        android:textColor="?theme_text_color_blue_book_notes"
        android:textSize="14sp"
        android:textStyle="bold"
        android:layout_alignParentBottom="true" />
</RelativeLayout>