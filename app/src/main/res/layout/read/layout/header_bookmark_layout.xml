<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#aaa"
    android:gravity="center"
    android:elevation="-50dp"
    android:minHeight="70dp"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/refresh_header_arrow_ImageView"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_centerVertical="true"
        android:layout_marginEnd="20dp"
        android:layout_marginRight="20dp"
        android:contentDescription="@android:string/untitled"
        android:src="@drawable/ic_arrow_white_top"
        app:layout_constraintBottom_toBottomOf="@id/refresh_header_bookmark_flag_ImageView"
        app:layout_constraintRight_toLeftOf="@id/refresh_header_title_TextView"
        app:layout_constraintTop_toTopOf="@id/refresh_header_bookmark_flag_ImageView" />

    <TextView
        android:id="@+id/refresh_header_title_TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minWidth="100dp"
        android:layout_centerVertical="true"
        android:maxLines="1"
        android:text="@string/pull_down_add_bookmark"
        android:textColor="@color/white"
        android:textSize="15sp"
        app:layout_constraintBottom_toBottomOf="@id/refresh_header_bookmark_flag_ImageView"
        app:layout_constraintRight_toLeftOf="@id/refresh_header_bookmark_flag_ImageView"
        app:layout_constraintTop_toTopOf="@id/refresh_header_bookmark_flag_ImageView" />

    <ImageView
        android:id="@+id/refresh_header_bookmark_flag_ImageView"
        android:layout_width="wrap_content"
        android:layout_height="70dp"
        android:visibility="invisible"
        android:src="@drawable/ic_bookmark_long_red"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>