<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/book_mark_no_data_LinearLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="gone">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="156dp"
            android:gravity="center_horizontal"
            android:text="@string/no_bookmarks"
            android:textColor="?attr/theme_text_color_gray_8A8A"
            android:textSize="18sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:gravity="center_horizontal"
            android:text="@string/drag_click_add_bookmark"
            android:textColor="?attr/theme_text_color_gray_8A8A"
            android:textSize="14sp" />
    </LinearLayout>

    <com.aquila.lib.layout.SmartRefreshLayout
        android:id="@+id/book_mark_SmartRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.aquila.lib.widget.view.CustomRecyclerView
            android:id="@+id/book_mark_data_RecyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:visibility="visible"
            app:attr_item_decoration_type="ITEM_DIVIDE_LNE"
            app:attr_item_divide_line_color="?theme_bg_color_divide_line_D3"
            app:attr_item_divide_line_size="@dimen/divide_line_size"
            app:attr_layout_style="list_vertical"
            tools:listitem="@layout/holder_item_book_mark_layout" />
    </com.aquila.lib.layout.SmartRefreshLayout>
</RelativeLayout>