<?xml version="1.0" encoding="utf-8"?>

<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:fitsSystemWindows="true"
    android:layout_height="match_parent">

    <View
        android:id="@+id/book_note_mask_View"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="#8000"
        android:visibility="invisible"
        tools:visibility="visible" />

    <LinearLayout
        android:id="@+id/book_note_root_container_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="50dp"
        android:background="?theme_bg_white_conner_top"
        android:orientation="vertical"
        android:visibility="visible"
        tools:visibility="visible">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="60dp">


            <ImageView
                android:id="@+id/book_note_close_ImageView"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:background="@drawable/selector_item_click_circle_bg"
                android:foreground="?android:attr/selectableItemBackground"
                android:scaleType="center"
                android:src="?theme_dialog_close_ImageView"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


            <LinearLayout
                android:id="@+id/book_note_tab_container_layout"
                android:layout_width="wrap_content"
                android:layout_height="30dp"
                android:layout_marginTop="15dp"
                android:layout_marginBottom="10dp"
                android:background="?theme_bg_white_conner_6dp_e3"
                android:orientation="horizontal"
                android:padding="2dp"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@id/book_note_close_ImageView">

                <TextView
                    android:id="@+id/book_note_bookNote_TextView"
                    android:layout_width="124dp"
                    android:layout_height="match_parent"
                    android:background="?theme_bg_selector_white_conner_6dp"
                    android:gravity="center"
                    android:textStyle="bold"
                    android:text="@string/notes"
                    android:textColor="?theme_text_color_black"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/book_note_bookmark_TextView"
                    android:layout_width="124dp"
                    android:layout_height="match_parent"
                    android:background="?theme_bg_selector_white_conner_6dp"
                    android:gravity="center"
                    android:textStyle="bold"
                    android:text="@string/book_mark"
                    android:textColor="?theme_text_color_black"
                    android:textSize="14sp" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="?theme_bg_color_divide_line_color"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>
</FrameLayout>
