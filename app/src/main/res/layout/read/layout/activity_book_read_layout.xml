<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?theme_root_view_bg">

    <com.wedevote.wdbook.ui.read.BookReadViewPager
        android:id="@+id/book_content_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:attr_is_slide_able="true"
        app:attr_layout_orientation="HORIZONTAL"
        tools:ignore="SpeakableTextPresentCheck" />

    <com.wedevote.wdbook.ui.read.widgets.OptionLinkJumpBackLayout
        android:id="@+id/book_link_option_layout"
        android:layout_width="200dp"
        android:layout_height="32dp"
        android:layout_gravity="center_horizontal|bottom"
        android:layout_marginBottom="10dp"
        android:visibility="gone"
        tools:visibility="visible" />

    <View
        android:id="@+id/book_mask_View"
        android:layout_width="match_parent"
        android:layout_height="@dimen/book_text_title_height"
        android:background="?theme_bg_color_white" />

    <com.wedevote.wdbook.ui.read.widgets.BookSearchResultLayout
        android:id="@+id/book_search_result_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <com.wedevote.wdbook.ui.read.widgets.BookReadToolLayout
        android:id="@+id/book_tool_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</FrameLayout>