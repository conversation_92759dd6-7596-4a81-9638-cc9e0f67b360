<?xml version="1.0" encoding="utf-8"?>

<com.chauthai.swipereveallayout.SwipeRevealLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:dragEdge="right"
    app:mode="same_level">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <Button
            android:id="@+id/notes_recycle_recover_Button"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:background="@color/color_4E4E4E"
            android:gravity="center"
            android:minWidth="70dp"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:text="@string/recover"
            android:textColor="?theme_text_color_white"
            android:textSize="16sp" />

        <Button
            android:id="@+id/notes_recycle_delete_Button"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:background="@color/color_red_E53935"
            android:gravity="center"
            android:minWidth="70dp"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:text="@string/label_delete"
            android:textColor="?theme_text_color_white"
            android:textSize="16sp" />

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/item_notes_recycle_content_ConstraintLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/selector_comm_click_bg"
        android:foreground="?android:attr/selectableItemBackground"
        android:paddingLeft="16dp"
        android:paddingRight="16dp"
        android:paddingBottom="1dp">

        <com.aquila.lib.widget.view.DotView
            android:id="@+id/item_notes_recycle_page_number_DotView"
            android:layout_width="10dp"
            android:layout_height="10dp"
            app:attr_dot_color="@color/text_color_FDA4A5"
            app:attr_dot_radius="5dp"
            app:layout_constraintBottom_toBottomOf="@id/item_notes_recycle_toc_title_TextView"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/item_notes_recycle_toc_title_TextView"
            app:layout_constraintTop_toTopOf="@id/item_notes_recycle_toc_title_TextView" />

        <TextView
            android:id="@+id/item_notes_recycle_toc_title_TextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:ellipsize="end"
            android:gravity="center"
            android:textDirection="ltr"
            android:paddingLeft="8dp"
            android:paddingRight="8dp"
            android:singleLine="true"
            android:textColor="?theme_text_color_black_4c_8a"
            android:textSize="12sp"
            app:layout_constraintLeft_toRightOf="@id/item_notes_recycle_page_number_DotView"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="这个是章节的标题" />


        <TextView
            android:id="@+id/item_notes_recycle_quote_text_TextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:layout_marginTop="8dp"
            android:ellipsize="end"
            android:gravity="center_vertical|left"
            android:lineSpacingExtra="5dp"
            android:maxLines="2"
            android:minHeight="30dp"
            android:textDirection="ltr"
            android:textColor="?theme_text_color_note_gray"
            android:textSize="14sp"
            app:layout_constrainedWidth="true"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintLeft_toRightOf="@id/item_notes_recycle_vertical_line_View"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/item_notes_recycle_toc_title_TextView"
            tools:text="大多数现代圣经译本都大多数现代圣经译本都大多数现代圣经译本都" />


        <View
            android:id="@+id/item_notes_recycle_vertical_line_View"
            android:layout_width="2dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="@id/item_notes_recycle_quote_text_TextView"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="@id/item_notes_recycle_quote_text_TextView"
            tools:background="?theme_text_color_gray_BC_88" />


        <TextView
            android:id="@+id/item_notes_recycle_content_TextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="8dp"
            android:layout_marginTop="8dp"
            android:ellipsize="end"
            android:gravity="center_vertical|left"
            android:textDirection="ltr"
            android:lineSpacingMultiplier="1.1"
            android:maxLines="2"
            android:textColor="?theme_text_color_note_black"
            android:textSize="14sp"
            app:layout_constraintLeft_toRightOf="@id/item_notes_recycle_vertical_line_View"
            app:layout_constraintTop_toBottomOf="@id/item_notes_recycle_quote_text_TextView"
            tools:text="耶稣基督，有权能拯救、更新这个世界被罪破坏的一切更新这个世界被罪破坏的一切更新这个世界被罪破坏的一切更新这个世界被罪破坏的一切更新这个世界被罪破坏的一切更新这个世界被罪破坏的一切，" />

        <TextView
            android:id="@+id/item_notes_recycle_date_TextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center_vertical|right"
            android:minHeight="30dp"
            android:paddingLeft="10dp"
            android:paddingRight="0dp"
            android:singleLine="true"
            android:text="2020-09-10 10:32"
            android:textColor="@color/text_color_gray_8A8A8A"
            android:textSize="12sp"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/item_notes_recycle_content_TextView" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</com.chauthai.swipereveallayout.SwipeRevealLayout>