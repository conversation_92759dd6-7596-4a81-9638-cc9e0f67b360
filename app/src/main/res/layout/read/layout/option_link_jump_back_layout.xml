<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="32dp"
    android:layout_gravity="center_horizontal"
    android:background="?theme_shape_all_conner_black">

    <LinearLayout
        android:id="@+id/option_link_back_LinearLayout"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingLeft="10dp"
        android:paddingRight="10dp">

        <ImageView
            android:id="@+id/option_link_back_icon"
            android:layout_width="6dp"
            android:layout_height="10dp"
            android:src="@drawable/ic_back_white"
            android:tint="?theme_icon_tint_color_white"
            tools:ignore="UseAppTint" />

        <TextView
            android:id="@+id/option_link_back_TextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="@string/back_to_page"
            android:textColor="?theme_text_color_white"
            android:textSize="12sp" />
    </LinearLayout>

    <TextView
        android:id="@+id/option_link_stay_TextView"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentRight="true"
        android:gravity="center"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:text="@string/stay_current_page"
        android:textColor="?theme_text_color_white"
        android:textSize="12sp" />
</RelativeLayout>

