<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.aquila.lib.widget.view.CustomRecyclerView
        android:id="@+id/book_notes_catalog_RecyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginRight="9dp"
        android:nestedScrollingEnabled="false"
        tools:listitem="@layout/holder_item_book_catalog_layout" />

    <TextView
        android:id="@+id/book_notes_catalog_TextView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:background="?theme_bg_color_white_1E"
        android:gravity="center"
        android:visibility="gone"
        android:text="@string/label_dialog_loading"
        android:textColor="?theme_text_color_gray"
        android:textSize="18sp" />
</RelativeLayout>