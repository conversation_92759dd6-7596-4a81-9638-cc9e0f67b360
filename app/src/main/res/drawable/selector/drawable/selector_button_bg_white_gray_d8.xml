<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="false">
        <shape>
            <corners android:radius="100dp" />
            <solid android:color="@color/white" />
            <stroke android:width="@dimen/divide_line_size" android:color="@color/text_color_gray_d8" />
        </shape>
    </item>

    <item android:state_pressed="true">
        <shape>
            <corners android:radius="100dp" />
            <solid android:color="#F5F5F5" />
            <stroke android:width="@dimen/divide_line_size" android:color="@color/text_color_gray_d8" />
        </shape>
    </item>
</selector>