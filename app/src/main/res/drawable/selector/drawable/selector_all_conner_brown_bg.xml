<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_enabled="true" android:state_pressed="false">
        <shape>
            <corners android:radius="100dp" />
            <solid android:color="@color/color_orange_FF8A00" />
        </shape>
    </item>

    <item android:state_pressed="true">
        <shape>
            <corners android:radius="100dp" />
            <solid android:color="@color/color_F2C183" />
        </shape>
    </item>

    <item android:state_enabled="false">
        <shape>
            <corners android:radius="100dp" />
            <solid android:color="#63FF8A00" />
        </shape>
    </item>
</selector>