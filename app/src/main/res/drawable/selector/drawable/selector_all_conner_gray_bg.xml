<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_enabled="true" android:state_pressed="false">
        <shape>
            <stroke android:color="@color/divide_line_color" />
            <corners android:radius="100dp" />
            <solid android:color="#ddd" />
        </shape>
    </item>

    <item android:state_pressed="true">
        <shape>
            <stroke android:color="@color/divide_line_color" />
            <corners android:radius="100dp" />
            <solid android:color="#aaa" />
        </shape>
    </item>

</selector>