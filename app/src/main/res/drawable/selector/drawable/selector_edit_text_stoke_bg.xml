<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:state_focused="false"
        android:state_enabled="false">
        <shape android:shape="rectangle">
            <corners android:radius="5dp" />
            <stroke android:width="1dp" android:color="@color/divide_line_color" />
        </shape>
    </item>

    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <corners android:radius="5dp" />
            <stroke android:width="1dp" android:color="@color/color_blue_2E7BE4" />
        </shape>
    </item>

    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <corners android:radius="5dp" />
            <stroke android:width="1dp" android:color="@color/color_red_FF3B30" />
        </shape>
    </item>


</selector>