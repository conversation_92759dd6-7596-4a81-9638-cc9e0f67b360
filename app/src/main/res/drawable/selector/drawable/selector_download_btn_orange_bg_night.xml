<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_enabled="true" android:state_pressed="false">
        <shape>
            <corners android:radius="100dp" />
            <stroke android:width="1dp" android:color="@color/color_orange_dark_CD8434" />
        </shape>
    </item>

    <item android:state_pressed="true">
        <shape>
            <corners android:radius="100dp" />
            <solid android:color="#333" />
            <stroke android:width="1dp" android:color="@color/color_orange_dark_CD8434" />
        </shape>
    </item>

    <item android:state_enabled="false">
        <shape>
            <corners android:radius="100dp" />
            <stroke android:width="1dp" android:color="#a000796B" />
        </shape>
    </item>
</selector>