<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:id="@android:id/background">
        <shape>
            <corners android:radius="5dip" />
            <!-- angle 是角度 -->
            <gradient
                android:angle="270"
                android:centerColor="#ccc"
                android:centerY="0.75"
                android:endColor="#ccc"
                android:startColor="#ccc" />
        </shape>
    </item>


    <!-- 第一进度条 -->
    <item android:id="@android:id/progress">
        <clip>
            <shape>
                <corners android:radius="5dip" />
                <solid android:color="#11243D" />
            </shape>
        </clip>
    </item>


    <!-- 第二进度条 -->
    <item android:id="@android:id/secondaryProgress">
        <clip>
            <shape>
                <corners android:radius="5dip" />
                <solid android:color="#aaa" />
            </shape>
        </clip>
    </item>


</layer-list>