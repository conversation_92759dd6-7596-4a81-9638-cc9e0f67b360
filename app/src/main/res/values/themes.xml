<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- APP的主题 -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:forceDarkAllowed" tools:targetApi="q">false</item>
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="android:windowDisablePreview">true</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="tabIndicatorColor">@color/transparent</item>
    </style>

    <!-- APP的暗色主题 -->
    <style name="AppTheme_Dark" parent="Theme.AppCompat.DayNight.NoActionBar">
        <item name="android:forceDarkAllowed" tools:targetApi="q">false</item>
        <item name="android:windowDisablePreview">true</item>
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="tabIndicatorColor">@color/transparent</item>
    </style>

    <style name="activity_transfer" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="book_contents_theme_light" parent="AppTheme.light">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="book_contents_theme_dark" parent="AppTheme_Dark.dark">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <!--*******************主题的定义********************************************************************************-->
    <attr name="theme_comm_click_bg" format="reference" />

    <attr name="theme_round_button_gray_stoke_click_bg" format="reference" />
    <attr name="theme_round_button_gray_stoke_click_bg_4e" format="reference" />
    <attr name="theme_white_bg_gray_stoke_conner_5" format="reference" />
    <attr name="theme_white_bg_gray_stoke_conner" format="reference" />

    <attr name="theme_root_view_bg" format="reference|color" />
    <attr name="theme_root_view_bg_f1f2f3" format="reference|color" />
    <attr name="theme_bg_color_white" format="reference|color" />
    <attr name="theme_bg_color_white_black" format="reference|color" />
    <attr name="theme_bg_color_white_44" format="reference|color" />
    <attr name="theme_bg_color_white_121417" format="reference|color" />
    <attr name="theme_bg_color_white_1E" format="reference|color" />
    <attr name="theme_bg_color_white_4E" format="reference|color" />
    <attr name="theme_bg_color_white_top_line" format="reference|color" />
    <attr name="theme_bg_color_white_top_line_33" format="reference|color" />
    <attr name="theme_bg_color_white_double_line" format="reference|color" />
    <attr name="theme_bg_color_white_bottom_line" format="reference|color" />
    <attr name="theme_bg_color_white_blue" format="reference|color" />
    <attr name="theme_bg_color_divide_line_color" format="reference|color" />
    <attr name="theme_bg_color_divide_line_color_login" format="reference|color" />
    <attr name="theme_bg_white_conner_8dp" format="reference|color" />
    <attr name="theme_bg_selector_white_blue_conner_8dp" format="reference|color" />
    <attr name="theme_bg_white_conner_8dp_top" format="reference|color" />
    <attr name="theme_bg_selector_white_conner_6dp" format="reference|color" />
    <attr name="theme_bg_white_conner_6dp_e3" format="reference|color" />
    <attr name="theme_shape_corner_gray" format="reference|color" />
    <attr name="theme_shape_corner_gray_12dp" format="reference|color" />

    <attr name="theme_bg_white_conner_top" format="reference|color" />
    <attr name="theme_bg_white_orange_btn" format="reference|color" />
    <attr name="theme_bg_reference_red" format="reference|color" />
    <attr name="theme_bg_color_divide_line_8E8E93" format="reference|color" />
    <attr name="theme_bg_color_divide_line_D3" format="reference|color" />
    <attr name="theme_bg_brightness_set_SeekBar" format="reference|color" />
    <attr name="theme_bg_scope_chapter_SeekBar" format="reference|color" />
    <attr name="theme_bg_color_divide_line_9B" format="reference|color" />
    <attr name="theme_bg_color_white_gray05" format="reference|color" />
    <attr name="theme_gray_04_color_E3E5E8" format="reference|color" />
    <attr name="theme_primary_color_22252A" format="reference|color" />
    <attr name="theme_ic_empty_book_shelf" format="reference|color" />
    <attr name="theme_ic_arrow_up" format="reference|color" />
    <attr name="theme_ic_arrow_down" format="reference|color" />
    <attr name="theme_ic_credit_card" format="reference|color" />
    <attr name="theme_ic_pay_result_success" format="reference|color" />
    <attr name="theme_ic_brightness" format="reference|color" />
    <attr name="theme_ic_arrange_list_black" format="reference|color" />
    <attr name="theme_ic_book_scope" format="reference|color" />
    <attr name="theme_ic_font_black" format="reference|color" />
    <attr name="theme_ic_brightness_bottom" format="reference|color" />
    <attr name="theme_ic_triangle_black_down" format="reference|color" />
    <attr name="theme_ic_change_theme_day" format="reference|color" />
    <attr name="theme_ic_book_note" format="reference|color" />
    <attr name="theme_ic_bookmark_outlined" format="reference|color" />
    <attr name="theme_ic_user_phone" format="reference|color" />

    <attr name="theme_dialog_close_ImageView" format="reference|color" />
    <attr name="theme_selector_book_scope_revoke" format="reference|color" />
    <attr name="theme_selector_font_bg" format="reference|color" />
    <attr name="theme_selector_font_text_color" format="reference|color" />
    <attr name="theme_selector_text_color_black_and_blue" format="reference|color" />
    <attr name="theme_selector_text_color_selected_gray_and_blue" format="reference|color" />
    <attr name="theme_text_color_white" format="reference|color" />
    <attr name="theme_color_all_conner_white" format="reference|color" />
    <attr name="theme_text_color_white_black_33" format="reference|color" />
    <attr name="theme_text_color_black" format="reference|color" />
    <attr name="theme_text_color_black_17191c" format="reference|color" />
    <attr name="theme_text_color_black_0e" format="reference|color" />
    <attr name="theme_text_color_black_33" format="reference|color" />
    <attr name="theme_text_color_black_4c" format="reference|color" />
    <attr name="theme_text_color_black_4c_8a" format="reference|color" />
    <attr name="theme_text_color_black_11243D" format="reference|color" />
    <attr name="theme_text_color_black_24" format="reference|color" />
    <attr name="theme_text_color_black_8D97A4" format="reference|color" />
    <attr name="theme_text_color_black_373636" format="reference|color" />
    <attr name="theme_text_color_black_4e" format="reference|color" />
    <attr name="theme_text_color_black_e3_4e" format="reference|color" />
    <attr name="theme_text_color_black_70_bc" format="reference|color" />
    <attr name="theme_text_color_package_black" format="reference|color" />
    <attr name="theme_text_color_dark" format="reference|color" />
    <attr name="theme_text_color_dark_4c" format="reference|color" />
    <attr name="theme_text_color_gray" format="reference|color" />
    <attr name="theme_text_color_note_gray" format="reference|color" />
    <attr name="theme_text_color_note_black" format="reference|color" />
    <attr name="theme_text_color_red" format="reference|color" />
    <attr name="theme_text_color_gray_8A8A" format="reference|color" />
    <attr name="theme_text_color_gray_BC_88" format="reference|color" />
    <attr name="theme_text_color_blue_book_notes" format="reference|color" />
    <attr name="theme_text_color_blue" format="reference|color" />
    <attr name="theme_text_color_blue_006FFF" format="reference|color" />
    <attr name="theme_text_color_orange" format="reference|color" />
    <attr name="theme_text_color_home_navigation" format="color|reference" />
    <attr name="theme_bg_color_brown_book_detail" format="reference|color" />
    <attr name="theme_divide_line_horizontal" format="color|reference" />
    <attr name="theme_divide_line_color_17191C" format="reference|color" />
    <attr name="theme_divide_line_vertical" format="color|reference" />
    <attr name="theme_not_exist_icon" format="reference" />

    <attr name="theme_ic_note_read" format="reference" />
    <attr name="theme_ic_note_edit" format="reference" />
    <attr name="theme_ic_note_delete" format="reference" />
    <attr name="theme_ic_history_delete" format="reference" />

    <attr name="theme_windowLightStatusBar" format="boolean" />

    <attr name="theme_shape_black_conner_5dp_bg" format="color|reference" />
    <attr name="theme_color_black_444" format="color" />

    <attr name="theme_shape_all_conner_gray" format="reference" />
    <attr name="theme_shape_all_conner_black" format="reference" />
    <attr name="theme_shape_all_conner_color_f2" format="reference" />

    <attr name="theme_tab_icon_tint_color" format="reference" />

    <attr name="theme_icon_tint_color" format="color" />
    <attr name="theme_icon_tint_color_474A52" format="color" />
    <attr name="theme_icon_tint_color_white" format="color" />
    <attr name="theme_icon_tint_color_black" format="color" />
    <attr name="theme_category_item_selector_bg" format="reference" />
    <attr name="theme_category_item_text_color_selector" format="reference" />
    <attr name="theme_shape_gray_conner_8_bg" format="reference" />
    <attr name="theme_search_history_recommend_item_bg" format="reference" />
    <attr name="theme_feedback_type_color" format="reference" />
    <attr name="theme_circle_bg_red" format="reference" />

    <attr name="theme_download_btn_orange_bg" format="reference" />

    <attr name="theme_checkBox" format="reference" />
    <attr name="theme_shape_corner_big" format="reference|color" />
    <attr name="theme_cover_border_bg" format="reference" />

    <!--*************白天模式**************************************************************************************-->
    <style name="AppTheme.light" parent="AppTheme">
        <item name="theme_ic_note_read">@drawable/ic_note_read</item>
        <item name="theme_ic_note_edit">@drawable/ic_note_edit</item>
        <item name="theme_ic_note_delete">@drawable/ic_note_delete</item>
        <item name="theme_ic_history_delete">@drawable/ic_delete_history</item>
        <item name="theme_windowLightStatusBar">true</item>
        <item name="theme_circle_bg_red">@drawable/shaper_circle_bg_red</item>
        <item name="theme_download_btn_orange_bg">@drawable/selector_download_btn_orange_bg</item>
        <item name="theme_checkBox">@drawable/selector_checkbox_ic</item>
        <item name="theme_shape_corner_big">@drawable/selector_all_conner_white_bg</item>

        <item name="theme_search_history_recommend_item_bg">@drawable/selector_all_conner_white_bg</item>
        <item name="theme_shape_gray_conner_8_bg">@drawable/selector_sort_gray_bg_conner_8</item>
        <item name="theme_category_item_text_color_selector">@drawable/selector_category_item_text_color</item>
        <item name="theme_category_item_selector_bg">@drawable/selector_category_item_bg</item>
        <item name="theme_shape_all_conner_gray">@drawable/shape_all_conner_gray</item>
        <item name="theme_shape_all_conner_black">@drawable/shape_all_conner_black</item>
        <item name="theme_shape_all_conner_color_f2">@drawable/shape_all_conner_color_f2</item>
        <item name="theme_icon_tint_color">@color/color_icon_normal_373636</item>
        <item name="theme_icon_tint_color_474A52">@color/color_gray_474A52</item>
        <item name="theme_icon_tint_color_white">@color/white</item>
        <item name="theme_icon_tint_color_black">@color/black</item>
        <item name="theme_tab_icon_tint_color">@drawable/selector_home_navigation_color</item>
        <item name="theme_not_exist_icon">@drawable/ic_not_exist</item>
        <item name="theme_divide_line_horizontal">@drawable/shape_divide_line_horizontal</item>
        <item name="theme_divide_line_color_17191C">@color/gray_05_light_color</item>
        <item name="theme_divide_line_vertical">@drawable/shape_divide_line_vertical</item>
        <item name="theme_comm_click_bg">@drawable/selector_comm_click_bg</item>
        <item name="theme_bg_color_white">@color/white</item>
        <item name="theme_bg_color_white_black">@color/white</item>
        <item name="theme_bg_color_white_44">@color/white</item>
        <item name="theme_bg_color_white_121417">@color/white</item>
        <item name="theme_bg_color_white_1E">@color/white</item>
        <item name="theme_bg_color_white_4E">@color/white</item>
        <item name="theme_bg_color_white_top_line">@drawable/layer_list_white_bg_top_line</item>
        <item name="theme_bg_color_white_top_line_33">@drawable/layer_list_white_bg_top_line</item>
        <item name="theme_bg_color_white_double_line">@drawable/layer_list_white_bg_double_line
        </item>
        <item name="theme_bg_color_white_bottom_line">@drawable/layer_list_white_bg_bottom_line
        </item>
        <item name="theme_bg_color_white_blue">@color/color_blue_0644A0</item>
        <item name="theme_bg_color_divide_line_color">@color/divide_line_color</item>
        <item name="theme_bg_color_divide_line_color_login">@drawable/selector_divide_line_login</item>
        <item name="theme_bg_color_divide_line_8E8E93">@color/divide_line_color</item>
        <item name="theme_bg_color_divide_line_D3">@color/color_dark_DEDEE3</item>
        <item name="theme_bg_brightness_set_SeekBar">@drawable/layer_list_seekbar_black_style</item>
        <item name="theme_bg_scope_chapter_SeekBar">@drawable/layer_list_seekbar_black_style</item>
        <item name="theme_bg_color_divide_line_9B">@color/text_color_gray_9B9B9B</item>
        <item name="theme_bg_color_white_gray05">@color/white</item>
        <item name="theme_gray_04_color_E3E5E8">@color/gray_04_light_color</item>
        <item name="theme_primary_color_22252A">@color/primary_light_color</item>
        <item name="theme_bg_white_conner_8dp">@drawable/shape_white_conner_6dp_bg</item>
        <item name="theme_bg_selector_white_blue_conner_8dp">@drawable/selector_wihite_blue_conner_bg</item>
        <item name="theme_bg_white_conner_8dp_top">@drawable/shape_white_conner_8dp_top_bg</item>
        <item name="theme_bg_selector_white_conner_6dp">@drawable/selector_white_conner_8dp_bg
        </item>
        <item name="theme_bg_white_conner_6dp_e3">@drawable/shape_gray_conner_bg</item>
        <item name="theme_shape_corner_gray">@drawable/selector_gray_color_corner_login</item>
        <item name="theme_shape_corner_gray_12dp">@drawable/selector_gray_color_corner_two</item>

        <item name="theme_selector_book_scope_revoke">@drawable/selector_book_scope_revoke</item>
        <item name="theme_selector_text_color_black_and_blue">
            @drawable/selector_text_color_black_and_blue
        </item>
        <item name="theme_selector_text_color_selected_gray_and_blue">@drawable/selector_text_color_selected_gray_and_blue</item>
        <item name="theme_selector_font_bg">@drawable/selector_font_btn_gray_orange_bg</item>
        <item name="theme_selector_font_text_color">@drawable/selector_font_btn_text_color</item>
        <item name="theme_bg_white_conner_top">@drawable/shape_white_conner_top_bg</item>
        <item name="theme_bg_white_orange_btn">@drawable/selector_all_conner_brown_bg</item>
        <item name="theme_ic_empty_book_shelf">@drawable/ic_empty_book_shelf</item>
        <!--        <item name="theme_ic_mine_order">@drawable/ic_mine_order</item>-->
        <!--        <item name="theme_ic_mine_book">@drawable/ic_mine_book</item>-->
        <!--        <item name="theme_ic_mine_setting">@drawable/ic_mine_setting</item>-->
        <!--        <item name="theme_ic_mine_feedback">@drawable/ic_mine_feedback</item>-->
        <item name="theme_ic_arrow_up">@drawable/ic_arrow_up_gray</item>
        <item name="theme_ic_arrow_down">@drawable/ic_arrow_down_gray</item>
        <item name="theme_ic_credit_card">@drawable/ic_payment_card</item>
        <item name="theme_ic_pay_result_success">@drawable/ic_pay_result_success</item>
        <item name="theme_ic_brightness">@drawable/ic_brightness</item>
        <item name="theme_ic_arrange_list_black">@drawable/ic_arrange_list_black</item>
        <item name="theme_ic_book_scope">@drawable/ic_book_scope</item>
        <item name="theme_ic_font_black">@drawable/ic_font_black</item>
        <item name="theme_dialog_close_ImageView">@drawable/ic_exit_black</item>
        <item name="theme_ic_brightness_bottom">@drawable/ic_brightness_bottom</item>
        <item name="theme_ic_triangle_black_down">@drawable/ic_triangle_black_down</item>
        <item name="theme_ic_change_theme_day">@drawable/ic_change_theme_day</item>
        <item name="theme_ic_book_note">@drawable/ic_book_note</item>
        <item name="theme_ic_bookmark_outlined">@drawable/selector_book_mark_ic</item>
        <item name="theme_ic_user_phone">@drawable/selector_ic_phone_email</item>
        <item name="theme_root_view_bg">#f8f8f8</item>
        <item name="theme_round_button_gray_stoke_click_bg">
            @drawable/selector_round_conner_dark_stoke_button_bg
        </item>
        <item name="theme_root_view_bg_f1f2f3">@color/color_gray_f1f2f3</item>
        <item name="theme_round_button_gray_stoke_click_bg_4e">
            @drawable/selector_round_conner_dark_stoke_button_bg
        </item>
        <item name="theme_white_bg_gray_stoke_conner_5">
            @drawable/shape_white_bg_gray_stoke_conner_5
        </item>
        <item name="theme_white_bg_gray_stoke_conner">
            @drawable/shape_white_bg_gray_stoke_conner
        </item>
        <item name="theme_text_color_home_navigation">
            @drawable/selector_home_navigation_text_color
        </item>
        <item name="theme_bg_reference_red">@color/color_red_E33733</item>
        <item name="theme_color_black_444">@color/black</item>
        <item name="theme_text_color_black">@color/text_color_black_000</item>
        <item name="theme_text_color_black_17191c">@color/default_bg_dark_color</item>
        <item name="theme_text_color_black_0e">@color/text_color_black_0e</item>
        <item name="theme_text_color_dark">@color/text_color_black_33</item>
        <item name="theme_text_color_black_33">@color/text_color_black_33</item>
        <item name="theme_text_color_red">@color/color_red_E53935</item>
        <item name="theme_text_color_black_4c">@color/text_color_black_4c</item>
        <item name="theme_text_color_black_4c_8a">@color/text_color_black_4c</item>
        <item name="theme_text_color_black_11243D">@color/text_color_dark_11243D</item>
        <item name="theme_text_color_black_24">@color/text_color_black_242424</item>
        <item name="theme_text_color_black_8D97A4">@color/text_color_gray_8D97A4</item>
        <item name="theme_text_color_black_373636">@color/color_icon_normal_373636</item>
        <item name="theme_text_color_black_4e">@color/divide_line_color_dark</item>
        <item name="theme_text_color_black_e3_4e">@color/color_dark_E3E3E3</item>
        <item name="theme_text_color_black_70_bc">@color/color_gray_707070</item>
        <item name="theme_text_color_package_black">@color/text_color_dark_11243D</item>
        <item name="theme_text_color_dark_4c">@color/text_color_black_4c</item>
        <item name="theme_text_color_gray">@color/text_color_gray_707A89</item>
        <item name="theme_text_color_note_gray">@color/text_color_gray_707A89</item>
        <item name="theme_text_color_note_black">@color/text_color_black_000</item>
        <item name="theme_text_color_gray_8A8A">@color/text_color_gray_8A8A8A</item>
        <item name="theme_text_color_gray_BC_88">@color/text_color_black_BC</item>
        <item name="theme_text_color_blue_book_notes">@color/color_blue_0644A0</item>
        <item name="theme_text_color_blue">@color/color_blue_2E7BE4</item>
        <item name="theme_text_color_blue_006FFF">@color/color_blue_006FFF</item>
        <item name="theme_text_color_white">@color/white</item>
        <item name="theme_color_all_conner_white">@drawable/selector_all_conner_white_bg</item>
        <item name="theme_text_color_white_black_33">@color/white</item>
        <item name="theme_text_color_orange">@color/color_E9973E</item>
        <item name="theme_bg_color_brown_book_detail">@color/color_FBEFEC</item>
        <item name="theme_shape_black_conner_5dp_bg">@drawable/shape_black_conner_5dp_bg</item>
        <item name="theme_feedback_type_color">@drawable/selector_feedback_type_color</item>
        <item name="theme_cover_border_bg">@drawable/shape_cover_border_light</item>
    </style>


    <!--******************黑夜模式*********************************************************************************-->
    <style name="AppTheme_Dark.dark" parent="AppTheme_Dark">
        <item name="theme_feedback_type_color">@drawable/selector_feedback_type_color_night</item>
        <item name="theme_ic_note_read">@drawable/ic_note_read_night</item>
        <item name="theme_ic_note_edit">@drawable/ic_note_edit_night</item>
        <item name="theme_ic_note_delete">@drawable/ic_note_delete_night</item>
        <item name="theme_ic_history_delete">@drawable/ic_delete_history_dark</item>
        <item name="theme_icon_tint_color">@color/color_icon_night_e3e3e3</item>
        <item name="theme_icon_tint_color_474A52">@color/color_gray_f1f2f3</item>
        <item name="theme_icon_tint_color_white">@color/color_icon_night_e3e3e3</item>
        <item name="theme_icon_tint_color_black">@color/color_icon_night_e3e3e3</item>
        <item name="theme_tab_icon_tint_color">@drawable/selector_home_navigation_color_night</item>
        <item name="theme_search_history_recommend_item_bg">@drawable/shape_all_conner_color_f2_night</item>
        <item name="theme_shape_gray_conner_8_bg">@drawable/selector_sort_gray_bg_conner_8_night</item>
        <item name="theme_category_item_text_color_selector">@drawable/selector_category_item_text_color_night</item>
        <item name="theme_category_item_selector_bg">@drawable/selector_category_item_bg_night</item>
        <item name="theme_circle_bg_red">@drawable/shaper_circle_bg_red_night</item>
        <item name="theme_download_btn_orange_bg">@drawable/selector_download_btn_orange_bg_night</item>
        <item name="theme_checkBox">@drawable/selector_checkbox_ic_dark</item>

        <item name="theme_shape_all_conner_color_f2">@drawable/shape_all_conner_color_f2_night</item>
        <item name="theme_shape_all_conner_gray">@drawable/shape_all_conner_gray_night</item>
        <item name="theme_shape_all_conner_black">@drawable/shape_all_conner_black_night</item>
        <item name="theme_shape_black_conner_5dp_bg">@drawable/shape_black_conner_5dp_bg_night</item>
        <item name="theme_windowLightStatusBar">false</item>
        <item name="theme_text_color_orange">@color/color_orange_dark_CD8434</item>
        <item name="theme_bg_color_brown_book_detail">@color/color_FEE6E0</item>
        <item name="theme_not_exist_icon">@drawable/ic_not_exist_dark</item>
        <item name="theme_divide_line_horizontal">@drawable/shape_divide_line_horizontal_dark</item>
        <item name="theme_divide_line_color_17191C">@color/default_bg_dark_color</item>
        <item name="theme_divide_line_vertical">@drawable/shape_divide_line_vertical_dark</item>
        <item name="theme_color_black_444">@color/color_dark_444444</item>
        <item name="theme_text_color_white">@color/color_dark_eee</item>
        <item name="theme_color_all_conner_white">@drawable/selector_all_conner_white_bg_night</item>
        <item name="theme_shape_corner_big">@drawable/selector_all_conner_white_bg_night_4e</item>

        <item name="theme_text_color_white_black_33">@color/text_color_black_33</item>
        <item name="theme_text_color_gray">#bcbcbc</item>
        <item name="theme_text_color_note_gray">@color/color_gray_dark_A0FFFFFF</item>
        <item name="theme_text_color_note_black">@color/color_dark_DEDEE3</item>
        <item name="theme_text_color_gray_8A8A">@color/color_dark_DEDEE3</item>
        <item name="theme_text_color_gray_BC_88">@color/text_color_gray_88</item>
        <item name="theme_text_color_blue_book_notes">@color/text_color_blue_4D90F2</item>
        <item name="theme_text_color_blue">@color/color_blue_pressed_0E53B0</item>
        <item name="theme_text_color_blue_006FFF">@color/color_blue_4D95F7</item>
        <item name="theme_text_color_dark">@color/color_dark_eee</item>
        <item name="theme_text_color_red">@color/color_red_A5343C</item>
        <item name="theme_text_color_dark_4c">@color/color_dark_eee</item>
        <item name="theme_text_color_black">#F5F5F5</item>
        <item name="theme_text_color_black_17191c">#F5F5F5</item>
        <item name="theme_text_color_black_0e">@color/white</item>
        <item name="theme_text_color_black_33">@color/white</item>
        <item name="theme_text_color_black_11243D">@color/color_dark_F0F0F2</item>
        <item name="theme_text_color_black_4c">@color/color_dark_F0F0F2</item>
        <item name="theme_text_color_black_24">@color/color_dark_F0F0F2</item>
        <item name="theme_text_color_black_4c_8a">@color/text_color_gray_8A8A8A</item>
        <item name="theme_text_color_black_8D97A4">@color/color_dark_F0F0F2</item>
        <item name="theme_text_color_black_373636">@color/color_F5F5F5</item>
        <item name="theme_text_color_black_4e">@color/text_color_black_BC</item>
        <item name="theme_text_color_black_e3_4e">@color/color_4E4E4E</item>
        <item name="theme_text_color_black_70_bc">@color/text_color_black_BC</item>
        <item name="theme_text_color_package_black">@color/color_dark_F0F0F2</item>
        <item name="theme_text_color_home_navigation">
            @drawable/selector_home_navigation_text_color_dark
        </item>
        <item name="theme_bg_reference_red">@color/color_red_A5343C</item>
        <item name="theme_ic_arrow_up">@drawable/ic_arrow_up_white</item>
        <item name="theme_ic_arrow_down">@drawable/ic_down_arrow_white</item>
        <item name="theme_ic_credit_card">@drawable/ic_payment_card_dark</item>
        <item name="theme_ic_pay_result_success">@drawable/ic_pay_result_success_dark</item>
        <item name="theme_ic_brightness">@drawable/ic_brightness_dark</item>
        <item name="theme_ic_arrange_list_black">@drawable/ic_arrange_list_black_dark</item>
        <item name="theme_ic_book_scope">@drawable/ic_book_scope_dark</item>
        <item name="theme_ic_font_black">@drawable/ic_font_black_dark</item>
        <item name="theme_ic_brightness_bottom">@drawable/ic_brightness_bottom_dark</item>
        <item name="theme_ic_triangle_black_down">@drawable/ic_triangle_black_down_dark</item>
        <item name="theme_ic_change_theme_day">@drawable/ic_change_theme_night</item>
        <item name="theme_ic_book_note">@drawable/ic_book_note_dark</item>
        <item name="theme_ic_bookmark_outlined">@drawable/selector_book_mark_ic_dark</item>
        <item name="theme_ic_user_phone">@drawable/selector_ic_phone_email_night</item>

        <item name="theme_dialog_close_ImageView">@drawable/ic_exit_black_dark</item>
        <item name="theme_selector_book_scope_revoke">@drawable/selector_book_scope_revoke_dark
        </item>
        <item name="theme_selector_text_color_black_and_blue">
            @drawable/selector_text_color_black_and_blue_dark
        </item>
        <item name="theme_selector_text_color_selected_gray_and_blue">
            @drawable/selector_text_color_selected_gray_and_blue_dark
        </item>
        <item name="theme_round_button_gray_stoke_click_bg">
            @drawable/selector_round_conner_dark_stoke_button_bg_dark
        </item>
        <item name="theme_round_button_gray_stoke_click_bg_4e">
            @drawable/selector_round_conner_dark_stoke_button_bg_dark_4e
        </item>
        <item name="theme_white_bg_gray_stoke_conner_5">
            @drawable/shape_white_bg_gray_stoke_conner_5_dark
        </item>
         <item name="theme_white_bg_gray_stoke_conner">
            @drawable/shape_white_bg_gray_stoke_conner_dark
        </item>
        <item name="theme_comm_click_bg">@drawable/selector_comm_click_bg_dark</item>
        <item name="theme_root_view_bg">@color/text_color_black_000</item>
        <item name="theme_root_view_bg_f1f2f3">@color/text_color_black_000</item>
        <item name="theme_bg_color_white_bottom_line">
            @drawable/layer_list_white_bg_bottom_line_dark
        </item>
        <item name="theme_bg_color_white_blue">@color/color_dark_F0F0F2</item>
        <item name="theme_bg_color_divide_line_color">@color/divide_line_color_dark</item>
        <item name="theme_bg_color_divide_line_color_login">@drawable/selector_divide_line_login_night</item>
        <item name="theme_bg_color_divide_line_8E8E93">@color/divide_line_color_8E8E93</item>
        <item name="theme_bg_color_divide_line_D3">@color/text_color_black_33</item>
        <item name="theme_bg_brightness_set_SeekBar">@color/color_dark_444444</item>
        <item name="theme_bg_scope_chapter_SeekBar">@drawable/layer_list_seekbar_black_style_dark
        </item>
        <item name="theme_bg_color_divide_line_9B">@color/text_color_black_33</item>
        <item name="theme_bg_color_white_gray05">@color/gray_05_dark_color</item>
        <item name="theme_gray_04_color_E3E5E8">@color/gray_04_dark_color</item>
        <item name="theme_primary_color_22252A">@color/primary_dark_color</item>
        <item name="theme_bg_white_conner_8dp">@drawable/shape_white_conner_6dp_bg_dark</item>
        <item name="theme_bg_selector_white_blue_conner_8dp">@drawable/selector_wihite_blue_conner_bg_dark</item>
        <item name="theme_bg_white_conner_8dp_top">@drawable/shape_white_conner_6dp_bg_dark</item>
        <item name="theme_bg_selector_white_conner_6dp">
            @drawable/selector_white_conner_8dp_bg_dark
        </item>
        <item name="theme_bg_white_conner_6dp_e3">@drawable/shape_gray_conner_dark_bg</item>
        <item name="theme_shape_corner_gray">@drawable/selector_gray_color_corner_login_dark</item>
        <item name="theme_shape_corner_gray_12dp">@drawable/selector_gray_color_corner_two_dark</item>

        <item name="theme_bg_white_conner_top">@drawable/shape_white_conner_top_bg_dark</item>
        <item name="theme_bg_white_orange_btn">@drawable/selector_all_conner_brown_bg</item>
        <item name="theme_ic_empty_book_shelf">@drawable/ic_empty_book_shelf_dark</item>
        <item name="theme_bg_color_white_top_line">@drawable/layer_list_white_bg_top_line_dark
        </item>
        <item name="theme_bg_color_white_top_line_33">@drawable/layer_list_black_bg_top_line_dark
        </item>
        <item name="theme_bg_color_white_double_line">
            @drawable/layer_list_white_bg_double_line_dark
        </item>
        <item name="theme_bg_color_white">@color/color_dark_1E1E1E</item>
        <item name="theme_bg_color_white_black">@color/black</item>
        <item name="theme_bg_color_white_1E">@color/color_dark_1E1E1E</item>
        <item name="theme_bg_color_white_4E">@color/color_4E4E4E</item>
        <item name="theme_bg_color_white_44">@color/color_dark_444444</item>
        <item name="theme_bg_color_white_121417">@color/color_dark_121417</item>
        <!--        <item name="theme_ic_mine_order">@drawable/ic_mine_order_dark</item>-->
        <!--        <item name="theme_ic_mine_book">@drawable/ic_mine_book_dark</item>-->
        <!--        <item name="theme_ic_mine_setting">@drawable/ic_mine_setting_dark</item>-->
        <!--        <item name="theme_ic_mine_feedback">@drawable/ic_mine_feedback_dark</item>-->
        <item name="theme_selector_font_bg">@drawable/selector_font_btn_gray_orange_dark_bg</item>
        <item name="theme_selector_font_text_color">@drawable/selector_font_btn_text_dark_color</item>
        <item name="theme_cover_border_bg">@drawable/shape_cover_border_dark</item>
    </style>
</resources>