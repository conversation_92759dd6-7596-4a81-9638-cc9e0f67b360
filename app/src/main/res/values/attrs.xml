<?xml version="1.0" encoding="utf-8"?>
<resources>
    <attr name="attr_title_text" format="string" />

    <declare-styleable name="CouponDashLineView">
        <attr name="attr_dash_color" format="color" />
        <attr name="attr_dash_width" format="dimension" />
        <attr name="attr_dash_size" format="dimension" />
        <attr name="attr_dash_space" format="dimension" />
        <attr name="attr_conner_radius" format="dimension" />
        <attr name="attr_conner_color" format="dimension" />
    </declare-styleable>


    <declare-styleable name="ExpandTextView">
        <attr name="maxCollapsedLines" format="integer" />
        <attr name="animDuration" format="integer" />
        <attr name="animAlphaStart" format="float" />
        <attr name="expandDrawable" format="reference" />
        <attr name="collapseDrawable" format="reference" />
        <attr name="collapsed" format="boolean" />
        <attr name="arrowAlign" format="enum">
            <enum name="bottom" value="0" />
            <enum name="top" value="1" />
            <enum name="center" value="2" />
            <enum name="right" value="0" />
            <enum name="left" value="1" />
        </attr>
        <attr name="arrowPosition" format="enum">
            <enum name="right" value="0" />
            <enum name="below" value="1" />
        </attr>
        <attr name="arrowPadding" format="dimension" />
    </declare-styleable>

    <declare-styleable name="CircleBGImageView">
        <attr name="attr_circle_bg_color" format="color" />
        <attr name="attr_stoke_color" format="color" />
        <attr name="attr_stoke_size" format="dimension" />
        <attr name="attr_selected" format="boolean" />
    </declare-styleable>


    <declare-styleable name="ProgressButton">
        <attr name="attr_button_progress" format="float" />
        <attr name="attr_progress_color" format="color" />
        <attr name="attr_bg_color" format="color" />
        <attr name="attr_need_show_Progress" format="boolean" />
    </declare-styleable>


    <declare-styleable name="SlideSwitch">
        <attr name="attr_theme_color" format="color" />
        <attr name="attr_is_check" format="boolean" />
        <attr name="attr_shape_type">
            <enum name="RECT" value="1" />
            <enum name="CIRCLE" value="2" />
        </attr>
    </declare-styleable>


    <!--顶部标题栏的属性-->
    <declare-styleable name="CommTopTitleLayout">
        <attr name="attr_hide_back" format="boolean" />

        <attr name="attr_back_src" format="reference" />

        <attr name="attr_is_text_bold" format="boolean" />

        <attr name="attr_title_text" />
        <attr name="attr_title_text_color" format="color" />
        <attr name="attr_title_text_size" format="dimension" />
        <attr name="attr_max_title_width" format="dimension" />

    </declare-styleable>

    <!--我的页面的item布局-->
    <declare-styleable name="WidgetMineItemLayout">
        <attr name="attr_image_src" format="reference" />
        <attr name="attr_title_text" />
        <attr name="attr_data_text" format="string" />
        <attr name="attr_data_text_color" format="color" />
        <attr name="attr_show_image" format="boolean" />
        <attr name="attr_image_tint" format="reference" />
        <attr name="attr_arrow_image_tint" format="reference" />

    </declare-styleable>

    <declare-styleable name="IndicatorView">
        <attr name="attr_cell_radius" format="dimension" />
        <attr name="attr_cell_margin" format="dimension" />
        <attr name="attr_cell_count" format="integer" />
        <attr name="attr_current_position" format="integer" />
        <attr name="attr_selected_color" format="color|reference" />
        <attr name="attr_normal_color" format="color|reference" />
        <attr name="attr_indicator_style" format="enum">
            <enum name="CIRCLE" value="0" />
            <enum name="RECT" value="1" />
            <enum name="LINE" value="2" />
        </attr>
    </declare-styleable>

    <!--BannerLayout-->
    <declare-styleable name="WidgetBannerLayout">
        <attr name="attr_interval_time" format="integer" /> <!--轮播时间间隔-->
        <attr name="attr_show_indicator" format="boolean" />  <!--是否显示指示器-->
        <attr name="attr_orientation" format="enum">  <!--轮播图方向-->
            <enum name="HORIZONTAL" value="0" />
            <enum name="VERTICAL" value="1" />
        </attr>

        <attr name="attr_auto_playing" format="boolean" />  <!--是否开启自动轮播-->
        <attr name="attr_item_space" format="dimension" /> <!--图片间距-->
        <attr name="attr_center_scale" format="float" /> <!--当前图片缩放比列-->
        <attr name="attr_move_speed" format="float" /> <!--滚动速度，越大越快-->
    </declare-styleable>

    <declare-styleable name="BannerLayout">
        <attr name="interval" format="integer" /> <!--轮播时间间隔-->
        <attr name="showIndicator" format="boolean" />  <!--是否显示指示器-->
        <attr name="orientation" format="boolean" />  <!--轮播图方向-->
        <attr name="autoPlaying" format="boolean" />  <!--是否开启自动轮播-->
        <attr name="itemSpace" format="integer" /> <!--图片间距-->
        <attr name="centerScale" format="float" /> <!--当前图片缩放比列-->
        <attr name="moveSpeed" format="float" /> <!--滚动速度，越大越快-->
    </declare-styleable>

    <!--************************************************************************************-->
    <declare-styleable name="CropImageView">
        <attr name="cropGuidelines">
            <enum name="off" value="0" />
            <enum name="onTouch" value="1" />
            <enum name="on" value="2" />
        </attr>
        <attr name="cropScaleType">
            <enum name="fitCenter" value="0" />
            <enum name="center" value="1" />
            <enum name="centerCrop" value="2" />
            <enum name="centerInside" value="3" />
        </attr>
        <attr name="cropShape">
            <enum name="rectangle" value="0" />
            <enum name="oval" value="1" />
        </attr>
        <attr name="cropAutoZoomEnabled" format="boolean" />
        <attr name="cropMaxZoom" format="integer" />
        <attr name="cropMultiTouchEnabled" format="boolean" />
        <attr name="cropFixAspectRatio" format="boolean" />
        <attr name="cropAspectRatioX" format="integer" />
        <attr name="cropAspectRatioY" format="integer" />
        <attr name="cropInitialCropWindowPaddingRatio" format="float" />
        <attr name="cropBorderLineThickness" format="dimension" />
        <attr name="cropBorderLineColor" format="color" />
        <attr name="cropBorderCornerThickness" format="dimension" />
        <attr name="cropBorderCornerOffset" format="dimension" />
        <attr name="cropBorderCornerLength" format="dimension" />
        <attr name="cropBorderCornerColor" format="color" />
        <attr name="cropGuidelinesThickness" format="dimension" />
        <attr name="cropGuidelinesColor" format="color" />
        <attr name="cropBackgroundColor" format="color" />
        <attr name="cropSnapRadius" format="dimension" />
        <attr name="cropTouchRadius" format="dimension" />
        <attr name="cropShowCropOverlay" format="boolean" />
        <attr name="cropShowProgressBar" format="boolean" />
        <attr name="cropMinCropWindowWidth" format="dimension" />
        <attr name="cropMinCropWindowHeight" format="dimension" />
        <attr name="cropMinCropResultWidthPX" format="float" />
        <attr name="cropMinCropResultHeightPX" format="float" />
        <attr name="cropMaxCropResultWidthPX" format="float" />
        <attr name="cropMaxCropResultHeightPX" format="float" />
    </declare-styleable>

    <!--自定义的展开收起textview扩展属性-->
    <declare-styleable name="ExpandTextViewTwo">
        <!--每行文本之间底部的间距-->
        <attr name="bottom_margin" format="dimension" />
        <!--主内容的文字大小-->
        <attr name="content_size" format="dimension" />
        <!--主内容的文本颜色-->
        <attr name="content_color" format="color" />
        <!--主内容是否是粗体-->
        <attr name="content_bold" format="boolean" />
        <!--收起状态下距离右边的距离,就是展开 按钮距离右边最少需要留多少空间-->
        <attr name="special_right_margin" format="dimension" />
        <!--特殊按钮的展示距离左边的距离，就是 展开按钮距离左边 最少需要留多少空间-->
        <attr name="special_left_margin" format="dimension" />
        <!--特殊内容的文字大小-->
        <attr name="special_size" format="dimension" />
        <!--特殊内容的文本颜色-->
        <attr name="special_color" format="color" />
        <!--特殊内容是否是粗体-->
        <attr name="special_bold" format="boolean" />
        <!--特殊内容横向热区扩大值-->
        <attr name="special_horizon_click_more" format="dimension" />
        <!--特殊内容纵向热区扩大值-->
        <attr name="special_vertical_click_more" format="dimension" />
        <!--当前的文本内容数据，会剔除一些特殊字符-->
        <attr name="current_text" format="string" />
        <!--最大行数-->
        <attr name="max_line" format="integer" />
        <!--收起  当前状态是展开状态下 特殊按钮的文本内容-->
        <attr name="shrink_text" format="string" />
        <!--展开  当前状态是收起装态下 特殊按钮的文本内容-->
        <attr name="expand_text" format="string" />
        <!--展开  当前状态是收起装态下 前缀内容，会算在主内容里，一般可以用... 省略号表示-->
        <attr name="expand_prifix_text" format="string" />

        <!--原先是必须绘制文本，下面的属性代表支持自定义特殊展示区域的大小，这样在那个位置可以放一些自己自定义的控件内容-->
        <!--如果设置了的话就要全部进行设置，则会优先依据这个走-->
        <!--        这个代表展开状态下，收起内容区域的宽高说明-->
        <attr name="special_shrink_width" format="dimension" />
        <attr name="special_shrink_height" format="dimension" />
        <!--        这个代表收缩状态下，展开内容区域的宽高说明-->
        <attr name="special_expand_width" format="dimension" />
        <attr name="special_expand_height" format="dimension" />
    </declare-styleable>


    <!--    &lt;!&ndash;书籍排行榜的信息属性&ndash;&gt;-->
    <!--    <declare-styleable name="WidgetBookRangeInfoLayout">-->
    <!--        <attr name="attr_cover_image_src" format="reference" />-->
    <!--        <attr name="attr_index_text" format="string" />-->
    <!--        <attr name="attr_book_name_text" format="string" />-->
    <!--        <attr name="attr_author_text" format="string" />-->
    <!--    </declare-styleable>-->
</resources>