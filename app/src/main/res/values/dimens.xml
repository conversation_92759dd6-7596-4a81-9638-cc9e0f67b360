<?xml version="1.0" encoding="utf-8"?>
<resources>

    <dimen name="divide_line_size">0.8dp</dimen>

    <dimen name="top_title_height">50dp</dimen>

    <dimen name="micro_item_layout_padding">24dp</dimen>

    <dimen name="highlight_circle_size">25dp</dimen>
    <dimen name="book_text_title_height">45dp</dimen>

    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="title_height">50dp</dimen>

    <dimen name="dimen_size_5">5dp</dimen>
    <dimen name="dimen_size_10">10dp</dimen>
    <dimen name="dimen_size_15">15dp</dimen>
    <dimen name="dimen_size_20">20dp</dimen>
    <dimen name="dimen_size_25">25dp</dimen>
    <dimen name="dimen_size_30">30dp</dimen>
    <!--<dimen name="dimen_size_40">40dp</dimen>-->
    <dimen name="dimen_size_50">50dp</dimen>
    <dimen name="home_bottom_tab_menu_height">60dp</dimen>

    <dimen name="content_horizontal_margin">25dp</dimen>

    <!--<dimen name="main_bottom_tools_text_size">12sp</dimen>-->
    <dimen name="image_button_width">50dp</dimen>

    <dimen name="text_size_20">20sp</dimen>
    <dimen name="text_size_18">18sp</dimen>
    <dimen name="text_size_16">16sp</dimen>
    <dimen name="text_size_15">15sp</dimen>
    <dimen name="text_size_14">14sp</dimen>
    <dimen name="text_size_12">12sp</dimen>
    <dimen name="text_size_10">10sp</dimen>
</resources>