<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="style_book_detail_label">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_color_black_000</item>
        <item name="android:minHeight">25dp</item>
        <item name="android:gravity">center_vertical|right</item>
        <item name="android:paddingRight">10dp</item>
        <item name="android:paddingTop">5dp</item>
        <item name="android:paddingBottom">5dp</item>
    </style>

    <style name="style_book_detail_value">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_color_black_000</item>
        <item name="android:minHeight">25dp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingLeft">10dp</item>
        <item name="android:paddingTop">5dp</item>
        <item name="android:paddingBottom">5dp</item>
    </style>

    <style name="style_user_info_label">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_color_black_000</item>
        <item name="android:minHeight">50dp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingLeft">5dp</item>
        <item name="android:paddingRight">5dp</item>
        <item name="attr_left_text_color">@color/text_color_black_000</item>
        <item name="attr_right_text_color">@color/text_color_gray_707A89</item>
        <item name="attr_right_text_size">16sp</item>
        <item name="attr_left_text_size">16sp</item>
        <item name="attr_right_single_line">true</item>
        <item name="attr_left_single_line">true</item>
        <item name="attr_right_max_width">300dp</item>
    </style>

    <style name="style_home_bottom_tab">
        <item name="attr_parent_orientation">IMAGE_TOP_TEXT_BOTTOM</item>
        <item name="attr_image_width">30dp</item>
        <item name="attr_image_height">30dp</item>
        <item name="attr_scale_type">FIT_CENTER</item>
        <item name="attr_text_size">12sp</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>
        <item name="attr_text_margin_image_size">5dp</item>
        <item name="android:foreground">?android:attr/selectableItemBackground</item>
        <item name="android:gravity">center</item>
        <item name="attr_text_color">?theme_text_color_home_navigation</item>
    </style>

    <style name="AlertDialog.AppCompat.Light.EditNote" parent="AlertDialog.AppCompat.Light">
        <item name="android:windowBackground">?theme_bg_color_white_1E</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowLightStatusBar" tools:targetApi="m">?theme_windowLightStatusBar</item>
    </style>

    <style name="Dialog.FullScreen" parent="Theme.AppCompat.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowLightStatusBar" tools:targetApi="m">?theme_windowLightStatusBar</item>
        <item name="android:windowTranslucentStatus">true</item>
    </style>

<!--        <item name="android:windowNoTitle">true</item>-->
    <style name="Dialog.EditNoteScreen" parent="Theme.AppCompat.Dialog">
        <item name="android:windowBackground">?theme_bg_color_white_1E</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowLightStatusBar" tools:targetApi="m">?theme_windowLightStatusBar</item>
    </style>

    <style name="DialogStyle" parent="Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowLightStatusBar" tools:targetApi="m">?theme_windowLightStatusBar</item>
    </style>

    <!--自定义RatingBar Drawable-->
    <style name="style_rating_bar_black" parent="@android:style/Widget.Holo.RatingBar.Indicator">
        <item name="android:progressDrawable">@drawable/layer_list_rating_bar_black</item>
        <item name="colorControlNormal">#888</item>
        <item name="colorControlActivated">#000</item>
    </style>

    <!--自定义RatingBar Color-->
    <style name="RatingBar_CustomColor" parent="@android:style/Widget.Holo.RatingBar.Indicator">
        <!--Background Color-->
        <item name="colorControlNormal">#D7D7D7</item>
        <!--Progress Color-->
        <item name="colorControlActivated">#F49800</item>
    </style>

    <style name="SSO_CheckBox" parent="Theme.AppCompat.Light">
        <item name="colorControlNormal">#EAEAEF</item>
        <item name="colorControlActivated">@color/color_E9973E</item>
    </style>

    <style name="dialogActivityTheme" parent="Theme.AppCompat.Dialog">
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode" tools:targetApi="o_mr1">shortEdges</item>
    </style>

    <!--顶部标题的文字样式-->
    <style name="style_title_text">
        <item name="android:textSize">18sp</item>
        <item name="android:textColor">?theme_text_color_black</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:layout_centerInParent">true</item>
        <item name="android:singleLine">true</item>
        <item name="android:ellipsize">middle</item>
    </style>
</resources>
