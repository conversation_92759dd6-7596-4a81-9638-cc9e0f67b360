package com.wedevote.wdbook.tools.util

import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import android.content.res.Resources
import android.graphics.Color
import android.os.Build
import androidx.annotation.StringRes
import com.aquila.lib.log.KLog
import com.bumptech.glide.load.model.GlideUrl
import com.bumptech.glide.load.model.LazyHeaders
import com.wedevote.wdbook.base.APP
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.Constants
import com.wedevote.wdbook.constants.HighlightColorType
import com.wedevote.wdbook.entity.store.AuthorEntity
import com.wedevote.wdbook.network.ApiHeader
import java.lang.reflect.Parameter

/***
 * @date 创建时间 2019-05-21 15:57
 * <AUTHOR> <PERSON>.<PERSON>
 * @description
 */

fun dp2px(i: Float): Float {
    val scale = Resources.getSystem().displayMetrics.density
    return i * scale
}

fun dp2px(i: Int): Int {
    val scale = Resources.getSystem().displayMetrics.density
    return (i * scale + 0.5f).toInt()
}

fun Context.scanForActivity(): Activity? {
    if (this is Activity) {
        return this
    } else if (this is ContextWrapper) {
        return this.scanForActivity()
    }
    return null
}

fun parseColor(type: HighlightColorType): Int {
    return parseColor(SDKSingleton.appBl.getHighlightColor(type))
}

fun parseColor(color: String?): Int {
    var resultColor = Color.TRANSPARENT
    if (!color.isNullOrEmpty()) {
        try {
            resultColor = Color.parseColor("#" + color.replace("#", ""))
        } catch (e: Exception) {
            e.printStackTrace()
            SDKSingleton.loggerBl.handleThrowable(e, getFunctionInfo())
        }
    }
    return resultColor
}

fun CharSequence?.isEquals(b: CharSequence?): Boolean {
    if (this == null || b == null) return false
    if (this === b) return true
    if ((this.length) == b.length) {
        if (this is String && b is String) {
            return this == b
        } else {
            for (i in 0 until length) {
                if (this.get(i) != b.get(i)) return false
            }
            return true
        }
    }
    return false
}

fun parseHtmlTag(text: String?): String? {
    return text?.replace("<em>", "<font color = #FF8A00>")?.replace("</em>", "</font>")
}

/*判断传进来所有的参数是否都不为空*/
fun hasEmptyString(vararg args: CharSequence?): Boolean {
    if (args.size == 0) return true
    
    for (a in args) {
        if (a.isNullOrEmpty()) {
            return true
        }
    }
    return false
}

/*将一个整数设置到一个区间值*/
fun Int.inValueArea(min: Int, max: Int): Int {
    return when {
        this < min -> min
        this > max -> max
        else -> this
    }
}

/*判断传进来所有的参数必须都不能为空*/
fun isAllObjNotNull(vararg args: Any?): Boolean {
    for (a in args) {
        if (a == null) {
            return false
        }
    }
    return true
}

/*判断传进来所有的参数是否都为空*/
fun isAllObjNull(vararg args: Any?): Boolean {
    for (a in args) {
        if (a != null) {
            return false
        }
    }
    return true
}

/*获取符合条件的startWith的文本长度*/
fun CharSequence.getStartWithTextLength(vararg any: String): Int {
    for (arg in any) {
        var index = indexOf(arg)
        if (index > 0) {
            return index + arg.length
        }
    }
    return 0
}

fun CharSequence?.isContainerOne(vararg any: CharSequence?): Boolean {
    if (this == null) return false
    for (arg in any) {
        if (this.contains(arg.toString())) {
            return true
        }
    }
    return false
}

/*是够包含其中一个的*/
fun Any?.isEqualOne(vararg any: Any?): Boolean {
    if (this == null) return false
    for (arg in any) {
        if (this.equals(arg)) {
            return true
        }
    }
    return false
}

enum class AreaResult {
    MIN, IN_AREA, MAX
}

/*将一个整数设置到一个区间值*/
fun Int.inValueArea(min: Int, max: Int, doAction: ((status: AreaResult) -> Unit)? = null): Int {
    if (min > max) {
        throw Exception("第一个参数的值必须要小于等于第二个参数的值")
    }
    var status = AreaResult.IN_AREA
    val result = when {
        this < min -> {
            status = AreaResult.MIN
            min
        }
        this > max -> {
            status = AreaResult.MAX
            max
        }
        else -> this
    }
    
    if (doAction != null) {
        doAction(status)
    }
    return result
}

fun Any.toJsonStr(): String {
    return GsonUtil.objectToJson(this)
}

private fun String.endsWith(toRegex: Regex, b: Boolean): Boolean {
    return this.endsWith(toRegex, b)
}

fun getPictureRemotePath(url: String?): GlideUrl? {
    if (url.isNullOrEmpty()) {
        return null
    }
    return GlideUrl(
        SDKSingleton.appBl.getFullImageUrl(url),
        LazyHeaders.Builder().addHeader(ApiHeader.REFERER, Constants.WD_BOOK_HTTP_REFERER).build()
    )
}

fun findString(@StringRes resId: Int): String {
    return APP.get().getString(resId)
}

fun <T> List<T>?.getLastElement(): T? {
    if (isNullOrEmpty()) {
        return null
    }
    return get(size - 1)
}

fun initAuthorsName(authorList: List<AuthorEntity>?): String {
    if (authorList.isNullOrEmpty()) {
        return ""
    }
    val sb = StringBuilder()
    for (entity in authorList) {
        sb.append(entity.name).append(" ")
    }
    return sb.toString()
}

fun initAuthorsStringName(authorList: List<String>?): String {
    if (authorList.isNullOrEmpty()) {
        return ""
    }
    val sb = StringBuilder()
    for (entity in authorList) {
        sb.append(entity).append(" ")
    }
    return sb.toString()
}


/*获取方法的参数*/
fun getFunctionInfo(): String {
    val targetElement = Thread.currentThread().stackTrace[3]
    val tag = targetElement.fileName
    val headString = "[(%s:%s).%s(%s)] ".format(
        targetElement.fileName, targetElement.lineNumber, targetElement.methodName,
        getMethodParametersInfo(targetElement.className, targetElement.methodName)
    )
    KLog.d("headString= $headString")
    return headString
}


private fun getMethodParametersInfo(className: String, methodName: String) {
    try {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val clazz = Class.forName(className)
            val methods = clazz.methods

            for (method in methods) {
                if (methodName.equals(method.name)) {
                    var sb = StringBuilder()
                    val parameters: Array<Parameter> = method.getParameters()
                    for (i in parameters.indices) {
                        sb.append("${parameters[i].getName()} ${parameters[i].getParameterizedType()}")
                        if (i < parameters.size - 1) {
                            sb.append(", ")
                        }
                    }
                }
            }
        }
    } catch (e: Exception) {
        e.printStackTrace()
        
    }
}
