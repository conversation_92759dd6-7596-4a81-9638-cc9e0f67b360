package com.wedevote.wdbook.tools.upgrade

import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.view.View
import androidx.core.content.FileProvider
import androidx.fragment.app.FragmentActivity
import com.aquila.lib.base.OnViewClickListener
import com.aquila.lib.download.ADownloadEntity
import com.aquila.lib.download.ADownloadingListener
import com.aquila.lib.log.KLog
import com.aquila.lib.tools.singleton.SPSingleton
import com.aquila.lib.tools.util.ToastUtil
import com.liulishuo.filedownloader.BaseDownloadTask
import com.liulishuo.filedownloader.FileDownloadListener
import com.liulishuo.filedownloader.FileDownloader
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.APPDistributionChannel
import com.wedevote.wdbook.entity.UpgradeEntity
import com.wedevote.wdbook.tools.util.DataPathUtil
import com.wedevote.wdbook.tools.util.SPKeyDefine
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import java.io.File
import com.aquila.lib.dialog.CommAlertDialog
import com.aquila.lib.layout.util.DensityUtil
import com.wedevote.wdbook.tools.util.toJsonStr

/***
 * @date 创建时间 2020/10/1 13:35
 * <AUTHOR> W.YuLong
 * @description
 */
class APPUpgradeManager(val activity: FragmentActivity) {
    val mainScope = MainScope()

    var apkDownloadEngine: APKDownloadEngine

    init {
        apkDownloadEngine = APKDownloadEngine(activity)
        apkDownloadEngine.setDownloadingListener(object : ADownloadingListener {
            override fun onBegin(entity: ADownloadEntity) {
                if (apkDownloadProgressDialog == null) {
                    apkDownloadProgressDialog = APKDownloadProgressDialog(activity)
                    apkDownloadProgressDialog!!.setCanceledOnTouchOutside(false)
                }
                apkDownloadProgressDialog!!.show()
                apkDownloadProgressDialog!!.updateProgressInfo(0)
            }

            override fun onDownloading(entity: ADownloadEntity, downloadSize: Long, totalSize: Long) {
                apkDownloadProgressDialog?.updateProgressInfo((downloadSize * 100f / totalSize).toInt())
            }

            override fun onComplete(entity: ADownloadEntity) {
                apkDownloadProgressDialog?.dismiss()
            }

            override fun onException(entity: ADownloadEntity, errorCode: Int, errorMsg: String) {
                apkDownloadProgressDialog?.dismiss()
            }
        })
    }

    var apkDownloadProgressDialog: APKDownloadProgressDialog? = null
    var alertDialog: CommAlertDialog? = null
    
    companion object {
        private var globalAlertDialog: CommAlertDialog? = null
        private var hasShownUpdateDialog: Boolean = false
        
        fun showUpdateDialog(activity: FragmentActivity, isForce: Boolean) {
            if (APPConfig.isFastClick()) return
            
            if (hasShownUpdateDialog) {
                return
            }
            
            if (globalAlertDialog != null && globalAlertDialog!!.isShowing) {
                return
            }
            
            val dialogBuilder = CommAlertDialog.with(activity)
                .setTitle(R.string.version_update_tip_title)
                .setMessage(R.string.version_update_tip_msg)
                .setDialogWidth(DensityUtil.dp2px(325f))
                .setEndText(R.string.update_now)
                .setLeftColorRes(R.color.text_color_blue_007AFF)
                .setRightColorRes(R.color.color_red_FF342A)
                .setTouchOutsideCancel(false)
                .setCancelAble(false)

            if (isForce) {
                dialogBuilder.setStartText(R.string.clase_app)
            } else {
                dialogBuilder.setStartText(R.string.later)
            }
            
            globalAlertDialog = dialogBuilder
                .setOnViewClickListener { dialog, view, i ->
                    when (i) {
                        CommAlertDialog.TAG_CLICK_END -> {
                            MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
                                try {
                                    val upgradeEntity = SDKSingleton.appBl.checkAppVersion(
                                        APPDistributionChannel.ANDROID)
                                    if (upgradeEntity != null) {
                                        val upgradeManager = APPUpgradeManager(activity)
                                        upgradeManager.getDownloadUrlBean(upgradeEntity, isForce)
                                    } else {
                                        ToastUtil.showToastLong(R.string.already_latest_version)
                                    }
                                } catch (e: Throwable) {
                                    ToastUtil.showToastLong(R.string.check_new_version_failed)
                                }
                            }
                        }

                        CommAlertDialog.TAG_CLICK_START -> {
                            if (isForce) {
                                activity.finish()
                                android.os.Process.killProcess(android.os.Process.myPid())
                            }
                        }
                    }
                }.create()
            
            if (!globalAlertDialog!!.isShowing) {
                globalAlertDialog!!.show()
                hasShownUpdateDialog = true
            }
        }
    }
    
    fun showUpdateDialog(isForce: Boolean) {
        showUpdateDialog(activity, isForce)
    }

    fun checkUpgrade(hideIgnore: Boolean, toastEnable: Boolean) {
        mainScope.launch {
            try {
                val upgradeEntity = SDKSingleton.appBl.checkAppVersion(APPDistributionChannel.ANDROID)
                if (upgradeEntity != null && (
                            hideIgnore || SPSingleton.get()
                                .getString(SPKeyDefine.SP_IgnoreUpdateVersion) != NewVersionDialog.getAppUpgradeInfo(upgradeEntity)
                            )
                ) {
                    val newVersionDialog = NewVersionDialog(activity)
                    newVersionDialog.show()
                    newVersionDialog.initUI(upgradeEntity, hideIgnore)
                    newVersionDialog.onViewClickListener = object : OnViewClickListener {
                        override fun <T> onClickAction(v: View, str: String, t: T?) {
                            val upgradeBean = t as UpgradeEntity
                            getDownloadUrlBean(upgradeBean, false)
                        }
                    }
                } else {
                    if (toastEnable) {
                        ToastUtil.showToastLong(R.string.already_latest_version)
                    }
                }
            } catch (e: Exception) {
                if (toastEnable) {
                    ToastUtil.showToastLong(R.string.check_new_version_failed)
                }
                ExceptionHandler.handleException(e)
            }
        }
    }

    fun getDownloadUrlBean(upgradeEntity: UpgradeEntity, isForce: Boolean = false) {
        mainScope.launch() {
            try {
                if (upgradeEntity.filePath.isNullOrEmpty()) {
                    if (upgradeEntity.fileId.isNotEmpty()) {
                        val downloadInfo = SDKSingleton.appBl.getAppDownloadUrl(upgradeEntity.fileId, APPDistributionChannel.ANDROID)
                        if (downloadInfo != null) {
                            executeDownloadAPK(upgradeEntity, downloadInfo.downloadUrl, isForce)
                        } else {
                            ToastUtil.showToastShort(R.string.get_download_link_failed)
                        }
                    }
                } else {
                    executeDownloadAPK(upgradeEntity, upgradeEntity.filePath, isForce)
                }
            } catch (e: Exception) {
                e.printStackTrace()
                ExceptionHandler.handleException(e)
            }
        }
    }

    fun executeDownloadAPK(entity: UpgradeEntity, downloadUrl: String?, isForce: Boolean = false) {
        if (downloadUrl.isNullOrEmpty()) {
            ToastUtil.showToastShort(R.string.get_download_link_failed)
            return
        }

        val path = "${DataPathUtil.getAPkDirPath()}${entity.versionName}_${entity.versionCode}.apk"
        FileDownloader.setup(activity)
        FileDownloader.getImpl().create(downloadUrl)
            .setPath(path)
            .setListener(object : FileDownloadListener() {
                override fun pending(task: BaseDownloadTask?, soFarBytes: Int, totalBytes: Int) {
                }

                override fun started(task: BaseDownloadTask?) {
                    super.started(task)
                    if (apkDownloadProgressDialog == null) {
                        apkDownloadProgressDialog = APKDownloadProgressDialog(activity)
                        apkDownloadProgressDialog!!.setCanceledOnTouchOutside(false)
                    }
                    apkDownloadProgressDialog!!.show()
                    apkDownloadProgressDialog!!.updateProgressInfo(0)
                }

                override fun progress(task: BaseDownloadTask?, soFarBytes: Int, totalBytes: Int) {
                    apkDownloadProgressDialog?.updateProgressInfo((soFarBytes * 100f / totalBytes).toInt())
                }

                override fun completed(task: BaseDownloadTask?) {
                    apkDownloadProgressDialog?.dismiss()
                    installAPK(path)

                    if (isForce) {
                        Handler(Looper.getMainLooper()).postDelayed({
                            activity.finishAffinity()
                            android.os.Process.killProcess(android.os.Process.myPid())
                        }, 1000)
                    }
                }

                override fun paused(task: BaseDownloadTask?, soFarBytes: Int, totalBytes: Int) {
                }

                override fun error(task: BaseDownloadTask, e: Throwable) {
                    apkDownloadProgressDialog?.dismiss()

                    e.printStackTrace()
                    KLog.e("下载出错了:" + task.etag, task.errorCause.message)
                }

                override fun warn(task: BaseDownloadTask) {
                    KLog.e(task.errorCause.message)
                }
            }).start()
    }

    private fun installAPK(apkPath: String) {
        if (!isForeground()) {
            return
        }
        val apkFile = File(apkPath)
        if (apkFile == null || !apkFile.exists()) {
            ToastUtil.showToastLong(R.string.package_not_exist)
            return
        } else if (!apkFile.exists()) {
            ToastUtil.showToastLong(R.string.package_error)
            return
        }
        val installIntent = Intent(Intent.ACTION_VIEW)
        installIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        val apkUri: Uri
        // 判读版本是否在7.0以上,解决7.0系统以上的安装apk问题
        apkUri = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            installIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            FileProvider.getUriForFile(activity, activity.packageName + ".fileprovider", apkFile)
        } else {
            Uri.fromFile(apkFile)
        }
        installIntent.setDataAndType(apkUri, "application/vnd.android.package-archive")
        installIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        activity.startActivity(installIntent)
    }

    fun isForeground(): Boolean {
        val am = activity.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val list = am.getRunningTasks(1)
        if (list != null && list.size > 0) {
            val cpn = list[0].topActivity
            if (activity.packageName == cpn!!.packageName) {
                return true
            }
        }
        return false
    }
}
