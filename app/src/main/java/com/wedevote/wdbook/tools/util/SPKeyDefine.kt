package com.wedevote.wdbook.tools.util

/***
 * @date 创建时间 2020/5/22 14:26
 * <AUTHOR> W<PERSON><PERSON>
 * @description
 */
object SPKeyDefine {
    const val SP_LoginUserId = "LoginUserId"
    const val SP_CurrentLanguage = "CurrentLanguage"

    const val SP_Brightness = "Brightness"

    const val SP_BookTextLevel = "BookTextLevel"

    const val SP_BookTextFont = "BookTextFont"

    const val SP_defaultCountry = "defaultCountry"

    const val SP_LoginAccountType = "LoginAccountType"

    const val SP_lastUser = "lastUser"

    const val SP_waitTime_mobile = "waitTimeMobile"
    const val SP_waitTime_email = "waitTimeEmail"

    const val SP_is_first_load_store = "isFirstLoadStore"

    /*上次支付的方式*/
    const val SP_LastPaymentStyle = "LastPaymentStyle"

    const val SP_IsLockScreen = "IsLockScreen"
    const val SP_IsWifiDownload = "IsWifiDownload"
    const val SP_IsCellularDownload = "IsCellularDownload"
    const val SP_isShowPage = "IsShowPage"
    const val SP_IgnoreUpdateVersion = "IgnoreUpdateVersion"

    const val SP_LastMarkType = "LastMarkType"
    const val SP_FistTimeDeleteNote = "FirstTimeDeleteNote"
    const val SP_FistTimeFoldNote = "FirstTimeFoldNote"
    const val SP_NotchHeight = "NotchHeight"
    const val SP_NeedCleanXMLData = "NeedCleanXMLData"

    @Deprecated("已弃用，发布前需要删掉")
    const val SP_ReceiveCouponNotification = "OpenNotificationStatus"

    /*首页横幅消息已点击关闭过的*/
    const val SP_ClosedNotifyTipId = "ClosedNotifyTipIdAndPublishId"
    const val SP_ClosedDialogMessageId = "DialogMessageIdClosed"

    const val SP_SpeedUpServer = "SpeedUpServer"  // QR Code 扫码得到的域名（用于替换 CFT 参数中的域名）

    const val SP_LastCFTConfigReloadingTime = "LastCFTConfigReloadingTime" // 上次重新加载 CFT 参数的时间
    const val SP_CFTFailedAPICount = "CFTFailedAPICount"   // CFT 模式下网络请求失败总数
    const val SP_LastDirectConnectionSuccess = "lastDirectConnectionSuccess" // 上次能否直连
}
