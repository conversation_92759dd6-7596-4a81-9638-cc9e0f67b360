package com.wedevote.wdbook.tools.download

import android.app.Dialog
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import android.view.Gravity
import android.view.View
import androidx.fragment.app.FragmentActivity
import com.aquila.lib.dialog.CommAlertDialog
import com.aquila.lib.dialog.OnDialogViewClickListener
import com.aquila.lib.log.KLog
import com.aquila.lib.tools.singleton.SPSingleton
import com.aquila.lib.tools.util.ToastUtil
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.Constants
import com.wedevote.wdbook.constants.DownloadStatus
import com.wedevote.wdbook.entity.DownloadDataEntity
import com.wedevote.wdbook.entity.ExceptionDataEntity
import com.wedevote.wdbook.entity.ExceptionType
import com.wedevote.wdbook.entity.store.BookFileDownloadEntity
import com.wedevote.wdbook.tools.upgrade.APPUpgradeManager
import com.wedevote.wdbook.tools.util.DataPathUtil
import com.wedevote.wdbook.tools.util.NetWorkUtils
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.ui.service.SyncDataService
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.async
import kotlinx.coroutines.launch

/***
 * @date 创建时间 2020/4/14 17:54
 * <AUTHOR> W.YuLong
 * @description 下载引擎
 */
class DownloaderEngine(val activity: FragmentActivity) : LifecycleFragment.LifecycleListener {
    private var onDownloadingListener: OnDownloadingListener? = null

    internal lateinit var binder: DownloadService.DownloadBinder

    var isServiceConnected = false
    var onServiceConnectedListener: OnDownloadServiceConnectedListener? = null

    fun setOnDownloadingListener(onDownloadingListener: OnDownloadingListener?) {
        this.onDownloadingListener = onDownloadingListener
        if (isServiceConnected && onDownloadingListener != null) {
            binder.addDownloadListener(onDownloadingListener)
        }
    }

    fun setServiceConnectedListener(onServiceConnectedListener: OnDownloadServiceConnectedListener?) {
        this.onServiceConnectedListener = onServiceConnectedListener
    }

    val serviceConnection = object : ServiceConnection {
        override fun onServiceDisconnected(name: ComponentName?) {
            isServiceConnected = false
        }

        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            isServiceConnected = true
            binder = service as DownloadService.DownloadBinder
            onDownloadingListener?.let {
                binder.addDownloadListener(it)
            }
            onServiceConnectedListener?.onServiceConnected()
        }
    }

    init {
        activity.supportFragmentManager.beginTransaction().add(LifecycleFragment(this), LifecycleFragment::class.java.name)
            .commitAllowingStateLoss()
    }

    override fun onLifecycleCreate() {
        super.onLifecycleCreate()
        val intent = Intent(activity, DownloadService::class.java)
        activity.startService(intent)
        activity.bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
    }

    override fun onLifecycleDestroy() {
        onDownloadingListener?.let {
            if (isServiceConnected) {
                binder.removeDownloadListener(it)
            }
        }
        activity.unbindService(serviceConnection)
    }

    fun removeListener() {
        onDownloadingListener?.let {
            if (isServiceConnected) {
                binder.removeDownloadListener(it)
            }
        }
    }

    fun readyDownloadOnlyByFileId(fileId: String) {
        if (!NetWorkUtils.isNetworkAvailable()) {
            return
        }
        if (binder.isInDownloadQueue(fileId)) {
            return
        }
        MainScope().launch() {
//                async { SDKSingleton.syncBl.syncAppAndUserData() }
            async {
                try {
                    var bookFileDownloadEntity = SDKSingleton.downloadBl.getFileDownloadUrl(fileId)
                    if (bookFileDownloadEntity != null) {
                        var downloadEntity = SDKSingleton.downloadBl.fetchDownloadFileEntity(
                            fileId,
                            DataPathUtil.getDownloadPath()
                        )
                        if (downloadEntity != null) {
                            startDownload(bookFileDownloadEntity, downloadEntity)
                        } else {
                            ToastUtil.showToastShort(R.string.error_create_download_info)
                            SDKSingleton.loggerBl.saveAndReportExceptionLog(
                                ExceptionDataEntity().apply {
                                    errorType = ExceptionType.DEFAULT.value
                                    errorCode = 0
                                    errorMessage =
                                        "${findString(R.string.error_create_download_info)}, fileId:$fileId\n\n\n"
                                }
                            )
                        }
                    } else {
                        //                    ToastUtil.showToastShort(R.string.get_file_download_url_error)
                        SDKSingleton.loggerBl.saveAndReportExceptionLog(
                            ExceptionDataEntity().apply {
                                errorType = ExceptionType.DEFAULT.value
                                errorCode = 0
                                errorMessage =
                                    "${findString(R.string.get_file_download_url_error)}, fileId:$fileId\n\n\n"
                            }
                        )
                    }
                } catch (e: Exception) {
                    ExceptionHandler.handleException(e)
                    e.printStackTrace()
                }
            }
        }
    }

    fun getFileUrlAndDownload(entity: DownloadDataEntity) {
        if (entity.fileId.isNullOrEmpty()) {
            return
        }
        if (binder.isInDownloadQueue(entity.fileId!!)) {
            return
        }
        MainScope().launch {
            try {
                SDKSingleton.downloadBl.getFileDownloadUrl(entity.fileId!!)?.also { data ->
                    startDownload(data, entity)
                }
            } catch (e: Exception) {
                if (!APPConfig.isFastClick()){
                    ToastUtil.showToastShort(R.string.get_file_download_url_failure)
                }
                ExceptionHandler.handleException(e)
            }
        }
    }

    fun showUpdateAPPDialog() {
        CommAlertDialog.with(activity).setMessage(R.string.need_update_app_prompt)
            .setStartText(R.string.label_cancel).setEndText(R.string.goto_update)
            .setAllButtonColorRes(R.color.text_color_blue_007AFF)
            .setOnViewClickListener { _, _, tag ->
                when (tag) {
                    CommAlertDialog.TAG_CLICK_END -> {
                        APPUpgradeManager(activity).checkUpgrade(true, true)
                    }
                }
            }.create().show()
    }

    fun startDownload(entity: BookFileDownloadEntity, downloadDataEntity: DownloadDataEntity) {
        var resourceDownloadInfo = SDKSingleton.dbWrapBl.getResourceDownloadInfo(downloadDataEntity.resourceId)
        if (resourceDownloadInfo != null && resourceDownloadInfo.needUpgradeApp) {
            showUpdateAPPDialog()
            return
        }
        if (downloadDataEntity.downloadStatus == DownloadStatus.COMPLETE) {
            return
        }
        MainScope().launch {
            try {
                val encryptionKey = SDKSingleton.userBl.getEncryptionKey(entity.fileId)
                encryptionKey?.let {
                    executeDownload(entity, downloadDataEntity)
                }
            } catch (e: Exception) {
                KLog.e("获取下载数据失败")
//                ToastUtil.showToastShort(R.string.get_download_data_failure)
                // 当下载失败之后需要进行同步一下操作
                activity.startService(Intent(activity, SyncDataService::class.java))
                ExceptionHandler.handleException(e)
            }
        }
    }

    var alertDialog: CommAlertDialog? = null

    fun executeDownload(
        bookFileDownloadEntity: BookFileDownloadEntity,
        downloadDataEntity: DownloadDataEntity
    ) {
        readyDownload(bookFileDownloadEntity, downloadDataEntity)
    }

    private fun checkWifi(
        fileId : String,
        callBack: (() -> Unit)? = null
    ) {
        if (binder.isInDownloadQueue(fileId)) {
            return
        }
        if (SPSingleton.get().getBoolean(SPKeyDefine.SP_IsWifiDownload, true) &&
            NetWorkUtils.getNetworkType() != NetWorkUtils.NET_TYPE_WIFI &&
            !SPSingleton.get().getBoolean(SPKeyDefine.SP_IsCellularDownload, false)
        ) {
            if (alertDialog != null && alertDialog!!.isShowing) {
                alertDialog!!.dismiss()
            }
            alertDialog =
                CommAlertDialog.with(activity).setMessage(R.string.dialog_if_continue_download)
                    .setStartText(findString(R.string.label_cancel))
                    .setEndText(findString(R.string.btn_continue))
                    .setAllButtonColorRes(R.color.text_color_blue_007AFF)
                    .setOnViewClickListener(object : OnDialogViewClickListener {
                        override fun onViewClick(dialog: Dialog, v: View, tag: Int) {
                            when (tag) {
                                CommAlertDialog.TAG_CLICK_START -> {
                                }
                                CommAlertDialog.TAG_CLICK_END -> {
                                    callBack?.invoke()
                                    SPSingleton.get()
                                        .putBoolean(SPKeyDefine.SP_IsCellularDownload, true)
                                }
                            }
                        }
                    }).create()
            if (!alertDialog!!.isShowing) {
                alertDialog!!.show()
            }
        } else {
            callBack?.invoke()
        }
    }

    private val commAlertDialog = CommAlertDialog.with(activity).setMessageGravity(Gravity.CENTER)
        .setMessage(R.string.tip_open_failed)
        .setStartText(R.string.label_OK)
        .setAllButtonColorRes(R.color.text_color_blue_007AFF)
        .create()

    fun readyDownload(entity: BookFileDownloadEntity, downloadDataEntity: DownloadDataEntity) {
        if (SDKSingleton.dbWrapBl.getLocalEncryptionKey(entity.fileId).isNullOrEmpty()) {
            if (!commAlertDialog.isShowing) {
                commAlertDialog.show()
            }
            return
        }
        binder.startOrPauseDownloadTask(entity, downloadDataEntity)
    }

    fun isInDownloadingTaskQueue(fileId: String?): Boolean {
        return isServiceConnected && binder.isInDownloadQueue(fileId)
    }
}
