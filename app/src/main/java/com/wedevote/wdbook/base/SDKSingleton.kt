package com.wedevote.wdbook.base

import com.aquila.lib.log.KLog
import com.wedevote.wdbook.SDK
import com.wedevote.wdbook.bl.ApiServerBl
import com.wedevote.wdbook.bl.AppBl
import com.wedevote.wdbook.bl.BibleBl
import com.wedevote.wdbook.bl.DownloadBl
import com.wedevote.wdbook.bl.LoggerBl
import com.wedevote.wdbook.bl.PaymentBl
import com.wedevote.wdbook.bl.SessionBl
import com.wedevote.wdbook.bl.StoreBl
import com.wedevote.wdbook.bl.SyncBl
import com.wedevote.wdbook.bl.UserBl
import com.wedevote.wdbook.create
import p67.fox.Client

/***
 * @date 创建时间 2021/4/26 16:03
 * <AUTHOR> W.<PERSON>
 * @description
 */
object SDKSingleton {
    lateinit var sdk: SDK
    var isConnectWithProxy = false

    fun initializeSdk(app: APP) {
        val proxy = Client.proxy()
        isConnectWithProxy = proxy != null
        KLog.d("Initialize SDK with proxy $isConnectWithProxy")
        sdk = SDK.Companion.create(app, APPConfig.env, true, proxy)
    }

    val loggerBl: LoggerBl
        get() = sdk.loggerBl

    val dbWrapBl: LocalDBWrapBl
        get() = LocalDBWrapBl(sdk)

    val apiServerBl: ApiServerBl
        get() = sdk.apiServerBl

    val userBl: UserBl
        get() = sdk.userBl

    val bibleBl: BibleBl
        get() = sdk.bibleBl

    val sessionBl: SessionBl
        get() = sdk.sessionBl

    val syncBl: SyncBl
        get() = sdk.syncBl

    val appBl: AppBl
        get() = sdk.appBl

    val storeBl: StoreBl
        get() = sdk.storeBl

    val paymentBl: PaymentBl
        get() = sdk.paymentBl

    val downloadBl: DownloadBl
        get() = sdk.downloadBl
}
