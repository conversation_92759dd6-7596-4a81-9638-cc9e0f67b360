package com.wedevote.wdbook.base

import android.app.Activity
import android.app.Application
import android.os.Bundle
import androidx.fragment.app.FragmentActivity
import com.wedevote.wdbook.BusinessPatch
import com.wedevote.wdbook.tools.upgrade.APPUpgradeManager
import java.lang.ref.WeakReference

/**
 * Android端的业务逻辑实现
 */
class AndroidBusinessPatch : BusinessPatch {

    companion object {
        private var currentActivityRef: WeakReference<Activity>? = null

        fun init(application: Application) {
            application.registerActivityLifecycleCallbacks(object : Application.ActivityLifecycleCallbacks {
                override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {}
                override fun onActivityStarted(activity: Activity) {}
                override fun onActivityResumed(activity: Activity) {
                    currentActivityRef = WeakReference(activity)
                }
                override fun onActivityPaused(activity: Activity) {}
                override fun onActivityStopped(activity: Activity) {}
                override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {}
                override fun onActivityDestroyed(activity: Activity) {
                    if (currentActivityRef?.get() == activity) {
                        currentActivityRef = null
                    }
                }
            })
        }
    }

    //Android端的App升级通知
    override fun onAppNeedUpdateNotification(isForce: Boolean, upgradeUrl: String?) {
        currentActivityRef?.get()?.let { activity ->
            if (activity is FragmentActivity) {
                APPUpgradeManager.showUpdateDialog(activity, isForce)
            }
        }
    }
}
