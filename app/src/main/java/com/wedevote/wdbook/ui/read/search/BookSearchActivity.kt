package com.wedevote.wdbook.ui.read.search

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.KeyEvent
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.aquila.lib.dialog.CommAlertDialog
import com.aquila.lib.layout.SmartRefreshLayout
import com.aquila.lib.layout.api.RefreshLayout
import com.aquila.lib.layout.listener.OnLoadMoreListener
import com.aquila.lib.layout.listener.OnRefreshListener
import com.aquila.lib.layout.util.DensityUtil
import com.aquila.lib.tools.util.ToastUtil
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.store.SearchItemEntity
import com.wedevote.wdbook.tools.interfaces.OnItemClickListener
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.ui.dialogs.LoadingProgressDialog
import com.wedevote.wdbook.ui.store.FlexboxDataAdapter
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import com.wedevote.wdbook.tools.event.OnBookSearchCallFinishEvent
import com.wedevote.wdbook.tools.event.OnBookSearchClearEvent
import com.wedevote.wdbook.tools.util.NetWorkUtils
import com.wedevote.wdbook.ui.read.lib.EPubBook

/***
 *@date 创建时间 2025/02/08
 *<AUTHOR> John.Qian
 *@description  书籍内搜索页面
 */
class BookSearchActivity : RootActivity(), View.OnClickListener, OnLoadMoreListener,
    OnRefreshListener {

    lateinit var searchEditText: EditText
    lateinit var clearInputImageView: ImageView
    lateinit var cancelTextView: TextView
    lateinit var historyCommendLayout: ConstraintLayout
    lateinit var historyLayout: ConstraintLayout
    lateinit var deleteHistoryImageView: ImageView
    lateinit var historyDataRecyclerView: CustomRecyclerView

    lateinit var noResultLayout: LinearLayout
    lateinit var resultDataRecyclerView: CustomRecyclerView
    lateinit var resultRefreshLayout: SmartRefreshLayout
    lateinit var noHistoryLayout: LinearLayout

    lateinit var historyFlexAdapter: FlexboxDataAdapter<SearchItemEntity>
    lateinit var resultDataAdapter: BookSearchResultAdapter

    private lateinit var loadingDialog: LoadingProgressDialog
    private var resourceId: String = ""

    // BookSearchManager实例
    private val searchManager = BookSearchManager.getInstance()

    // 是否需要恢复滚动位置
    private var needRestoreScrollPosition = false
    // 保存恢复的滚动位置信息
    private var restoredFirstVisibleItemPosition: Int = -1
    private var restoredFirstVisibleItemOffset: Int = 0

    enum class SearchModeUI(val value: Int) {
        HISTORY(0),
        RESULT_DATA(1),
        NO_DATA(2),
    }

    private var currentMode = SearchModeUI.HISTORY

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_search_in_book_layout)
        initViewFromXML()
        resourceId = intent.getStringExtra(IntentConstants.EXTRA_ResourceId) ?: ""

        searchManager.resourceId = resourceId

        loadingDialog = LoadingProgressDialog(this)
        initAdapters()

        // 恢复保存的数据
        val restoreResult = searchManager.restoreSavedData()
        needRestoreScrollPosition = restoreResult.first
        restoredFirstVisibleItemPosition = restoreResult.second
        restoredFirstVisibleItemOffset = restoreResult.third

        if (searchManager.getKeywords().trim().isNotEmpty()) {
            searchEditText.setText(searchManager.getKeywords())
            searchEditText.setSelection(searchManager.getKeywords().length)
            clearInputImageView.visibility = View.VISIBLE
        }

        resultRefreshLayout.isEnableLoadMore = searchManager.isEnableLoadMore

        if (resultDataAdapter.itemCount <= 0) {
            initCurrentModeShow(SearchModeUI.HISTORY)
        } else {
            initCurrentModeShow(SearchModeUI.RESULT_DATA)
        }

        loadSearchHistoryData()
        setViewListeners()

        if (savedInstanceState == null) {
            overridePendingTransition(R.anim.anim_move_from_bottom, 0)
        }

        // 滚动恢复
        resultDataRecyclerView.post {
            if (needRestoreScrollPosition) {
                needRestoreScrollPosition = false // 只恢复一次
                val layoutManager = resultDataRecyclerView.layoutManager as? androidx.recyclerview.widget.LinearLayoutManager
                if (layoutManager != null && restoredFirstVisibleItemPosition >= 0) {
                    if (restoredFirstVisibleItemOffset == -1) {
                        centerItemOnScreen(layoutManager, restoredFirstVisibleItemPosition)
                    } else {
                        layoutManager.scrollToPositionWithOffset(restoredFirstVisibleItemPosition, restoredFirstVisibleItemOffset)
                    }
                }
            }
        }
    }

    override fun recreate() {
        searchManager.saveCurrentData()
        super.recreate()
    }

    private fun initViewFromXML() {
        searchEditText = findViewById(R.id.search_input_EditText)
        clearInputImageView = findViewById(R.id.search_clear_input_ImageView)
        cancelTextView = findViewById(R.id.search_cancel_TextView)
        historyCommendLayout = findViewById(R.id.search_history_and_recommend_container_layout)
        historyLayout = findViewById(R.id.search_history_container_layout)
        deleteHistoryImageView = findViewById(R.id.search_delete_history_ImageView)
        historyDataRecyclerView = findViewById(R.id.search_history_RecyclerView)
        noResultLayout = findViewById(R.id.search_no_result_Layout)
        resultDataRecyclerView = findViewById(R.id.search_result_data_RecyclerView)
        resultRefreshLayout = findViewById(R.id.search_data_RefreshLayout)
        noHistoryLayout = findViewById(R.id.search_no_history_Layout)
    }

    private fun initAdapters() {
        historyFlexAdapter = FlexboxDataAdapter(FlexboxDataAdapter.FlexboxViewType.SEARCH_HISTORY)
        historyFlexAdapter.enableSelectItem = false
        historyDataRecyclerView.adapter = historyFlexAdapter
        resultDataAdapter = BookSearchResultAdapter()
        resultDataRecyclerView.adapter = resultDataAdapter

        searchManager.resultDataAdapter = resultDataAdapter
    }

    private fun setViewListeners() {
        clearInputImageView.setOnClickListener(this)
        cancelTextView.setOnClickListener(this)
        deleteHistoryImageView.setOnClickListener(this)
        resultRefreshLayout.setOnRefreshListener(this).setOnLoadMoreListener(this)
        val onFlexItemSelectListener = object : OnItemClickListener {
            override fun <T> onItemClick(obj: Any, tag: String, t: T) {
                if (t is SearchItemEntity) {
                    searchEditText.setText(t.keyWords)
                    searchEditText.setSelection(t.keyWords.length)
                    searchManager.setKeywords(t.keyWords)
                    userDoSearchAction(t)
                } else if (t is String) {
                    searchEditText.setText(t)
                    searchEditText.setSelection(t.length)
                    userDoSearchAction(null)
                }
            }
        }
        historyFlexAdapter.onItemClickListener = onFlexItemSelectListener

        searchEditText.setOnEditorActionListener(object : TextView.OnEditorActionListener {
            override fun onEditorAction(v: TextView?, actionId: Int, event: KeyEvent?): Boolean {
                if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                    userDoSearchAction()
                    return true
                }
                return false
            }
        })
        searchEditText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(
                charSequence: CharSequence,
                start: Int,
                before: Int,
                count: Int
            ) {
                val textLength = charSequence.toString().length
                if (textLength > 50) {
                    ToastUtil.showToastCenter(getString(R.string.search_keyword_length_limit))
                    val truncatedText = truncateToMaxLength(charSequence.toString(), 50)
                    searchEditText.setText(truncatedText)
                    searchEditText.setSelection(truncatedText.length)
                } else if (textLength < 50) {
                    searchEditText.isSelected = false
                }
            }

            override fun afterTextChanged(s: Editable?) {
                clearInputImageView.visibility = if (s!!.length > 0) View.VISIBLE else View.GONE
            }
        })

        searchEditText.setOnKeyListener { v, keyCode, event ->
            if (event.action == KeyEvent.ACTION_DOWN && keyCode == KeyEvent.KEYCODE_DEL) {
                val editText = v as EditText
                val text = editText.text.toString()
                if (EPubBook.containsHebrew(text)) {
                    val selectionEnd = editText.selectionEnd
                    // 检查光标是否不在文本末尾
                    if (selectionEnd < text.length) {
                        // 删除光标右边的字符
                        val newText = text.substring(0, selectionEnd) + text.substring(selectionEnd + 1)
                        editText.setText(newText)
                        // 保持光标位置不变（相对于删除前的右侧字符）
                        editText.setSelection(selectionEnd)
                        return@setOnKeyListener true
                    }
                }
            }
            return@setOnKeyListener false
        }
    }

    /*用户手动点击搜索操作*/
    fun userDoSearchAction(entity: SearchItemEntity? = null) {
        if (!NetWorkUtils.isNetworkAvailable()) {
            ToastUtil.showToastLong(R.string.no_network_connect)
        }
        if (entity == null) {
            searchManager.setKeywords(searchEditText.text.toString())
        }

        // 执行搜索
        searchManager.userDoSearchAction(
            this,
            entity,
            onSearchStarted = {
                loadSearchHistoryData()
                loadingDialog.show()
                loadingDialog.setTitleText(getString(R.string.loading))
            },
            onSearchFinished = { success ->
                resultRefreshLayout.finishLoadMoreAndRefresh()
                resultRefreshLayout.isEnableLoadMore = searchManager.isEnableLoadMore
                if (resultDataAdapter.itemCount <= 0) {
                    initCurrentModeShow(SearchModeUI.NO_DATA)
                } else {
                    initCurrentModeShow(SearchModeUI.RESULT_DATA)
                }

                if (loadingDialog.isShowing) {
                    loadingDialog.dismiss()
                }
                if (!success) {
                    ToastUtil.showToastShort(R.string.no_network_connect)
                }
            }
        )
    }

    private fun loadSearchHistoryData() {
        historyFlexAdapter.dataList = searchManager.loadSearchHistoryData(resourceId)
        historyLayout.visibility = if (historyFlexAdapter.itemCount > 0) View.VISIBLE else View.GONE
        if (historyFlexAdapter.itemCount > 0) {
            noHistoryLayout.visibility = View.GONE
        } else {
            noHistoryLayout.visibility = View.VISIBLE
        }
    }

    override fun onRefresh(refreshLayout: RefreshLayout?) {
        searchManager.currentPage = 1
        resultRefreshLayout.isEnableLoadMore = true
        resultDataAdapter.clearDataList()
        onLoadMore(resultRefreshLayout)
    }

    override fun onLoadMore(refreshLayout: RefreshLayout) {
        searchManager.loadMoreData(this) { success ->
            resultRefreshLayout.finishLoadMoreAndRefresh()
            resultRefreshLayout.isEnableLoadMore = searchManager.isEnableLoadMore
            if (resultDataAdapter.itemCount <= 0) {
                initCurrentModeShow(SearchModeUI.NO_DATA)
            } else {
                initCurrentModeShow(SearchModeUI.RESULT_DATA)
            }

            if (loadingDialog.isShowing) {
                loadingDialog.dismiss()
            }
        }
    }

    private fun initCurrentModeShow(mode: SearchModeUI) {
        when (mode) {
            SearchModeUI.HISTORY -> {
                historyCommendLayout.visibility = View.VISIBLE
                resultRefreshLayout.visibility = View.GONE
                noResultLayout.visibility = View.GONE
            }

            SearchModeUI.RESULT_DATA -> {
                historyCommendLayout.visibility = View.GONE
                resultRefreshLayout.visibility = View.VISIBLE
                noResultLayout.visibility = View.GONE
                val inputMethodManager =
                    getSystemService(INPUT_METHOD_SERVICE) as android.view.inputmethod.InputMethodManager
                inputMethodManager.hideSoftInputFromWindow(searchEditText.windowToken, 0)
                searchEditText.clearFocus()
            }

            SearchModeUI.NO_DATA -> {
                historyCommendLayout.visibility = View.GONE
                resultRefreshLayout.visibility = View.GONE
                noResultLayout.visibility = View.VISIBLE
            }
        }
        currentMode = mode
    }

    override fun onClick(v: View?) {
        when (v) {
            cancelTextView -> {
                onBackPressed()
            }

            clearInputImageView -> {
                searchEditText.setText("")
                initCurrentModeShow(SearchModeUI.HISTORY)
            }

            deleteHistoryImageView -> {
                showClearSearchHistoryDialog()
            }
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
        // 发送清理搜索高亮和隐藏搜索菜单的事件
        EventBus.getDefault().post(OnBookSearchClearEvent())
        searchManager.clearSavedData()
    }

    fun showClearSearchHistoryDialog() {
        CommAlertDialog.with(this)
            .setTitle(R.string.are_you_sure_delete_all_history)
            .setTitleTextTextSize(16)
            .setDialogHeight(DensityUtil.dp2px(110f))
            .setDialogWidth(DensityUtil.dp2px(325f))
            .setStartText(R.string.label_cancel).setEndText(R.string.label_delete)
            .setLeftColorRes(R.color.text_color_blue_007AFF)
            .setRightColorRes(R.color.color_red_FF342A)
            .setOnViewClickListener { dialog, view, i ->
                if (i == CommAlertDialog.TAG_CLICK_END) {
                    SDKSingleton.appBl.clearSearchHistory()
                    loadSearchHistoryData()
                    ToastUtil.showToastShort(R.string.has_clear_search_data)
                }
            }.showDialog()
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(0, R.anim.anim_move_to_bottom)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun OnBookSearchCallFinishEvent(event: OnBookSearchCallFinishEvent) {
        searchManager.clearSavedData()
        finish()
    }

    /**
     * 截断字符串到指定的最大字符数
     */
    private fun truncateToMaxLength(text: String, maxLength: Int): String {
        var count = 0
        var index = 0

        while (index < text.length) {
            val char = text[index]
            val charCount = if (char.toInt() < 127) 1 else 2

            if (count + charCount > maxLength) {
                break
            }

            count += charCount
            index++
        }

        return text.substring(0, index)
    }

    fun finishWithAnimation() {
        searchManager.setKeywords(searchEditText.text.toString())
        // 保存当前的滚动位置信息
        var firstVisibleItemPosition = -1
        var firstVisibleItemOffset = 0
        val layoutManager = resultDataRecyclerView.layoutManager as? androidx.recyclerview.widget.LinearLayoutManager
        if (layoutManager != null) {
            firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition()
            val firstVisibleItemView = layoutManager.findViewByPosition(firstVisibleItemPosition)
            if (firstVisibleItemView != null) {
                firstVisibleItemOffset = layoutManager.getDecoratedTop(firstVisibleItemView)
            }
        }
        searchManager.saveCurrentData(firstVisibleItemPosition, firstVisibleItemOffset)
        finish()
    }

    /**
     * 将指定位置的 Item 滚动到屏幕中央
     */
    private fun centerItemOnScreen(layoutManager: androidx.recyclerview.widget.LinearLayoutManager, position: Int) {
        val recyclerViewHeight = resultDataRecyclerView.height
        layoutManager.scrollToPositionWithOffset(position, 0)
        resultDataRecyclerView.post {
            val view = layoutManager.findViewByPosition(position)
            if (view != null) {
                val viewHeight = view.height
                val offset = (recyclerViewHeight - viewHeight) / 2
                if (offset > 0) {
                    layoutManager.scrollToPositionWithOffset(position, offset)
                }
            } else {
                 layoutManager.scrollToPosition(position)
            }
        }
    }

    companion object {
        fun clearSavedData() {
            BookSearchManager.getInstance().clearSavedData()
        }
    }
}


