package com.wedevote.wdbook.ui.home.microwidget

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.core.text.isDigitsOnly
import com.aquila.lib.dialog.CommAlertDialog
import com.aquila.lib.log.KLog
import com.aquila.lib.tools.util.ToastUtil
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.base.WrapCoroutineHelper
import com.wedevote.wdbook.constants.Constants
import com.wedevote.wdbook.constants.DownloadStatus
import com.wedevote.wdbook.entity.BookNoteCountEntity
import com.wedevote.wdbook.entity.resource.ResourceDownloadInfo
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.NetWorkUtils
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.tools.util.getFunctionInfo
import com.wedevote.wdbook.ui.AboutActivity
import com.wedevote.wdbook.ui.CommWebViewActivity
import com.wedevote.wdbook.ui.account.SSOLoginActivity
import com.wedevote.wdbook.ui.home.BookSectionListActivity
import com.wedevote.wdbook.ui.home.HomeMainActivity
import com.wedevote.wdbook.ui.home.HomeTab
import com.wedevote.wdbook.ui.read.BookReadActivity
import com.wedevote.wdbook.ui.store.BookCategoryListActivity
import com.wedevote.wdbook.ui.store.BookDetailActivity
import com.wedevote.wdbook.ui.store.NewBookListActivity
import com.wedevote.wdbook.ui.store.ProductActiveActivity
import com.wedevote.wdbook.ui.store.RecommendBookListActivity
import com.wedevote.wdbook.ui.store.search.SearchActivity
import com.wedevote.wdbook.ui.user.BookDownloadActivity
import com.wedevote.wdbook.ui.user.BookNoteCountActivity
import com.wedevote.wdbook.ui.user.BookNoteDetailActivity
import com.wedevote.wdbook.ui.user.FavoriteBookListActivity
import com.wedevote.wdbook.ui.user.OnBookDownloadCallback
import com.wedevote.wdbook.ui.user.PurchasedBookListActivity
import com.wedevote.wdbook.ui.user.feedback.EditFeedbackActivity
import com.wedevote.wdbook.ui.user.feedback.FaqArticleDetailActivity
import com.wedevote.wdbook.ui.user.feedback.FeedbackDetailActivity
import com.wedevote.wdbook.ui.user.feedback.HelpCenterActivity
import com.wedevote.wdbook.ui.user.feedback.UserFeedbackListActivity
import com.wedevote.wdbook.ui.user.notification.NotificationCenterActivity
import com.wedevote.wdbook.utils.JsonUtility
import java.io.File
import java.util.LinkedList
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/***
 * @date 创建时间 2020/10/20 11:26
 * <AUTHOR> W.YuLong
 * @description DeepLink
 */
object DeepLinkUtils {
    const val openBookMark = "openBookMark"
    
    /*Deeplink的解析*/
    fun parseDeepLink(context: Context, link: String): Boolean {
        val linkUri = Uri.parse(link)
        val linkPath: String = when {
            linkUri.scheme == Constants.WD_BOOK_SCHEMA || linkUri.scheme == SDKSingleton.appBl.wdBookSchema -> {
                linkUri.schemeSpecificPart.trimStart('/')
            }
            linkUri.host == SDKSingleton.appBl.wdBookSchemaHost -> {
                linkUri.path!!.trimStart('/')
            }
            else -> {
                return false
            }
        }
        
        val linkParamArray = if (linkPath.trim().isNullOrEmpty()) emptyList() else linkPath.split("/")
        
        val paramsMap = HashMap<String, String>()
        for (name in linkUri.queryParameterNames) {
            paramsMap[name] = linkUri.getQueryParameter(name).toString()
        }
        KLog.e(
            JsonUtility.encodeToString(paramsMap),
            "linkUri.scheme = ${linkUri.scheme}",
            "linkUri.host = ${linkUri.host}",
            "linkPath = $linkPath",
            "linkParams = $linkParamArray"
        )
        
        // 只有scheme就跳转到首页
        if (linkParamArray.isNullOrEmpty()) {
            /* wdbook:// */
            HomeMainActivity.gotoHomeActivity(context)
        } else {
            val queue = LinkedList<String>()
            for (str in linkParamArray) {
                queue.offer(str)
            }
    
            var firstParam = queue.poll()
            var model = if (firstParam.contains("?")) {
                firstParam.split("?")[0]
            } else {
                firstParam
            }
            var secondParam = queue.poll()
            when (model) {
                /*商品 dp*/
                "dp" -> {
                    /* dp/{productId} */
                    val intent = Intent()
                    intent.setClass(context, BookDetailActivity::class.java)
                    intent.putExtra(IntentConstants.EXTRA_ProductId, linkParamArray[1].toLong())
                    context.startActivity(intent)
                }
                /*阅读器页面*/
                "reader" -> {
                    if (!secondParam.isNullOrEmpty()) {
                        /* reader/{resourceId} */
                        val shelfBookEntity = SDKSingleton.userBl.getShelfBookItemEntityByResourceId(linkParamArray[1])
                        if (shelfBookEntity != null) {
                            val downloadInfo = SDKSingleton.dbWrapBl.getResourceDownloadInfo(linkParamArray[1])!!
                            if ((downloadInfo.downloadStatus == DownloadStatus.COMPLETE || downloadInfo.downloadStatus == DownloadStatus.UPDATE) &&
                                File(downloadInfo.getActualFilePath()).exists()
                            ) {
                                BookReadActivity.gotoBookReadActivity(
                                    context,
                                    downloadInfo.getActualFilePath(),
                                    downloadInfo.fileId,
                                    linkParamArray[1]
                                )
                            } else {
                                showNeedDownloadDialog(context, linkParamArray[1]) { info ->
                                    BookReadActivity.gotoBookReadActivity(
                                        context,
                                        info.getActualFilePath(),
                                        downloadInfo.fileId,
                                        linkParamArray[1]
                                    )
                                }
//                                ToastUtil.showToastShort("当前书籍还没有下载，请前往书架下载")
                            }
                        } else {
                            ToastUtil.showToastShort("你还没购买本书，请去商城购买。")
                        }
                    }
                }
                /*试读页面*/
                "trial" -> {
                    if (!secondParam.isNullOrEmpty()) {
                        /* trial/{resourceId} */
                        val downloadInfo = SDKSingleton.dbWrapBl.getResourceDownloadInfo(secondParam)!!
                        if ((downloadInfo.downloadStatus == DownloadStatus.COMPLETE || downloadInfo.downloadStatus == DownloadStatus.UPDATE) &&
                            File(downloadInfo.getActualFilePath()).exists()
                        ) {
                            BookReadActivity.gotoBookReadActivity(
                                context,
                                downloadInfo.getActualFilePath(),
                                downloadInfo.fileId,
                                linkParamArray[1]
                            )
                        } else {
                            ToastUtil.showToastShort(R.string.please_download_before_read)
                        }
                    }
                }
                "browser" -> {
                    /* /browser?url=http://wayshare.cn */
                    val url = linkUri.getQueryParameter("url")
                    KLog.d("browser url = $url")
                    CommWebViewActivity.gotoWebView(context, url!!)
                }
                "store" -> {
                    if (secondParam.isNullOrEmpty()) { // 直接去书城首页
                        /* store */
                        HomeMainActivity.gotoHomeActivity(context)
                    } else {
                        val thirdParam = queue.poll()
                        when (secondParam.split("?")[0]) {
                            "search" -> {
                                /* store/search[?query={keyword}] */
                                val intent = Intent(context, SearchActivity::class.java)
                                intent.putExtra(IntentConstants.EXTRA_Keywords, linkUri.getQueryParameter("query"))
                                context.startActivity(intent)
                            }
                            "category" -> {
                                if (thirdParam.isNullOrEmpty()) {
                                    /* store/category */
                                    val intent = Intent(context, BookCategoryListActivity::class.java)
                                    context.startActivity(intent)
                                } else {
                                    var fourParams = if (thirdParam.contains("?")) {
                                        thirdParam.split("?")[0]
                                    } else {
                                        thirdParam
                                    }
    
                                    /* store/category/{categoryId} */
                                    val intent = Intent(context, NewBookListActivity::class.java)
                                    intent.putExtra(IntentConstants.EXTRA_CategoryId, fourParams.toLong())
                                    context.startActivity(intent)
                                }
                            }
                            "column" -> {
                                /* store/column/{detailId} */
                                /* 显示栏目列表 */
                                if (!thirdParam.isNullOrEmpty()) {
                                    val detailId = thirdParam.toLong()
                                    val widgetDetailEntity = SDKSingleton.appBl.getWidgetDetailDataEntity(detailId)
                                    if (widgetDetailEntity != null) {
                                        val intent = Intent(context, BookSectionListActivity::class.java)
                                        intent.putExtra(
                                            IntentConstants.EXTRA_WidgetDetailEntityJson,
                                            JsonUtility.encodeToString(widgetDetailEntity)
                                        )
                                        context.startActivity(intent)
                                    } else {
                                        ToastUtil.showToastShort(R.string.column_has_offline)
                                    }
                                }
                            }
                            "featured" -> {
                                /* store/widgets/{containerId} */
                                if (!thirdParam.isNullOrEmpty()) {
                                    if (thirdParam.equals("free_books")) {
    
                                    } else {
                                        try {
                                            val containerId = thirdParam.toLong()

                                            val widgetContainerEntity = SDKSingleton.appBl.getWidgetCombineEntity(containerId)
                                            if (widgetContainerEntity != null && !widgetContainerEntity.detailEntityList.isNullOrEmpty()) {
                                                val detailEntity = widgetContainerEntity.detailEntityList?.get(0)
                                                val intent = Intent(context, RecommendBookListActivity::class.java)
                                                intent.putExtra(
                                                    IntentConstants.EXTRA_WidgetDetailEntityJson,
                                                    JsonUtility.encodeToString(detailEntity)
                                                )
                                                intent.putExtra(IntentConstants.EXTRA_CategoryName, widgetContainerEntity.containerTitle)
                                                context.startActivity(intent)
                                            } else {
                                                ToastUtil.showToastShort(R.string.column_has_offline)
                                            }
                                        } catch (e: Exception) {
                                            e.printStackTrace()
                                            ToastUtil.showToastShort(R.string.column_has_offline)
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                "shelf" -> {
                    HomeMainActivity.gotoHomeActivity(context, HomeTab.SHELF)
                }
                "mine" -> {
                    if (secondParam.isNullOrEmpty()) {
                        HomeMainActivity.gotoHomeActivity(context, HomeTab.MINE)
                    } else {
                        when (secondParam) {
                            "notes" -> {
                                val thirdParams = queue.poll()
                                val fourthParams = queue.poll()
                                when (thirdParams) {
                                    "books" -> {
                                        if (fourthParams.isNullOrEmpty()) {
                                            val intent = Intent(context, BookNoteCountActivity::class.java)
                                            context.startActivity(intent)
                                        } else {
                                            var noteCountEntity = SDKSingleton.dbWrapBl.getBookNoteCountEntity(fourthParams)
                                            val shelfBookEntity = SDKSingleton.userBl.getShelfBookItemEntityByResourceId(fourthParams)
                                            if (shelfBookEntity != null) {
                                                val downloadInfo = SDKSingleton.dbWrapBl.getResourceDownloadInfo(fourthParams)!!
                                                if ((downloadInfo.downloadStatus == DownloadStatus.COMPLETE || downloadInfo.downloadStatus == DownloadStatus.UPDATE) &&
                                                    File(downloadInfo.getActualFilePath()).exists()
                                                ) {//已购且下载了
                                                    gotoBookNoteDetail(noteCountEntity, context)
                                                } else {// 还没下载书籍
                                                    showNeedDownloadDialog(context, fourthParams, context.getString(R.string.please_download_first), onComplete = {
                                                        gotoBookNoteDetail(noteCountEntity, context)
                                                    })
                                                }
                                            }else {// 还没购买
                                                ToastUtil.showToastShort(R.string.please_buy_first)
                                            }
                                        }
                                    }
                                    "recycling" -> {
                                        if (fourthParams.equals("books")) {
                                            val fifthParam = queue.poll()
                                            if (!fifthParam.isNullOrEmpty()) {
                                                val shelfBookEntity = SDKSingleton.userBl.getShelfBookItemEntityByResourceId(fifthParam)
                                                if (shelfBookEntity != null) {
                                                    val downloadInfo = SDKSingleton.dbWrapBl.getResourceDownloadInfo(fifthParam)!!
                                                    if ((downloadInfo.downloadStatus == DownloadStatus.COMPLETE || downloadInfo.downloadStatus == DownloadStatus.UPDATE) &&
                                                        File(downloadInfo.getActualFilePath()).exists()
                                                    ) {//已购且下载了
                                                        gotoBookNoteCountActivity(fifthParam, context)
                                                    } else {// 还没下载书籍
                                                        showNeedDownloadDialog(context, fifthParam, context.getString(R.string.please_download_first), onComplete = {
                                                            gotoBookNoteCountActivity(fifthParam, context)
                                                        })
                                                    }
                                                } else {// 还没购买
                                                    ToastUtil.showToastShort(R.string.please_buy_first)
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            "bookmarks" -> {
                                var thirdParams = queue.poll()
                                when (thirdParams) {
                                    "books" -> {
                                        var fourthParams = queue.poll()
                                        if (!fourthParams.isNullOrEmpty()) {
                                            /* reader/{resourceId} */
                                            val shelfBookEntity = SDKSingleton.userBl.getShelfBookItemEntityByResourceId(fourthParams)
                                            if (shelfBookEntity != null) {
                                                val downloadInfo = SDKSingleton.dbWrapBl.getResourceDownloadInfo(fourthParams)!!
                                                if ((downloadInfo.downloadStatus == DownloadStatus.COMPLETE || downloadInfo.downloadStatus == DownloadStatus.UPDATE) &&
                                                    File(downloadInfo.getActualFilePath()).exists()
                                                ) {
                                                    BookReadActivity.gotoBookReadActivity(
                                                        context,
                                                        downloadInfo.getActualFilePath(),
                                                        downloadInfo.fileId,
                                                        linkParamArray[1],
                                                        jumpStep = openBookMark
                                                    )
                                                } else {
                                                    showNeedDownloadDialog(context, fourthParams) { info ->
                                                        BookReadActivity.gotoBookReadActivity(
                                                            context,
                                                            info.getActualFilePath(),
                                                            downloadInfo.fileId,
                                                            linkParamArray[1],
                                                            jumpStep = openBookMark
                                                        )
                                                    }
//                                                    ToastUtil.showToastShort("当前书籍还没有下载，请前往书架下载")
                                                }
                                            } else {
                                                ToastUtil.showToastShort(R.string.please_buy_first)
                                            }
                                        }
                                    }
                                    else -> {}
                                }
                            }
                            "favorites" -> {
                                val intent = Intent(context, FavoriteBookListActivity::class.java)
                                context.startActivity(intent)
                            }
                            "help" -> {
                                var fourthParam = queue.poll()
    
                                if (fourthParam.isNullOrEmpty()) {
                                    val intent = Intent(context, HelpCenterActivity::class.java)
                                    context.startActivity(intent)
                                } else {
                                    if (fourthParam.isDigitsOnly()) {
                                        FaqArticleDetailActivity.gotoFaqArticalDetailActivity(context, fourthParam.toLong())
                                    } else {
                                        ToastUtil.showToastShort(com.aquila.lib.widget.R.string.empty_no_search)
                                    }
                                }
                            }
                            "feedbacks" -> {
                                val thirdParams = queue.poll()
                                if (thirdParams.isNullOrEmpty()) {
                                    val intent = Intent(context, UserFeedbackListActivity::class.java)
                                    context.startActivity(intent)
    
                                } else {
                                    when (thirdParams) {
                                        "create" -> {
                                            EditFeedbackActivity.gotoEditFeedback(context)
                                        }
                                        else -> {
                                            try {
                                                FeedbackDetailActivity.gotoFeedbackDetailActivity(context, thirdParams.toLong())
                                            } catch (e: Exception) {
                                                e.printStackTrace()
                                                SDKSingleton.loggerBl.handleThrowable(e, funcName = getFunctionInfo())
                                            }
                                        }
                                    }
                                }
                            }
                            "messages" -> {
                                val intent = Intent(context, NotificationCenterActivity::class.java)
                                context.startActivity(intent)
                            }
                            "login" -> {
                                val intent = Intent(context, SSOLoginActivity::class.java)
                                context.startActivity(intent)
                            }
                            "purchased-books" -> {
                                val intent = Intent(context, PurchasedBookListActivity::class.java)
                                context.startActivity(intent)
                            }
                        }
                    }
                }
                "account" -> {
    
                }
                "aboutus" -> {
                    val intent = Intent(context, AboutActivity::class.java)
                    context.startActivity(intent)
                }
                "agreement" -> {
                    WrapCoroutineHelper.getBookAgreementURL {
                        CommWebViewActivity.gotoWebView(context, it, titleName = findString(R.string.title_agreement))
                    }
                }
                "privacy-policy" -> {
                    WrapCoroutineHelper.getBookPrivacyURL {
                        CommWebViewActivity.gotoWebView(context, it, titleName = findString(R.string.title_privacy))
                    }
                }
                "activity" -> {
                    if (!secondParam.isNullOrEmpty() && secondParam.isDigitsOnly()) {
                        if (!NetWorkUtils.isNetworkAvailable()) {
                            ToastUtil.showToastLong(R.string.no_network_connect)
                            return true
                        }
                        ProductActiveActivity.gotoActivityDetailUI(context, secondParam.toLong())
                    } else {
                        ToastUtil.showToastShort(R.string.activity_msg_not_found)
                    }
                }
            }
        }
        return true
    }

    private fun gotoBookNoteDetail(
        noteCountEntity: BookNoteCountEntity?,
        context: Context
    ) {
        if (noteCountEntity != null) {
            BookNoteDetailActivity.gotoBookNoteDetailActivity(context, noteCountEntity)
        } else {
            ToastUtil.showToastShort(R.string.no_notes)
        }
    }

    private fun gotoBookNoteCountActivity(fifthParam: String, context: Context) {
        var bookNoteCountEntity = SDKSingleton.userBl.getBookNoteCountEntity(fifthParam)
        if (bookNoteCountEntity != null) {
            BookNoteDetailActivity.gotoBookNoteDetailActivity(
                context,
                bookNoteCountEntity,
                1
            )
        } else {
            ToastUtil.showToastShort(R.string.no_notes)
        }
    }
    
    
    fun showNeedDownloadDialog(
        context: Context,
        resourceId: String,
        msg: String = findString(R.string.please_download_before_read),
        onComplete: (info: ResourceDownloadInfo) -> Unit
    ) {
        CommAlertDialog.with(context)
            .setTitle(R.string.warm_prompt_title).setMessage(msg)
            .setStartText(R.string.label_cancel).setEndText(R.string.download_this_book)
            .setOnViewClickListener { _, _, tag ->
                if (tag == CommAlertDialog.TAG_CLICK_END) {
//                    showBookDownloadingDialog()
                    BookDownloadActivity.gotoDownloadBook(
                        context, resourceId,
                        object : OnBookDownloadCallback {
                            override fun onDownloadComplete(info: ResourceDownloadInfo) {
                                onComplete(info)
                                
                            }
                        }
                    )
                }
            }.showDialog()
    }
    
    fun openUrl(context: Context, paramsMap: HashMap<String, String>, url: String) {
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            var jumpUrl = if (paramsMap["openWithToken"].equals("1")) {
                var nonce = SDKSingleton.appBl.getTokenNonce()
                val map = HashMap<String, String>()
                if (!nonce.isNullOrEmpty()) {
                    map["nonce"] = nonce
                }
                APPUtil.formatUrl(url, map)
            } else {
                url
            }
            // 如果需要登录，则带上nonce
            KLog.d("jumpUrl = $jumpUrl")
            if (paramsMap["openWithBrowser"] == "1") { // 1 表示需要打开网页
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse(jumpUrl.toString()))
                context.startActivity(intent)
            } else {
                CommWebViewActivity.gotoWebView(context, jumpUrl.toString(), false, "")
            }
        }
    }
}
