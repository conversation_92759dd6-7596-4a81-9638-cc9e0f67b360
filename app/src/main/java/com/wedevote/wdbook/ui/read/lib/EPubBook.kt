package com.wedevote.wdbook.ui.read.lib

import android.graphics.Typeface
import android.util.Xml
import com.aquila.lib.tools.singleton.SPSingleton
import com.wedevote.wdbook.base.APP
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.HighlightColorType
import com.wedevote.wdbook.entity.BookCatalogEntity
import com.wedevote.wdbook.entity.NoteEntity
import com.wedevote.wdbook.entity.search.SearchResultChapter
import com.wedevote.wdbook.entity.search.SearchResultSentence
import com.wedevote.wdbook.tools.BookDecode
import com.wedevote.wdbook.tools.define.TextFontType
import com.wedevote.wdbook.tools.util.GsonUtil
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.tools.util.getFunctionInfo
import com.wedevote.wdbook.tools.util.toJsonStr
import com.wedevote.wdbook.ui.read.lib.data.BookReference
import com.wedevote.wdbook.ui.read.lib.data.BookReference.ChapterInfo
import com.wedevote.wdbook.ui.read.lib.data.TocItem
import org.xmlpull.v1.XmlPullParser
import java.io.ByteArrayInputStream
import java.io.InputStream
import kotlin.collections.ArrayList
import kotlin.collections.HashSet

object EPubBook {
    var bookTitle = ""
        private set
    private var publisher: String? = null
    private var language: String? = null
    private var description: String? = null
    var pathList = ArrayList<String>()
    const val preload_book_name = "nidoottae_cclm_20"
    var originalPathList = ArrayList<String>()
    val tocItemList = ArrayList<TocItem>()
    var contentsDialogData_temp: ArrayList<BookCatalogEntity> = ArrayList()
    val referenceList = ArrayList<BookReference>()
    var pageNumberList = ArrayList<Int>()
    var pageInfoIndex = 0
    var isOpenBook = false

    var typefaceSerif = Typeface.createFromAsset(APP.get().assets, "fonts/SourceHanSerifCN-Regular.otf")
    var textFont: Int = TextFontType.SERIF.value

    private val SHOW_PAGE_START_INDEX = 256
    private val SERIF_FONT_START_INDEX = 512
    private var bookPath = ""
    private val rootPath = ""

    private const val bibleLinkPrefix = "wdbible://bible/"
    const val bigBookSize = 2000

    /**
     * 将HTML实体形式的RTL控制字符替换为Unicode控制字符
     * @param text 包含HTML实体的文本
     * @return 替换后的文本
     */
    fun replaceRTLEntities(text: String): String {
        return text.replace("&#x202A;", "\u202A")
            .replace("&#x202B;", "\u202B")
            .replace("&#x202C;", "\u202C")
    }

    /**
     * 移除文本中的RTL控制字符
     * @param text 包含RTL控制字符的文本
     * @return 移除RTL控制字符后的文本
     */
    fun removeRTLControlChars(text: String): String {
        return text.replace("\u202A", "")
            .replace("\u202B", "")
            .replace("\u202C", "")
    }

    /**
     * 规范化文本以便比较，去除RTL控制字符周围的空格
     * @param text 需要规范化的文本
     * @return 规范化后的文本
     */
    fun normalizeTextForComparison(text: String): String {
        var normalized = text.replace(" \u202A", "\u202A")
            .replace("\u202A ", "\u202A")
            .replace(" \u202B", "\u202B")
            .replace("\u202B ", "\u202B")
            .replace(" \u202C", "\u202C")
            .replace("\u202C ", "\u202C")

        normalized = normalized.replace("\\s+".toRegex(), "")

        return normalized
    }

    /**
     * 查找句子在文本中的位置，同时处理RTL控制字符周围的空格问题
     * @param pageText 要在其中查找的页面文本
     * @param sentence 要查找的句子
     * @return 句子在文本中的起始位置，如果找不到则返回-1
     */
    fun findSentenceOffsetWithRTLSpaces(pageText: String, sentence: String): Int {
        if (pageText.contains(sentence)) {
            return pageText.indexOf(sentence)
        }
        val normalizedOriginalSentence = EPubBook.normalizeTextForComparison(sentence)
        if (pageText.contains(normalizedOriginalSentence)) {
            return pageText.indexOf(normalizedOriginalSentence)
        }

        try {
            // 尝试构造忽略RTL控制字符前后空格的正则表达式
            val rtlEscapeA = Regex.escape("\u202A")
            val rtlEscapeB = Regex.escape("\u202B")
            val rtlEscapeC = Regex.escape("\u202C")

            // 先保护正则表达式中特殊字符
            val escapedSentence = Regex.escape(sentence)

            // 创建允许RTL控制字符前后有空格的模式
            val pattern = escapedSentence
                .replace(rtlEscapeA, "\\s$rtlEscapeA\\s")
                .replace(rtlEscapeB, "\\s$rtlEscapeB\\s")
                .replace(rtlEscapeC, "\\s$rtlEscapeC\\s")
                .replace("\\s+".toRegex(), "\\\\s")

            // 查找第一个匹配位置
            val regex = Regex(pattern)
            val matchResult = regex.find(pageText)

            if (matchResult != null) {
                return matchResult.range.first
            }

            // 如果上面的方法都失败，尝试规范化后的文本比较
            val normalizedSentence = normalizeTextForComparison(sentence)
            val normalizedPageText = normalizeTextForComparison(pageText)

            // 在规范化文本中找到位置
            val normalizedIndex = normalizedPageText.indexOf(normalizedSentence)
            if (normalizedIndex != -1) {
                var originalIndex = -1
                var nonWhitespaceCount = 0
                for (i in pageText.indices) {
                    // 仅计算非空白字符
                    if (!pageText[i].isWhitespace()) {
                        // 当非空白字符计数等于规范化后的索引时，我们可能找到了起点
                        if (nonWhitespaceCount == normalizedIndex) {
                            // 验证这个起点：从此位置提取子串并规范化，看是否匹配
                            val potentialMatchLength = sentence.length + (sentence.length / 2) // 考虑空格，增加缓冲区
                            val endIndex = minOf(i + potentialMatchLength, pageText.length)
                            if (endIndex > i) { // 确保 endIndex 有效
                                val potentialMatchSubstring = pageText.substring(i, endIndex)
                                if (normalizeTextForComparison(potentialMatchSubstring).startsWith(normalizedSentence)) {
                                    originalIndex = i
                                    break // 找到第一个正确的匹配，退出循环
                                }
                            }
                        }
                        nonWhitespaceCount++
                    }
                     // 优化：如果原始文本指针已经远超理论上的匹配结束位置，可以提前退出
                    if(nonWhitespaceCount > normalizedIndex + normalizedSentence.length + 10) { // 增加一些容错空间
                        break
                    }
                }
                if (originalIndex != -1) {
                    return originalIndex
                }
            }
        } catch (e: Exception) {
            return -1
        }
        return -1
    }

    /**
     * 辅助方法：找到与目标字符串最接近的前缀
     */
    private fun String.findAnyPrefix(target: String): String {
        // 从最长的可能前缀开始尝试
        val maxLength = minOf(this.length, target.length + 10) // 允许一些额外字符
        for (i in maxLength downTo 0) {
            val prefix = this.substring(0, i)
            val normalized = normalizeTextForComparison(prefix)
            if (target.startsWith(normalized)) {
                return prefix
            }
        }
        // 默认情况下返回空字符串
        return ""
    }

    fun hasReference(): Boolean {
        return referenceList.isNotEmpty()
    }

    fun getReferenceChapters(bookIndex: Int): ArrayList<ChapterInfo> {
        return referenceList[bookIndex].chapters
    }

    fun getReferenceVerses(bookIndex: Int, chapterIndex: Int): ArrayList<String> {
        return referenceList[bookIndex].chapters[chapterIndex].verses
    }

    fun getReferencePath(bookIndex: Int, chapterIndex: Int, verseIndex: Int): String {
        return referenceList[bookIndex].chapters[chapterIndex].paths[verseIndex]
    }

    fun getTocTitle(pathIndex: Int): String {
        if (pathIndex < 0 || pathIndex >= pathList.size) {
            return ""
        }
        return getTocTitle(pathList[pathIndex])
    }

    fun getTocTitle(path: String?): String {
        for (t in tocItemList) {
            if (t.path == path) {
                return t.title
            }
        }
        return ""
    }

    fun checkPathList() { // 删除无效的path
        val newPathList = ArrayList<String>(pathList.size) // 预设初始容量
        val tocItemPaths = HashSet<String>(tocItemList.size) // 使用HashSet存储tocItemList中的路径
        for (t in tocItemList) {
            tocItemPaths.add(t.path)
        }
        for (path in pathList) {
            if (tocItemPaths.contains(path)) {
                newPathList.add(path)
            }
            originalPathList.add(path)
        }
        pathList = newPathList
    }

    fun getOriginalIndex(path: String): Int {
        for (i in 0..originalPathList.size) {
            if (originalPathList[i] == path) {
                return i
            }
        }
        return 0
    }

    fun getPathIndex(path: String): Int {
        for (i in pathList.indices) {
            if (pathList[i] == path) {
                return i
            }
        }
        // 无法精确匹配则匹配末尾
        for (i in pathList.indices) {
            if (i < pathList.size && pathList[i] != null) {
                if (pathList[i].endsWith(path)) {
                    return i
                }
            }
        }
        return 0
    }

    fun getCurrentPageInfoIndex(): Int {
        val showPageNumber = SPSingleton.get().getBoolean(SPKeyDefine.SP_isShowPage, false)
        var pageInfoIndex = BookParameters.textLevel
        val textFont = SPSingleton.get().getInt(SPKeyDefine.SP_BookTextFont, TextFontType.SERIF.value)
        if (showPageNumber) {
            pageInfoIndex += SHOW_PAGE_START_INDEX
        }
        pageInfoIndex += (textFont * SERIF_FONT_START_INDEX)
        return pageInfoIndex
    }

    fun getPageString(index: Int): String {
        return if (pageNumberList.isNullOrEmpty() || getCurrentPageInfoIndex() != pageInfoIndex) {
            if (pathList.size > bigBookSize) {
                ""
            } else {
                "..."
            }
        } else if (index == 0) {
            "1"
        } else {
            (pageNumberList[index - 1] + 1).toString()
        }
    }

    fun setPageInfo(index: Int, pageList: ArrayList<Int>) {
        pageNumberList = pageList
        pageInfoIndex = index
    }

    fun openBook(path: String, key: String?, user: String?, bookFileId: String): Boolean {
        isOpenBook = true
        synchronized(this) {
            bookPath = path
            pathList.clear()
            tocItemList.clear()
            originalPathList.clear()
            BookDecode.initBook(path, key!!, user!!)
            val content = BookDecode.getFileText("package.opf")
            if (content.length > 32) {
                pathList.addAll(parseOpfFile(content, bookFileId))
            } else {
                return false
            }
            val content2 = BookDecode.getFileText("toc.ncx")
            if (content2.length > 32) {
                tocItemList.addAll(parseTocFile(content2))
            } else {
                return false
            }

            val content3 = BookDecode.getBookText("META-INF/anchors.xml")
            if (!content3.isEmpty()) {
                parseReferenceFile(content3)
            }
            checkPathList()
            val keyCheck = BookDecode.getBookText("META-INF/encryption")
            if (keyCheck.isNotEmpty() && keyCheck != "resurrection") {
                return false
            }
            return true
        }
    }

    private fun parseOpfFile(inputString: String, bookFileId: String): ArrayList<String> {
        synchronized(this) {
            var pathList = ArrayList<String>()
            try {
                val bookXmlData = SDKSingleton.dbWrapBl.getBookXmlData(bookFileId)
                if (!bookXmlData.isNullOrEmpty()) {
                    pathList = GsonUtil.parseJsonToObject(bookXmlData)
                    return pathList
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
            val inputStream: InputStream = ByteArrayInputStream(inputString.toByteArray())
            val parser = Xml.newPullParser()
            try {
                var text = ""
                val fileList = ArrayList<FileItem>()
                var manifest = false
                var spine = false
                parser.setFeature(XmlPullParser.FEATURE_PROCESS_NAMESPACES, false)
                parser.setInput(inputStream, null)
                var event = parser.eventType
                while (event != XmlPullParser.END_DOCUMENT) {
                    if (event == XmlPullParser.START_TAG) {
                        if (parser.name == "manifest") {
                            manifest = true
                        }
                        if (parser.name == "spine") {
                            spine = true
                        } else if (manifest && parser.name == "item") {
                            val href = parser.getAttributeValue(null, "href")
                            val id = parser.getAttributeValue(null, "id")
                            if (href != null) {
                                fileList.add(FileItem(id, href))
                            }
                        } else if (spine && parser.name == "itemref") {
                            val id = parser.getAttributeValue(null, "idref")
                            if (id != null) {
                                for (f in fileList) {
                                    if (f.id == id) {
                                        pathList.add(f.path)
                                    }
                                }
                            }
                        }
                    } else if (event == XmlPullParser.END_TAG) {
                        if (parser.name == "dc:title") {
                            bookTitle = text
                        } else if (parser.name == "dc:publisher") {
                            publisher = text
                        } else if (parser.name == "dc:publisher") {
                            publisher = text
                        } else if (parser.name == "dc:language") {
                            language = text
                        } else if (parser.name == "dc:description") {
                            description = text
                        } else if (parser.name == "manifest") {
                            manifest = false
                        }
                        if (parser.name == "spine") {
                            spine = false
                        }
                    } else if (event == XmlPullParser.TEXT) {
                        text = parser.text
                    }
                    event = parser.next()
                }

                SDKSingleton.dbWrapBl.saveBookXmlData(bookFileId, pathList.toJsonStr())
            } catch (e: Exception) {
                e.printStackTrace()
                SDKSingleton.loggerBl.handleThrowable(e, getFunctionInfo())
            }
            return pathList
        }
    }

    private fun addTocItem(content: String, path: String, level: Int) {
        var content = content
        val tagStart = path.lastIndexOf('#')
        content = content.replace("\n", "")
        if (tagStart > 0) {
            tocItemList.add(TocItem(content, path.substring(0, tagStart), path.substring(tagStart + 1), level))
        } else {
            tocItemList.add(TocItem(content, path, "", level))
        }
    }

    private fun addChapter(r: BookReference, addrs1: List<String>, addrs2: List<String>?, path: String) {
        val chapterStart = addrs1[1].toInt()
        val verseStart = if (addrs1.size == 3) {
            addrs1[2].toInt()
        } else {
            1
        }

        if (addrs2 == null) {
            r.addChapter(chapterStart, verseStart, verseStart, path)
        } else {
            val chapterEnd = addrs2[1].toInt()
            val verseEnd = if (addrs2.size == 3) {
                addrs2[2].toInt()
            } else {
                SDKSingleton.bibleBl.getChapterVerseCount(r.bookId, chapterEnd)
            }
            for (i in chapterStart..chapterEnd) {
                var s = 0
                var e = 0
                s = if (i == chapterStart) {
                    verseStart
                } else {
                    1
                }

                e = if (i == chapterEnd) {
                    verseEnd
                } else {
                    SDKSingleton.bibleBl.getChapterVerseCount(r.bookId, i)
                }
                r.addChapter(i, s, e, path)
            }
        }
    }

    private fun addReference(path: String, link: String) {
        if (link.startsWith(bibleLinkPrefix)) {
            val links = link.substring(bibleLinkPrefix.length).split("-")
            val addrs1 = links[0].split(".")
            var addrs2: List<String>? = null
            if (links.size == 2) {
                addrs2 = links[1].split(".")
            }

            var find = false
            for (r in referenceList) {
                if (r.bookId == addrs1[0]) {
                    addChapter(r, addrs1, addrs2, path)
                    find = true
                    break
                }
            }

            if (!find) {
                val reference = BookReference()
                reference.bookId = addrs1[0]
                addChapter(reference, addrs1, addrs2, path)
                referenceList.add(reference)
            }
        }
    }

    private fun parseReferenceFile(inputString: String) {
        val inputStream: InputStream = ByteArrayInputStream(inputString.toByteArray())
        val parser = Xml.newPullParser()
        var link: String = ""
        referenceList.clear()
        try {
            parser.setFeature(XmlPullParser.FEATURE_PROCESS_NAMESPACES, false)
            parser.setInput(inputStream, null)
            var event = parser.eventType
            while (event != XmlPullParser.END_DOCUMENT) {
                if (event == XmlPullParser.START_TAG) {
                    if (parser.name == "reference") {
                        val address = parser.getAttributeValue(null, "href")
                        link = parser.getAttributeValue(null, "reference")
                        if (address != null && link != null) {
                            addReference(address, link)
                        }
                    }
                }
                event = parser.next()
            }
        } catch (e: Exception) {
            // Log.e("wd", "parse reference file failed,link=$link")
            e.printStackTrace()
            SDKSingleton.loggerBl.handleThrowable(e, getFunctionInfo())
        }
    }

    fun parseTocFile(inputString: String): ArrayList<TocItem> {
        val inputStream: InputStream = ByteArrayInputStream(inputString.toByteArray())
        val parser = Xml.newPullParser()
        val tocItemList = ArrayList<TocItem>()
        var level = 0
        try {
            var text = ""
            var labelStart = false
            parser.setFeature(XmlPullParser.FEATURE_PROCESS_NAMESPACES, false)
            parser.setInput(inputStream, null)
            var event = parser.eventType
            while (event != XmlPullParser.END_DOCUMENT) {
                if (event == XmlPullParser.START_TAG) {
                    if (parser.name == "navPoint") {
                        level++
                    } else if (parser.name == "navLabel") {
                        labelStart = true
                    } else if (parser.name == "content") {
                        val src = parser.getAttributeValue(null, "src")
                        src?.let {
                            var content = text
                            val tagStart = it.lastIndexOf('#')
                            content = content.replace("\n", "")
                            if (tagStart > 0) {
                                tocItemList.add(TocItem(content, it.substring(0, tagStart), it.substring(tagStart + 1), level))
                            } else {
                                tocItemList.add(TocItem(content, it, "", level))
                            }
                        }
                        text = ""
                    }
                } else if (event == XmlPullParser.END_TAG) {
                    if (parser.name == "navPoint") {
                        level--
                    } else if (parser.name == "navLabel") {
                        labelStart = false
                    }
                } else if (event == XmlPullParser.TEXT && labelStart) {
                    text += parser.text
                }
                event = parser.next()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            SDKSingleton.loggerBl.handleThrowable(e, getFunctionInfo())
        }
        return tocItemList
    }

    fun getText(position: Int): String {
        synchronized(this) {
            try {
                var fileFullPath = ""
                if (position < 0 || position >= pathList.size) {
                    return ""
                } else {
                    fileFullPath = rootPath + pathList[position]
                    return BookDecode.getBookText(fileFullPath)
                }
            } catch (e: Exception) {
                e.printStackTrace()
                return ""
            }
        }
    }

    fun isCopyrightPage(position: Int): Boolean {
        val title = getTocTitle(position)
        return position <= 1 && (title.lowercase().contains("copyright") || title.contains("版权") || title.contains("版權"))
    }

    private fun addSpineItem(fileList: ArrayList<FileItem>, id: String?) {
        if (id != null) {
            for (f in fileList) {
                if (f.id == id) {
                    pathList.add(f.path)
                }
            }
        }
    }

    fun calculateHasPrevious(currentPosition: Int, dataList: List<Any>?): Boolean {
        if (currentPosition <= 0 || dataList == null) {
            return false
        }

        // 向前查找有效的句子
        for (i in currentPosition - 1 downTo 0) {
            val item = dataList[i]
            // 如果上一条是章节标题，需要检查再上一条是否有句子
            if (item is SearchResultChapter) {
                if (i == 0) {
                    return false
                }
                continue
            }
            if (item is SearchResultSentence) {
                return true
            }
        }

        return false
    }

    fun calculateHasNext(currentPosition: Int, dataList: List<Any>?, itemCount: Int, canLoadMore: Boolean = false): Boolean {
        if (dataList == null) {
            return false
        }

        // 如果还可以加载更多，则认为有下一条
        if (canLoadMore) {
            return true
        }

        // 如果是最后一项，且不能加载更多，则没有下一条
        if (currentPosition >= itemCount - 1) {
            return false
        }

        // 向后查找有效的句子
        for (i in currentPosition + 1 until itemCount) {
            if (dataList[i] is SearchResultSentence) {
                return true
            }
        }

        return false
    }

    /**
     * 从搜索结果创建 NoteEntity
     * @param chapter 章节信息
     * @param sentence 句子信息
     * @param selectedPosition 当前选中的位置
     * @param dataList 数据列表
     * @param itemCount 数据总数
     * @param canLoadMore 是否可以加载更多
     * @param searchKeywords 搜索关键词，用于在没有高亮标记时创建高亮
     * @return 创建的 NoteEntity 对象
     */
    fun createNoteEntityFromSearchResult(
        chapter: SearchResultChapter,
        sentence: SearchResultSentence,
        selectedPosition: Int = -1,
        dataList: List<Any>? = null,
        itemCount: Int = 0,
        canLoadMore: Boolean = false,
        searchKeywords: String = ""
    ): NoteEntity {
        val hasPrevious = if (selectedPosition >= 0 && dataList != null)
            calculateHasPrevious(selectedPosition, dataList) else false
        val hasNext = if (selectedPosition >= 0 && dataList != null)
            calculateHasNext(selectedPosition, dataList, itemCount, canLoadMore) else false

        return NoteEntity().apply {
            this.resourceId = chapter.resourceId
            this.pagePath = "EPUB/${chapter.fileId}.xhtml"
            this.wordStartOffset = sentence.startOffset
            this.wordEndOffset = sentence.endOffset
            this.summary = sentence.content
            this.markStyle = NoteEntity.STYLE_HIGHLIGHT
            this.highlightColorType = HighlightColorType.COLOR_ORANGE
            this.isSearchHighlight = true
            this.fileName = chapter.fileName
            val contentWithTag = sentence.contentWithTag

            // 先尝试定位整个句子，然后再定位高亮部分
            if (contentWithTag.contains("{{{") && contentWithTag.contains("}}}")) {
                // 已经包含高亮标记，直接使用
                this.noteText = "[[[${sentence.content}]]]$contentWithTag"
            } else if (searchKeywords.isNotEmpty() && sentence.content.contains(searchKeywords, ignoreCase = true)) {
                // 使用关键词创建高亮
                val highlightedContent = sentence.content.replace(
                    searchKeywords,
                    "{{{$searchKeywords}}}",
                    ignoreCase = true
                )
                this.noteText = "[[[${sentence.content}]]]$highlightedContent"
            } else {
                // 无法确定高亮位置，使用整个句子作为高亮
                this.noteText = "[[[${sentence.content}]]]{{{${sentence.content}}}}"
            }

            // 存储导航按钮状态信息
            if (selectedPosition >= 0) {
                this.extraData = "{\"hasPrevious\":$hasPrevious,\"hasNext\":$hasNext}"
            }
        }
    }

    /**
     * 检查文本是否包含RTL控制字符
     * @param text 要检查的文本
     * @return 是否包含RTL控制字符
     */
    fun containsRTLControls(text: String): Boolean {
        return text.contains("\u202A") || text.contains("\u202B") || text.contains("\u202C")
    }

    /**
     * 检查字符是否为希伯来文字符
     * @param char 要检查的字符
     * @return 是否为希伯来文字符
     */
    fun isHebrewChar(char: Char): Boolean {
        // 希伯来文Unicode范围: 0x0590-0x05FF
        return char.toInt() in 0x0590..0x05FF
    }

    /**
     * 检查文本是否包含希伯来文字符
     * @param text 要检查的文本
     * @return 是否包含希伯来文
     */
    fun containsHebrew(text: String): Boolean {
        if (text.isEmpty()) return false
        return text.any { isHebrewChar(it) }
    }

    /**
     * 检查文本是否以希伯来文开始
     * @param text 要检查的文本
     * @return 是否以希伯来文开始
     */
    fun startsWithHebrew(text: String): Boolean {
        if (text.isEmpty()) return false

        // 跳过空格和控制字符
        var i = 0
        while (i < text.length && (text[i].isWhitespace() || text[i] == '\u202A' || text[i] == '\u202B' || text[i] == '\u202C')) {
            i++
        }

        // 如果全是空格或已到末尾，返回false
        if (i >= text.length) return false

        return isHebrewChar(text[i])
    }

    /**
     * 检查文本是否以希伯来文结束
     * @param text 要检查的文本
     * @return 是否以希伯来文结束
     */
    fun endsWithHebrew(text: String): Boolean {
        if (text.isEmpty()) return false

        // 从末尾开始，跳过空格和控制字符
        var i = text.length - 1
        while (i >= 0 && (text[i].isWhitespace() || text[i] == '\u202A' || text[i] == '\u202B' || text[i] == '\u202C')) {
            i--
        }

        // 如果全是空格或已到开头，返回false
        if (i < 0) return false

        return isHebrewChar(text[i])
    }

    internal class FileItem(var id: String, var path: String)
}
