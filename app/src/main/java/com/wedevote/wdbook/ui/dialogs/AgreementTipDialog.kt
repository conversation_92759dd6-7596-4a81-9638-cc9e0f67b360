package com.wedevote.wdbook.ui.dialogs

import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.widget.Button
import android.widget.TextView
import com.aquila.lib.base.OnViewClickListener
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.base.WrapCoroutineHelper
import com.wedevote.wdbook.tools.util.buildSpannableString
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.ui.CommWebViewActivity

/***
 *@date 创建时间  2023.4.17
 *<AUTHOR> <PERSON><PERSON>
 *@description
 */
class AgreementTipDialog(context: Context) : BaseDialog(context), View.OnClickListener {
    private lateinit var okButton: Button
    private lateinit var cancelButton: Button
    private lateinit var protocolTextView: TextView
    var onViewClickListener: OnViewClickListener? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_agreement_tip_layout)
        cancelButton = findViewById(R.id.tip_content_cancel_Button)
        okButton = findViewById(R.id.tip_content_OK_Button)
        protocolTextView = findViewById(R.id.register_use_protocol_TextView)

        okButton.setOnClickListener(this)
        cancelButton.setOnClickListener(this)
        protocolTextView.setOnClickListener(this)

        configDialog(Gravity.BOTTOM)
        setCancelable(false)
        setCanceledOnTouchOutside(false)

        protocolTextView.buildSpannableString {
            addText(context.getString(R.string.please_first_read_and_agree))
            addText(context.getString(R.string.text_user_agreement)){
                setColor("#006FFF")
                onClick(false) {
                    if (!APPConfig.isFastClick()) {
                        WrapCoroutineHelper.getBookAgreementURL { url ->
                            CommWebViewActivity.gotoWebView(
                                context,
                                url,
                                titleName = findString(R.string.title_agreement)
                            )
                        }
                    }
                }
            }
            addText("&")
            addText(context.getString(R.string.text_privacy_agreement)){
                setColor("#006FFF")
                onClick(false) {
                    if (!APPConfig.isFastClick()) {
                        WrapCoroutineHelper.getBookPrivacyURL { url ->
                            CommWebViewActivity.gotoWebView(
                                context,
                                url,
                                titleName = findString(R.string.title_privacy)
                            )
                        }
                    }
                }
            }
        }
        protocolTextView.highlightColor = context.resources.getColor(R.color.transparent)
    }

    override fun onClick(v: View?) {
        when (v) {
            cancelButton -> {
                dismiss()
            }
            okButton -> {
                onViewClickListener?.onClickAction(v, "", "")
                dismiss()
            }
        }
    }
}