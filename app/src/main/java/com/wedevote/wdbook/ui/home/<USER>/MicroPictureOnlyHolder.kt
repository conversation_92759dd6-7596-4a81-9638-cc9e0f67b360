package com.wedevote.wdbook.ui.home.microwidget

import android.view.ViewGroup
import com.aquila.lib.base.BaseViewHolder
import com.wedevote.wdbook.tools.util.ImageLoadUtil
import com.aquila.lib.widget.view.AdaptiveImageView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.entity.home.WidgetContainerCombineEntity
import com.wedevote.wdbook.tools.util.PictureUtil
import com.wedevote.wdbook.tools.util.dp2px

/***
 * @date 创建时间 2020/4/13 17:50
 * <AUTHOR> W.<PERSON>Long
 * @description
 */
class MicroPictureOnlyHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.micro_holder_picture_only_layout) {
    val pictureImageView: AdaptiveImageView = itemView.findViewById(R.id.micro_holder_picture_ImageView)

    override fun <T> initUIData(t: T) {
        t as WidgetContainerCombineEntity
        if (!t.detailEntityList.isNullOrEmpty()) {
            PictureUtil.loadImage(pictureImageView, t.detailEntityList!![0].imgPath, dp2px(5))
        }
    }
}
