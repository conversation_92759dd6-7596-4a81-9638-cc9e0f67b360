package com.wedevote.wdbook.ui.home.microwidget

import android.view.ViewGroup
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.wedevote.wdbook.entity.home.WidgetContainerCombineEntity

/***
 * @date 创建时间 2020/4/13 15:33
 * <AUTHOR> <PERSON><PERSON>
 * @description 微件系统的adapter
 */
open class HomeMicroCommAdapter : BaseRecycleAdapter<WidgetContainerCombineEntity, BaseViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder {
        return MicroWidgetViewFactory.createHolderByType(parent, viewType)
    }

    override fun getItemViewType(position: Int): Int {
        return MicroWidgetViewFactory.getViewType(dataList!![position].containerKey)
    }
}
