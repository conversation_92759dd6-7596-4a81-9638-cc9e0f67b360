package com.wedevote.wdbook.ui.store.search

import android.text.Html
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.wedevote.wdbook.tools.util.ImageLoadUtil
import com.aquila.lib.widget.view.AdaptiveImageView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.entity.store.ProductEntity
import com.wedevote.wdbook.tools.util.PictureUtil
import com.wedevote.wdbook.tools.util.initAuthorsName
import com.wedevote.wdbook.tools.util.parseHtmlTag
import com.wedevote.wdbook.ui.store.BookDetailActivity

/***
 * @date 创建时间 2022/1/19 14:11
 * <AUTHOR> W<PERSON>
 * @description
 */
class SearchResultAdapter : BaseRecycleAdapter<ProductEntity, SearchResultViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SearchResultViewHolder {
        return SearchResultViewHolder(parent)
    }
}

class SearchResultViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.view_holder_search_result_layout) {
    val coverImageView: AdaptiveImageView = itemView.findViewById(R.id.search_result_cover_ImageView)
    val nameTextView: TextView = itemView.findViewById(R.id.search_result_name_TextView)
    val authorTextView: TextView = itemView.findViewById(R.id.search_result_author_TextView)
    val descTextView: TextView = itemView.findViewById(R.id.search_result_desc_TextView)
    val categoryTextView: TextView = itemView.findViewById(R.id.search_result_category_TextView)
    val purchasedImageView: ImageView = itemView.findViewById(R.id.search_result_purchased_ImageView)

    override fun <T> initUIData(t: T) {
        val productEntity = t as ProductEntity
        PictureUtil.loadImage(coverImageView, t.cover)
        nameTextView.text = Html.fromHtml(parseHtmlTag(t.title))
        authorTextView.text = Html.fromHtml(parseHtmlTag(initAuthorsName(t.authorList)))

        descTextView.text = Html.fromHtml(parseHtmlTag(productEntity.desc))
        categoryTextView.text = Html.fromHtml(initPublishInfo(productEntity))

        purchasedImageView.visibility = if (productEntity.purchased == 1) {
            View.VISIBLE
        } else {
            View.GONE
        }

        itemView.setOnClickListener { v ->
            BookDetailActivity.gotoBookDetail(v.context, productEntity.productId)
        }
    }

    fun initPublishInfo(entity: ProductEntity): String {
        var name = ""
        if (!entity.categoriesList.isNullOrEmpty()) {
            name = entity.categoriesList?.last()?.name ?: ""
        }
        var publishName = ""
        if (!entity.publisherEntity?.name.isNullOrEmpty()) {
            publishName = "| ${entity.publisherEntity?.name}"
        }
        return "$name $publishName".replace("<em>", "<font color = #FF8A00>").replace("</em>", "</font>")
    }
}
