package com.wedevote.wdbook.ui.home.microwidget

import android.view.ViewGroup
import android.widget.ImageView
import com.aquila.lib.base.BaseViewHolder
import com.wedevote.wdbook.R
import com.wedevote.wdbook.constants.AnalyticsConstants
import com.wedevote.wdbook.constants.AnalyticsConstants.LOG_V1_PARAM_WIDGET_ID
import com.wedevote.wdbook.entity.home.NewWidgetDetailEntity
import com.wedevote.wdbook.entity.home.WidgetContainerCombineEntity
import com.wedevote.wdbook.tools.util.AnalyticsUtils.logEvent
import com.wedevote.wdbook.tools.util.PictureUtil
import com.youth.banner.Banner
import com.youth.banner.adapter.BannerAdapter
import com.youth.banner.indicator.CircleIndicator

/***
 * @date 创建时间 2020/9/4 11:50
 * <AUTHOR> W.YuLong
 * @description 首页的Banner微件
 */
class MicroBannerViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.micro_holder_banner_layout) {

    val bannerLayout: Banner<NewWidgetDetailEntity, BannerDataAdapter> = itemView.findViewById(R.id.micro_holder_banner_layout)

    var bannerAdapter = BannerDataAdapter()

    init {
        bannerLayout.setLoopTime(5000)
        bannerLayout.setAdapter(bannerAdapter).indicator = CircleIndicator(itemView.context)
    }

    override fun <T> initUIData(t: T) {
        t as WidgetContainerCombineEntity
        loadData(t)
    }

    private fun loadData(bean: WidgetContainerCombineEntity) {
        bannerLayout.setDatas(bean.detailEntityList)
    }
}

/***
 *@date 创建时间 2020/9/4 14:31
 *<AUTHOR> W.YuLong
 *@description
 */
class BannerDataAdapter : BannerAdapter<NewWidgetDetailEntity, BannerDataAdapter.BannerDataViewHolder>(null) {
    override fun onCreateHolder(parent: ViewGroup, viewType: Int): BannerDataViewHolder {
        return BannerDataViewHolder(parent)
    }

    override fun onBindView(holder: BannerDataViewHolder, data: NewWidgetDetailEntity?, position: Int, size: Int) {
        holder.initUIData(data)
    }

    /***
     *@date 创建时间 2020/9/4 14:31
     *<AUTHOR> W.YuLong
     *@description
     */
    class BannerDataViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.widget_view_holder_banner_layout) {
        var itemBannerImageView: ImageView = itemView.findViewById(R.id.item_banner_ImageView)

        override fun <T> initUIData(t: T) {
            val widgetDetailBean = t as NewWidgetDetailEntity
            PictureUtil.loadImage(itemBannerImageView, widgetDetailBean.imgPath, R.drawable.ic_load_failed_bg)
            itemBannerImageView.setOnClickListener {
                MicroWidgetViewFactory.parseDeepLinkParams(itemView.context, widgetDetailBean.widgetViewParam)
                logEvent(AnalyticsConstants.LOG_V1_PRODUCT_WIDGET, LOG_V1_PARAM_WIDGET_ID, MicroWidgetViewFactory.KEY_banners)
            }
        }
    }
}
