package com.wedevote.wdbook.ui.read

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import android.view.View.OnTouchListener
import android.view.View.VISIBLE
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.FragmentActivity
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.dialog.CommAlertDialog
import com.aquila.lib.dialog.OnDialogViewClickListener
import com.aquila.lib.layout.SmartRefreshLayout
import com.aquila.lib.layout.api.RefreshLayout
import com.aquila.lib.layout.listener.OnRefreshListener
import com.aquila.lib.tools.singleton.SPSingleton
import com.aquila.lib.tools.util.ToastUtil
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APP
import com.wedevote.wdbook.base.APPConfig.isCurrentThemeLight
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.DataStatus
import com.wedevote.wdbook.constants.HighlightColorType
import com.wedevote.wdbook.entity.NoteEntity
import com.wedevote.wdbook.entity.feedback.CorrectionParamsEntity
import com.wedevote.wdbook.tools.event.BookDataChangeEvent
import com.wedevote.wdbook.tools.event.DialogDismissEvent
import com.wedevote.wdbook.tools.event.OnNoteMergedEvent
import com.wedevote.wdbook.tools.event.OnSyncNoteFinish
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.tools.util.getFunctionInfo
import com.wedevote.wdbook.ui.read.lib.BookParameters
import com.wedevote.wdbook.ui.read.lib.BookTextControllerHelper
import com.wedevote.wdbook.ui.read.lib.ContentUtils
import com.wedevote.wdbook.ui.read.lib.EPubBook
import com.wedevote.wdbook.ui.read.lib.IGetSelectTextListener
import com.wedevote.wdbook.ui.read.lib.TextSelectOperateLayout
import com.wedevote.wdbook.ui.read.lib.span.FootnoteSpan
import com.wedevote.wdbook.ui.read.lib.view.FormatContentView
import com.wedevote.wdbook.ui.read.lib.view.TextPageManager
import com.wedevote.wdbook.ui.read.lib.view.Word
import com.wedevote.wdbook.ui.read.widgets.BookMarkRefreshHeaderLayout
import com.wedevote.wdbook.ui.user.NoteEditActivity
import com.wedevote.wdbook.ui.user.OnNoteEditCallback
import com.wedevote.wdbook.utils.JsonUtility
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus

/***
 *@date 重构时间 2020/11/24 11:04
 *<AUTHOR> W.YuLong
 *@description
 */
class BookTextViewHolder(parent: ViewGroup, var controllerHelper: BookTextControllerHelper) :
    BaseViewHolder(parent, R.layout.format_text_listview), OnRefreshListener, OnCancelHighlightCallback {
    private var textPageManager: TextPageManager? = null

    private var touchX = 0f
    private var touchY = 0f
    private var isClosingSelect = false
    private val VERSE_LINK_HEAD = "wdbible://bible/"

    val refreshLayout: SmartRefreshLayout = itemView.findViewById(R.id.format_root_RefreshLayout)
    var pageTitleTextView: TextView = itemView.findViewById(R.id.format_text_title_TextView)
    val pageNumberTextView: TextView = itemView.findViewById(R.id.format_text_page_number_TextView)
    val formatContentView: FormatContentView = itemView.findViewById(R.id.format_text_content_FormatView)
    val textSelectOperateLayout: TextSelectOperateLayout = itemView.findViewById(R.id.format_text_selected_layout)
    val bookmarkFlagImageView: ImageView = itemView.findViewById(R.id.format_book_mark_Flag_ImageView)
    val noteCountTextView: TextView = itemView.findViewById(R.id.format_text_note_count_TextView)

    val bookMarkRefreshHeaderLayout: BookMarkRefreshHeaderLayout

    var onBookmarkActionListener: OnBookmarkActionListener? = null
        set(value) {
            field = value
            bookMarkRefreshHeaderLayout.onBookmarkActionListener = value
        }

    var previousSelectedStartWord = -1

    init {
        if (isCurrentThemeLight()) {
            itemView.setBackgroundColor(Color.WHITE)
            pageTitleTextView.setTextColor(BookParameters.textNormalColor)
            pageNumberTextView.setTextColor(BookParameters.textNormalColor)
        } else {
            pageTitleTextView.setTextColor(BookParameters.textNormalColorDark)
            pageNumberTextView.setTextColor(BookParameters.textNormalColorDark)
            itemView.setBackgroundColor(Color.BLACK)
        }
        setViewListeners()
        if (controllerHelper.indexInChapter >= 0) {
            val pageFirstLineIndex = controllerHelper.getPageFirstLineIndex()
            val totalLines = controllerHelper.getPageTotalLines(controllerHelper.indexInChapter)
            textPageManager = TextPageManager(controllerHelper.textPageList, controllerHelper.lineHeight, pageFirstLineIndex, totalLines)
            textPageManager!!.initPageView(formatContentView, controllerHelper.pageHeight)
            formatContentView.setPageParameters(controllerHelper.textPageList, pageFirstLineIndex, totalLines)
        }

        formatContentView.onCancelHighlightCallback = this
        bookMarkRefreshHeaderLayout = BookMarkRefreshHeaderLayout(parent.context)
        refreshLayout.setRefreshHeader(bookMarkRefreshHeaderLayout)
        refreshLayout.setOnRefreshListener(this)
    }

    var pathIndex = 0
    var indexInChapter = 0
    var positionInViewPager = 0
    fun initDataUI(bookTitle: String?, pathIndex: Int, indexInChapter: Int, position: Int) {
        this.pathIndex = pathIndex
        this.indexInChapter = indexInChapter
        this.positionInViewPager = position
        if (indexInChapter != 0) {
            pageTitleTextView.text = bookTitle
        } else {
            pageTitleTextView.text = ""
        }

//        var notchHeight = SPSingleton.get().getInt(SPKeyDefine.SP_NotchHeight, 0)
//        if (notchHeight > 0) {
//            pageTitleTextView.setPadding(0, notchHeight, 0, 10)
//        }

        reloadNoteListData()
        initBookMarkFlagShow()
    }

    private fun initBookMarkFlagShow() {
        var firstWordLocation = controllerHelper.getFirstWordOffset(indexInChapter)
        var lastWordLocation = controllerHelper.getPageLastWordOffset(indexInChapter)
        var bookMarkBean = SDKSingleton.dbWrapBl
            .getBookmarkEntity(
                controllerHelper.resourceId!!,
                EPubBook.pathList[pathIndex],
                firstWordLocation,
                lastWordLocation
            )
        if (bookMarkBean != null) {
            bookmarkFlagImageView.elevation = 10f
            bookmarkFlagImageView.setImageResource(R.drawable.ic_bookmark_long_red)
        } else {
            bookmarkFlagImageView.elevation = -10f
            bookmarkFlagImageView.setImageResource(R.drawable.ic_bookmark_white_line)
        }
        bookMarkRefreshHeaderLayout.isAdded = bookMarkBean != null
    }

    override fun onRefresh(layout: RefreshLayout) {
        refreshLayout.finishLoadMoreAndRefresh()
    }

    fun isNoteFolded(): Boolean {
        return noteCountTextView.visibility == VISIBLE
    }

    private fun isInnerAddress(link: String): Boolean {
        var url = link
        val index = url.lastIndexOf('#')
        if (index > 0) {
            url = url.substring(0, index)
        }

        for (path in EPubBook.pathList) {
            if (path.endsWith(url)) {
                return true
            }
        }
        return false
    }

    private fun showFootnoteDialog(context: Context, text: String?, id: Int) {
        val noteDialog = FootnoteDialog(context)
        noteDialog.show()
        noteDialog.initFootnoteText(text, id)
        noteDialog.setOnDismissListener{
            EventBus.getDefault().post(DialogDismissEvent())
        }
    }

    var lastDragPosition = 0
    var viewMargin = ContentUtils.contentHorizonMargin
    var lastLinePosition = -1
    var lastWordPosition = -1

    fun setOnPageListener(listener: OnTextOperateListener?) {
        textSelectOperateLayout.onTextOperateListener = listener
    }

    fun reloadNoteListData() {
        if (textPageManager!!.lastWord == null) {
            noteCountTextView.visibility = View.GONE
            return
        }
        var noteList = SDKSingleton.dbWrapBl.getPageNoteEntityList(
            controllerHelper.resourceId!!, EPubBook.pathList[pathIndex], 0,
            textPageManager!!.lastWord!!.location
        )

        var noteNumberInPage = 0
        val newNoteList = ArrayList<NoteEntity>()
        for (itemEntity in noteList) {
            val text = controllerHelper.textPageList.originalText
            val matchEntity = SDKSingleton.dbWrapBl.matchNoteInContent(itemEntity, text)
            if (matchEntity.noteText.isNotEmpty() && matchEntity.wordEndOffset >= textPageManager!!.firstWord!!.location) {
                noteNumberInPage++
            }
            newNoteList.add(matchEntity)
        }

        // 获取搜索高亮数据
        val searchHighlights = getSearchHighlights()
        if (searchHighlights.isNotEmpty()) {
            newNoteList.addAll(searchHighlights)
        }

        if (noteNumberInPage > 10) {
            noteCountTextView.visibility = VISIBLE
            noteCountTextView.text = noteCountTextView.context.getString(R.string.notes) + " " + noteNumberInPage
            textSelectOperateLayout.onTextOperateListener?.onNoteFold(positionInViewPager)
        } else {
            noteCountTextView.visibility = View.GONE
        }

        formatContentView.initNoteEntityList(newNoteList, noteNumberInPage > 10)
    }
    
    private fun getSearchHighlights(): List<NoteEntity> {
        val activity = itemView.context as? BookReadActivity ?: return emptyList()
        val noteEntityJson = activity.intent.getStringExtra(IntentConstants.EXTRA_NoteEntity) ?: return emptyList()
        
        try {
            val noteEntity = JsonUtility.decodeFromString<NoteEntity>(noteEntityJson)
            if (!noteEntity.isSearchHighlight) {
                return listOf()
            }
            if (EPubBook.getPathIndex(noteEntity.pagePath) == pathIndex) {
                // 获取当前页面的文本内容
                val pageText = controllerHelper.textPageList.originalText
                val contentWithTag = noteEntity.noteText ?: ""
                
                // 尝试使用新格式处理
                val newFormatResults = processNewFormatHighlights(noteEntity, contentWithTag, pageText)
                if (newFormatResults.isNotEmpty()) {
                    return newFormatResults
                }
                
                // 如果新格式处理失败，回退到旧的处理方式
                return processOldFormatHighlights(noteEntity, contentWithTag, pageText)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        
        return emptyList()
    }
    
    /**
     * 处理新格式的高亮标记 [[[原始句子]]]带标记内容
     */
    private fun processNewFormatHighlights(noteEntity: NoteEntity, contentWithTag: String, pageText: String): List<NoteEntity> {
        val sentenceRegex = "\\[\\[\\[(.*?)\\]\\]\\]".toRegex()
        val sentenceMatch = sentenceRegex.find(contentWithTag) ?: return emptyList()
        
        // 提取原始句子和带标记的内容
        val originalSentence = sentenceMatch.groupValues[1]
        val highlightContent = contentWithTag.substring(sentenceMatch.range.last + 1)

        val sentenceStartOffset = EPubBook.findSentenceOffsetWithRTLSpaces(pageText, originalSentence)

        // 如果找到了句子位置，处理高亮部分
        if (sentenceStartOffset >= 0) {
            return processHighlightsInSentence(noteEntity, originalSentence, highlightContent, sentenceStartOffset, pageText)
        }
        
        return emptyList()
    }

    /**
     * 处理句子中的高亮部分
     */
    private fun processHighlightsInSentence(
        noteEntity: NoteEntity,
        originalSentence: String,
        highlightContent: String,
        sentenceStartOffset: Int,
        pageText: String
    ): List<NoteEntity> {
        val highlightRegex = "\\{\\{\\{(.*?)\\}\\}\\}".toRegex()
        val noteEntities = ArrayList<NoteEntity>()

        // 从找到的句子开头开始，在 pageText 中搜索高亮
        var searchStartIndexInPage = sentenceStartOffset

        for (match in highlightRegex.findAll(highlightContent)) {
            val highlightText = match.groupValues[1]
            if (highlightText.isEmpty()) continue // 跳过空匹配

            // 直接在 pageText 中搜索高亮文本，从上一个匹配之后开始
            val absoluteOffset = pageText.indexOf(highlightText, searchStartIndexInPage)

            if (absoluteOffset >= 0) {
                // 在 pageText 中按顺序找到了高亮
                val newNoteEntity = createHighlightNoteEntity(noteEntity, highlightText, absoluteOffset)
                noteEntities.add(newNoteEntity)

                // 为下一次搜索做准备，从当前高亮的末尾开始
                searchStartIndexInPage = absoluteOffset + highlightText.length
            } else {
                // 如果在原始句子中找不到高亮文本，直接在页面文本中搜索
                if (pageText.contains(highlightText)) {
                    // 优先在句子附近搜索
                    val nearSentenceStart = maxOf(0, sentenceStartOffset - 20)
                    val nearSentenceEnd = minOf(pageText.length, sentenceStartOffset + originalSentence.length + 20)
                    val nearSentenceText = pageText.substring(nearSentenceStart, nearSentenceEnd)

                    var exactStartOffset = -1
                    if (nearSentenceText.contains(highlightText)) {
                        exactStartOffset = nearSentenceStart + nearSentenceText.indexOf(highlightText)
                    } else {
                        // 如果句子附近找不到，在整个页面中搜索
                        exactStartOffset = pageText.indexOf(highlightText)
                    }

                    if (exactStartOffset >= 0) {
                        val newNoteEntity = createHighlightNoteEntity(noteEntity, highlightText, exactStartOffset)
                        noteEntities.add(newNoteEntity)
                    }
                }
            }
        }

        return noteEntities
    }
    
    /**
     * 处理旧格式的高亮标记
     */
    private fun processOldFormatHighlights(noteEntity: NoteEntity, contentWithTag: String, pageText: String): List<NoteEntity> {
        val regex = "\\{\\{\\{(.*?)\\}\\}\\}".toRegex()
        val content = noteEntity.summary // 原始内容
        if (contentWithTag.isNullOrEmpty() || !contentWithTag.contains("{{{")) {
            // 如果没有标记，则返回原始高亮
            return processPlainHighlight(noteEntity, content, pageText)
        }
        
        // 创建多个高亮实体
        val noteEntities = ArrayList<NoteEntity>()
        val matches = regex.findAll(contentWithTag)

        for (match in matches) {
            val highlightText = match.groupValues[1]
            val startTagIndex = match.range.first
            val endTagIndex = match.range.last + 1
            
            // 计算标记在原始文本中的位置
            val beforeTagWithMarkers = contentWithTag.substring(0, startTagIndex)
            val markerCount = beforeTagWithMarkers.count { it == '{' || it == '}' }
            val beforeTagLength = beforeTagWithMarkers.length - markerCount
            
            val startInOriginal = noteEntity.wordStartOffset + beforeTagLength
            val endInOriginal = startInOriginal + highlightText.length
            
            val newNoteEntity = createHighlightNoteEntity(noteEntity, highlightText, startInOriginal)
            newNoteEntity.wordEndOffset = endInOriginal
            
            // 尝试在当前页面内容中精确定位
            updateNoteEntityWithExactPosition(newNoteEntity, highlightText, pageText)
            
            noteEntities.add(newNoteEntity)
        }
        
        return if (noteEntities.isEmpty()) {
            // 如果没有找到标记，则返回原始高亮
            processPlainHighlight(noteEntity, content, pageText)
        } else {
            noteEntities
        }
    }
    
    /**
     * 处理普通的高亮（没有特殊标记）
     */
    private fun processPlainHighlight(noteEntity: NoteEntity, content: String, pageText: String): List<NoteEntity> {
        val highlightedEntity = createHighlightNoteEntity(noteEntity, content, noteEntity.wordStartOffset)
        
        // 尝试在当前页面内容中精确定位
        if (pageText.contains(content)) {
            val exactStartOffset = pageText.indexOf(content)
            if (exactStartOffset >= 0) {
                val newNoteEntity = createHighlightNoteEntity(noteEntity, content, exactStartOffset)
                return listOf(newNoteEntity)
            }
        }
        
        return listOf(highlightedEntity)
    }
    
    /**
     * 创建高亮的NoteEntity对象
     */
    private fun createHighlightNoteEntity(baseEntity: NoteEntity, highlightText: String, startOffset: Int): NoteEntity {
        val newNoteEntity = NoteEntity(baseEntity)
        newNoteEntity.wordStartOffset = startOffset
        newNoteEntity.wordEndOffset = startOffset + highlightText.length
        newNoteEntity.summary = highlightText
        newNoteEntity.highlightColorType = HighlightColorType.COLOR_ORANGE
        newNoteEntity.isSearchHighlight = true
        return newNoteEntity
    }
    
    /**
     * 使用精确位置更新NoteEntity对象
     */
    private fun updateNoteEntityWithExactPosition(noteEntity: NoteEntity, highlightText: String, pageText: String) {
        if (pageText.contains(highlightText)) {
            // 在当前页面内容中找到了原始内容，更新偏移量
            val exactStartOffset = pageText.indexOf(highlightText)
            if (exactStartOffset >= 0) {
                noteEntity.wordStartOffset = exactStartOffset
                noteEntity.wordEndOffset = exactStartOffset + highlightText.length
            }
        } else {
            // 如果直接匹配失败，尝试模糊匹配
            val cleanHighlightText = highlightText.replace(Regex("[\\s,.;:\"'\\-!?()\\[\\]{}]"), "")
            val cleanPageText = pageText.replace(Regex("[\\s,.;:\"'\\-!?()\\[\\]{}]"), "")
            
            if (cleanHighlightText.length > 5 && cleanPageText.contains(cleanHighlightText)) {
                val fuzzyStartOffset = cleanPageText.indexOf(cleanHighlightText)
                if (fuzzyStartOffset >= 0) {
                    val startSearchPos = maxOf(0, fuzzyStartOffset - 20)
                    val endSearchPos = minOf(pageText.length, fuzzyStartOffset + cleanHighlightText.length + 40)
                    
                    if (startSearchPos < endSearchPos) {
                        val searchRange = pageText.substring(startSearchPos, endSearchPos)
                        
                        val bestMatch = findBestMatch(highlightText, searchRange)
                        if (bestMatch >= 0) {
                            noteEntity.wordStartOffset = startSearchPos + bestMatch
                            noteEntity.wordEndOffset = noteEntity.wordStartOffset + highlightText.length
                        }
                    }
                }
            }
        }
    }

    // 查找最佳匹配位置
    private fun findBestMatch(needle: String, haystack: String): Int {
        if (haystack.contains(needle)) {
            return haystack.indexOf(needle)
        }
        
        // 如果完全匹配失败，尝试查找部分匹配
        // 从最长的可能匹配开始尝试
        for (length in needle.length - 1 downTo minOf(needle.length / 2, 5)) {
            val partialNeedle = needle.substring(0, length)
            if (haystack.contains(partialNeedle)) {
                return haystack.indexOf(partialNeedle)
            }
        }

        // 如果前缀匹配失败，尝试使用滑动窗口查找最长公共子串
        if (needle.length > 10) {
            var bestMatchLength = 0
            var bestMatchPos = -1

            // 使用不同长度的窗口尝试匹配
            for (windowSize in minOf(needle.length, 20) downTo 8) {
                // 在needle中滑动窗口
                for (i in 0..needle.length - windowSize) {
                    val window = needle.substring(i, i + windowSize)
                    if (haystack.contains(window)) {
                        // 找到匹配，记录位置
                        val matchPos = haystack.indexOf(window)
                        if (windowSize > bestMatchLength) {
                            bestMatchLength = windowSize
                            bestMatchPos = matchPos
                        }
                    }
                }

                // 如果找到了足够长的匹配，就返回
                if (bestMatchLength >= 8) {
                    return bestMatchPos
                }
            }

            // 如果找到了任何匹配，返回最佳匹配位置
            if (bestMatchPos >= 0) {
                return bestMatchPos
            }
        }

        return -1
    }

    fun getSelectedWordPosition(first: Boolean): Int {
        if (previousSelectedStartWord >= 0) {
            return previousSelectedStartWord
        } else {
            val list = formatContentView.getSelectedWordsBoundary()
            if (!list.isNullOrEmpty()) {
                if (first) {
                    return list[0]
                } else {
                    return list[1]
                }
            } else {
                return -1
            }
        }
    }

    fun setPreviousSelectedWord(prevWord: Int, hideFirstDot: Boolean) {
        previousSelectedStartWord = prevWord
        if (hideFirstDot) {
            formatContentView.isShowFirstSelectedDot = false
        } else {
            formatContentView.isShowLastSelectedDot = false
        }
    }

    private fun initNoteEntity(noteEntity: NoteEntity?): NoteEntity {
        var entity: NoteEntity
        if (noteEntity != null) {
            entity = noteEntity
        } else {
            entity = NoteEntity()
            entity.also {
                it.createTime = System.currentTimeMillis()
                it.resourceId = controllerHelper.resourceId ?: ""
                it.highlightColorType = BookParameters.lastMarkType
                it.pagePath = EPubBook.pathList[pathIndex]
                it.filePosition = EPubBook.getOriginalIndex(it.pagePath)
                it.tocTitle = EPubBook.getTocTitle(pathIndex)
                var list = formatContentView.getSelectedWordsBoundary()
                if (list.size == 2) {
                    it.wordStartOffset = list[0]
                    it.wordEndOffset = list[1]

                    if (previousSelectedStartWord >= 0) {
                        if (it.wordStartOffset > previousSelectedStartWord) {
                            it.wordStartOffset = previousSelectedStartWord
                        } else {
                            it.wordEndOffset = previousSelectedStartWord
                        }
                    }
                }
                it.markStyle =
                    if (BookParameters.lastMarkType == HighlightColorType.LINE_ORANGE) NoteEntity.STYLE_DRAW_LINE else NoteEntity.STYLE_HIGHLIGHT
                it.lastUpdateTime = System.currentTimeMillis()
                it.summary = formatContentView.getTextFromPosition(it.wordStartOffset, it.wordEndOffset)
            }
            entity.dataId = ""
            entity.dataStatus = DataStatus.NORMAL.value
        }
        SDKSingleton.dbWrapBl.mergeNote(entity, controllerHelper.resourceId!!, EPubBook.pathList[pathIndex], false)?.let {
            entity = it
        }
        return entity
    }

    private fun checkAndMergeNote(noteEntity: NoteEntity, force: Boolean): Boolean {
        val newNoteEntity =
            SDKSingleton.dbWrapBl.mergeNote(noteEntity, controllerHelper.resourceId!!, EPubBook.pathList[pathIndex], force) ?: return false
        EventBus.getDefault().post(OnNoteMergedEvent())
        newNoteEntity.summary = formatContentView.getTextFromPosition(newNoteEntity.wordStartOffset, newNoteEntity.wordEndOffset)
        return true
    }

    private fun setViewListeners() {
        noteCountTextView.setOnClickListener {
            val dialog = BookNoteAndMarkDialogFragment()
            dialog.arguments = Bundle().apply {
                putString(IntentConstants.EXTRA_ResourceId, controllerHelper.resourceId!!)
                putInt(IntentConstants.EXTRA_PathIndex, pathIndex)
                putInt(IntentConstants.EXTRA_StartWord, textPageManager!!.firstWord!!.location)
                putInt(IntentConstants.EXTRA_EndWord, textPageManager!!.lastWord!!.location)
            }
            dialog.setOnJumpListener(object : OnJumpActionListener {
                override fun doJump(page: Int, offset: Int) {
                }
            })
            dialog.show((formatContentView.context as FragmentActivity).supportFragmentManager, "BookNoteAndMarkDialog")
        }

        textSelectOperateLayout.iGetSelectTextListener = object : IGetSelectTextListener {
            override fun getSelectText(): String {
                if (!formatContentView.checkSelectContent()) {
                    return ""
                }
                var copyText = formatContentView.getSelectedText(getSelectedWordPosition(false)).trim()
                if (copyText.length > 10) {
                    copyText += SDKSingleton.dbWrapBl.getCopySuffix(controllerHelper.resourceId!!)
                }
                return copyText
            }
        }

        textSelectOperateLayout.onBookNoteActionListener = object : OnBookNoteActionListener {
            override fun onCancelSelect() {
                textSelectOperateLayout.setTextOperateViewShowState(false)
                textSelectOperateLayout.setSelectState(false)
                refreshLayout.isEnableRefresh = true
                previousSelectedStartWord = -1
                formatContentView.cancelSelection()
                formatContentView.isShowFirstSelectedDot = true
                formatContentView.isShowLastSelectedDot = true
            }

            override fun initCorrectionEntity(text: String): CorrectionParamsEntity {
                return CorrectionParamsEntity().also {
                    it.resourceId = controllerHelper.resourceId ?: ""
                    it.fileId = controllerHelper.fileId ?: ""
                    it.pagePath = EPubBook.pathList[pathIndex]
                    var list = formatContentView.getSelectedWordsBoundary()
                    if (list.size == 2) {
                        it.wordStartOffset = list[0]
                        it.wordEndOffset = list[1]
                        if (previousSelectedStartWord >= 0) {
                            if (it.wordStartOffset > previousSelectedStartWord) {
                                it.wordStartOffset = previousSelectedStartWord
                            } else {
                                it.wordEndOffset = previousSelectedStartWord
                            }
                        }
                        it.chapterName = EPubBook.getTocTitle(pathIndex)
                        it.bookName = SDKSingleton.userBl.getShelfBookItemEntityByResourceId(it.resourceId)!!.resourceName
                        it.summary = formatContentView.getTextFromPosition(it.wordStartOffset, it.wordEndOffset)
                    }
                }
            }

            private fun doDeleteNote(dataId: String) {
                SDKSingleton.dbWrapBl.removeNote(dataId)
                onCancelSelect()
                textSelectOperateLayout.onTextOperateListener?.onReloadBookData()

                MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
                    SDKSingleton.syncBl.syncNoteData()
                    EventBus.getDefault().post(OnSyncNoteFinish())
                }
            }

            override fun onDeleteNote(noteEntity: NoteEntity?) {
                noteEntity?.let {
                    if (noteEntity.noteText.isNotEmpty()) {
                        val dialog = CommAlertDialog.with(itemView.context)
                            .setTitle(R.string.delete_highlight)
                            .setMessage(R.string.will_delete_note)
                            .setStartText(R.string.label_cancel)
                            .setEndText(R.string.label_delete)
                            .setOnViewClickListener { _, _, tag ->
                                if (tag == CommAlertDialog.TAG_CLICK_END) {
                                    doDeleteNote(noteEntity.dataId!!)
                                }
                            }.create()
                        dialog.setOnDismissListener {
                            onCancelSelect()
                        }
                        dialog.show()
                    } else {
                        doDeleteNote(noteEntity.dataId!!)
                    }
                }
            }

            private fun doHighlight(highlightColorType: HighlightColorType, note: NoteEntity) {
                note.highlightColorType = highlightColorType
                note.markStyle =
                    if (BookParameters.lastMarkType == HighlightColorType.LINE_ORANGE)
                        NoteEntity.STYLE_DRAW_LINE else NoteEntity.STYLE_HIGHLIGHT
                SDKSingleton.dbWrapBl.saveNote(note)
                textSelectOperateLayout.noteEntity = note
                SPSingleton.get().putString(SPKeyDefine.SP_LastMarkType, highlightColorType.value)
                formatContentView.cancelSelection()
                textSelectOperateLayout.onTextOperateListener?.onReloadBookData()
            }

            override fun onHighlightAction(color: HighlightColorType, noteEntity: NoteEntity?): Boolean {
                if (!formatContentView.checkSelectContent()) {
                    return false
                }
                BookParameters.lastMarkType = color
                var note = initNoteEntity(noteEntity)
                if (note.wordStartOffset == note.wordEndOffset) {
                    ToastUtil.showToastShort(formatContentView.context.getText(R.string.no_empty_note))
                    return false
                }

                if (checkAndMergeNote(note, false)) {
                    doHighlight(color, note)
                } else {
                    CommAlertDialog.with(formatContentView.context).setMessage(R.string.operate_error_text)
                        .setStartText(R.string.label_cancel)
                        .setEndText(R.string.continue_to_merge)
                        .setTitle(R.string.operate_error).setOnViewClickListener(object :
                            OnDialogViewClickListener {
                            override fun onViewClick(dialog: Dialog, v: View, tag: Int) {
                                if (tag == CommAlertDialog.TAG_CLICK_END) {
                                    checkAndMergeNote(note, true)
                                    doHighlight(color, note)
                                }
                            }
                        }).create().show()
                    onCancelSelect()
                }
                return true
            }

            private fun doNoteShow(noteEntity: NoteEntity) {
                NoteEditActivity.gotoNoteEdit(
                    itemView.context, noteEntity,
                    object : OnNoteEditCallback {
                        override fun onNoteEditFinish(entity: NoteEntity) {
                            onCancelSelect()
                            textSelectOperateLayout.onTextOperateListener?.onReloadBookData()
                            EventBus.getDefault().post(DialogDismissEvent())
                        }
                    }
                )
            }

            override fun onNoteShow(noteEntity: NoteEntity?): Boolean {
                if (noteEntity!!.wordStartOffset == noteEntity.wordEndOffset) {
                    ToastUtil.showToastShort(formatContentView.context.getText(R.string.no_empty_note))
                    return false
                }

                if (!checkAndMergeNote(noteEntity, false)) {
                    CommAlertDialog.with(formatContentView.context).setMessage(R.string.operate_error_text)
                        .setStartText(R.string.label_cancel)
                        .setEndText(R.string.continue_to_merge)
                        .setTitle(R.string.operate_error).setOnViewClickListener(object : OnDialogViewClickListener {
                            override fun onViewClick(dialog: Dialog, v: View, tag: Int) {
                                if (tag == CommAlertDialog.TAG_CLICK_END) {
                                    checkAndMergeNote(noteEntity, true)
                                    doNoteShow(noteEntity)
                                }
                            }
                        }).create().show()
                    return false
                }
                doNoteShow(noteEntity)
                return true
            }

            override fun onNoteAdd(noteEntity: NoteEntity?) {
                if (!formatContentView.checkSelectContent()) {
                    return
                }
                val note = initNoteEntity(noteEntity)
                if (!onNoteShow(note)) {
                    onCancelSelect()
                }
            }
        }
        var isSlideUp = false
        formatContentView.setOnTouchListener(object : OnTouchListener {
            override fun onTouch(v: View, event: MotionEvent): Boolean {
                if (processOnTouch(event)) {
                    return true
                }
                when (event.actionMasked) {
                    MotionEvent.ACTION_DOWN -> {
                        touchX = event.x
                        touchY = event.y
                        isSlideUp = false
                        isClosingSelect = false
                        if (textSelectOperateLayout.isInSelect) {
                            formatContentView.cancelSelection()
                            textSelectOperateLayout.setTextOperateViewShowState(false)
                            textSelectOperateLayout.setSelectState(false)
                            refreshLayout.isEnableRefresh = true
                            previousSelectedStartWord = -1
                            formatContentView.isShowFirstSelectedDot = true
                            formatContentView.isShowLastSelectedDot = true
                            isClosingSelect = true
                        }
                    }
                    MotionEvent.ACTION_MOVE -> {
                        if (Math.abs(event.y - touchY) > 20f) {
                            isSlideUp = true
                        }
                    }
                    MotionEvent.ACTION_UP -> {
                        if (isSlideUp) {
                            return true
                        }

                        if (textSelectOperateLayout.isInSelect || isClosingSelect) {
                            isClosingSelect = false
                            return false
                        }
                        val x = event.x
                        val y = event.y
                        /*点击文本注解*/
                        val word = getLinkWord(x, y)
                        if (word != null) {
                            val linkSpan = word.findLink()
                            if (linkSpan != null) {
                                if (linkSpan is FootnoteSpan) {
                                    showFootnoteDialog(formatContentView.context, linkSpan.link, linkSpan.id)
                                    return true
                                } else if (!linkSpan.link.isNullOrEmpty()) {
                                    showBibleVerseDialog(formatContentView.context, linkSpan.link)
                                    return true
                                }
                            }
                        }
                        /*点击图片*/
                        val bitmap = getImage(x, y)
                        if (bitmap != null) {
                            textSelectOperateLayout.onTextOperateListener?.onBitmapClick(bitmap)
                            return true
                        }

                        /*点击了高亮*/
                        var noteEntity = formatContentView.getTouchNoteData(x, y)
                        if (noteEntity != null && !noteEntity.isSearchHighlight) {
                            var position = formatContentView.getLinePosition(touchY)
                            if (position >= 0) {
                                val currentLineHeight =
                                    formatContentView.textPageList!![position].getLineHeight(
                                        textPageManager!!.lineHeight
                                    )

                                textSelectOperateLayout.setTextOperateViewShowState(true)
                                textSelectOperateLayout.setSelectState(true)
                                refreshLayout.isEnableRefresh = false
                                textSelectOperateLayout.initSelectNoteUI(noteEntity)
                                val y = formatContentView.getLineBottomY(position)
                                textSelectOperateLayout.setSelectedContainerLayoutPosition(x, y, currentLineHeight)
                            }
                            textSelectOperateLayout.onTextOperateListener?.onTextClick(positionInViewPager, true)
                            return true
                        }

                        val note = formatContentView.getTouchNoteFlagData(touchX, touchY)
                        if (note != null) {
                            textSelectOperateLayout.onBookNoteActionListener?.onNoteShow(note)
                            return true
                        }

                        val flipRange = formatContentView.width / 4
                        if (touchX < flipRange) {
                            if (textSelectOperateLayout.onTextOperateListener?.onFlipPage(positionInViewPager, false)!!) {
                                return false
                            }
                        } else if (touchX > formatContentView.width - flipRange) {
                            if (textSelectOperateLayout.onTextOperateListener?.onFlipPage(positionInViewPager, true)!!) {
                                return false
                            }
                        }

                        if (!textSelectOperateLayout.isInSelect) {
                            textSelectOperateLayout.onTextOperateListener?.onTextClick(positionInViewPager, false)
                        }
                    }
                }
                return false
            }
        })

        formatContentView.setOnLongClickListener(object : View.OnLongClickListener {
            override fun onLongClick(v: View): Boolean {
                if (isSlideUp || textSelectOperateLayout.isInSelect || bookMarkRefreshHeaderLayout.isRefreshing()) {
                    return false
                }

                var position = formatContentView.getLinePosition(touchY)
                if (position < 0) {
                    return false
                }

                var wordStart = textPageManager!!.getWordPosition(touchX - 2 * viewMargin, position) - 2
                val line = formatContentView.textPageList!![position]
                val wordEnd: Int
                if (wordStart < 0) {
                    wordStart = 0
                } else if (wordStart + 2 < line.size) {
                    wordStart += 2
                }

                wordEnd = if (wordStart + 2 <= line.size) {
                    wordStart + 2 // 从当前长按的点，一次选择两个字符
                } else {
                    line.size
                }
                showTextSelectLayout(position, position, wordStart, wordEnd)
                lastDragPosition = 2
                return true
            }
        })
    }

    private fun showBibleVerseDialog(context: Context, link: String?) {
        if (!link.isNullOrEmpty()) {
            if (link.startsWith(VERSE_LINK_HEAD)) {
                val verseDialog = BibleVerseDialog(context)
                verseDialog.show()
                verseDialog.setOnDismissListener{
                    EventBus.getDefault().post(DialogDismissEvent())
                }
                try {
                    verseDialog.initBibleContentUI(link)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            } else if (isInnerAddress(link)) {
                textSelectOperateLayout.onTextOperateListener?.onInnerJump(link)
            } else {
                val intent = Intent(Intent.ACTION_VIEW)
                intent.action = "android.intent.action.VIEW"
                intent.data = Uri.parse(link)
                try {
                    (context as Activity).startActivity(intent)
                } catch (e: Exception) {
                    e.printStackTrace()
                    SDKSingleton.loggerBl.handleThrowable(e, getFunctionInfo())
                }
            }
        }
    }

    fun processOnTouch(event: MotionEvent): Boolean {
        if (!textSelectOperateLayout.isInSelect) {
            return false
        }

        var actionMask = event.actionMasked
        if (actionMask != MotionEvent.ACTION_DOWN && lastDragPosition == 0) {
            return false
        }
        when (actionMask) {
            MotionEvent.ACTION_DOWN -> {
                lastDragPosition = formatContentView.getDragPosition(event.x, event.y)
                if (lastDragPosition == 0) {
                    return false
                }
            }
            MotionEvent.ACTION_MOVE -> {
                try {
                    lastLinePosition = formatContentView.getLinePosition(event.y)
                    if (lastLinePosition < 0) {
                        return true
                    }
                    lastWordPosition = textPageManager!!.getWordPosition(event.x - viewMargin, lastLinePosition)
                    formatContentView.setSelectionPosition(lastLinePosition, lastWordPosition, lastDragPosition == 1)
                    if (previousSelectedStartWord < 0) {
                        textSelectOperateLayout.onTextOperateListener?.onSelectedChanged(
                            lastLinePosition,
                            lastWordPosition
                        )
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
            MotionEvent.ACTION_UP -> {
                var lineY = formatContentView.getLinePosition(event.y)
                if (lineY < 0) {
                    lineY = controllerHelper.getPageLastLineIndex(indexInChapter)
                }
                val wordPosition = textPageManager!!.getWordPosition(event.x - viewMargin, lineY)

                textSelectOperateLayout.onTextOperateListener?.onSelectedStopMoving()

                val x = textPageManager!!.getPressX(lineY, wordPosition) + viewMargin
                val currentLineHeight = formatContentView.textPageList!![lineY].getLineHeight(textPageManager!!.lineHeight)
                val y = formatContentView.getLineBottomY(lineY)
                textSelectOperateLayout.setSelectedContainerLayoutPosition(x, y, currentLineHeight)
                textSelectOperateLayout.onTextOperateListener?.onTextClick(positionInViewPager, true)
            }
        }
        return true
    }

    fun showTextSelectLayout(lineStart: Int, lineEnd: Int, wordStart: Int, wordEnd: Int, isOnlySelect: Boolean = false) {
        val x = textPageManager!!.getPressX(lineEnd, wordEnd) + viewMargin
        formatContentView.setSelectionPosition(lineStart, wordStart, true)
        formatContentView.setSelectionPosition(lineEnd, wordEnd, false)

        val currentLineHeight =
            formatContentView.textPageList!![lineEnd].getLineHeight(textPageManager!!.lineHeight)

        val lineBottomY = formatContentView.getLineBottomY(lineEnd)
        try {
            textSelectOperateLayout.setTextOperateViewShowState(true)
            textSelectOperateLayout.setSelectState(true)
            refreshLayout.isEnableRefresh = false
            textSelectOperateLayout.setSelectedContainerLayoutPosition(x, lineBottomY, currentLineHeight)
        } catch (e: Exception) {
            formatContentView.cancelSelection()
            e.printStackTrace()
        }
    }

    fun hideTextSelectLayout() {
        formatContentView.cancelSelection()
        textSelectOperateLayout.setTextOperateViewShowState(false)
        textSelectOperateLayout.setSelectState(false)
        refreshLayout.isEnableRefresh = true
        previousSelectedStartWord = -1
        formatContentView.isShowFirstSelectedDot = true
        formatContentView.isShowLastSelectedDot = true
    }

    fun isTextSelectShow(): Boolean {
        return textSelectOperateLayout.visibility == VISIBLE
    }

    fun setPageString(pageString: String?) {
        pageNumberTextView.text = pageString
    }

    private fun getLinkWord(x: Float, y: Float): Word? {
        val margin = ContentUtils.contentHorizonMargin
        val lineY = formatContentView.getLinePosition(y)
        if (lineY < 0) {
            return null
        }
        return controllerHelper.textPageList.findWord(lineY, x - margin)
    }

    private fun getImage(x: Float, y: Float): Bitmap? {
        val lineY = formatContentView.getLinePosition(y)
        if (lineY < 0) {
            return null
        }
        val endPageY = controllerHelper.getPageLastLineIndex(indexInChapter)
        return if (lineY in 0..endPageY) {
            controllerHelper.textPageList[lineY].getImage(x)
        } else {
            null
        }
    }

    override fun onCancelHighlight() {
        refreshLayout.isEnableRefresh = true
    }
}
