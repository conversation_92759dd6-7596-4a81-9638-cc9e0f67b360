package com.wedevote.wdbook.ui.user

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import com.aquila.lib.layout.SmartRefreshLayout
import com.aquila.lib.layout.api.RefreshLayout
import com.aquila.lib.layout.listener.OnRefreshListener
import com.aquila.lib.widget.view.DotView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.base.BaseRootFragment
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.UserInfoEntity
import com.wedevote.wdbook.entity.coupon.CouponStatus

import com.wedevote.wdbook.tools.event.OnChangeAccountSuccess
import com.wedevote.wdbook.tools.event.OnFavoriteEvent
import com.wedevote.wdbook.tools.event.OnFeedbackReadEvent
import com.wedevote.wdbook.tools.event.OnLoginEvent
import com.wedevote.wdbook.tools.event.OnNotificationReadEvent
import com.wedevote.wdbook.tools.event.OnSyncNoteFinish
import com.wedevote.wdbook.tools.event.OnSyncPurchasedDataFinish
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.ImageLoadUtil
import com.wedevote.wdbook.tools.util.PictureUtil
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.tools.util.getFunctionInfo
import com.wedevote.wdbook.ui.EditAccountActivity
import com.wedevote.wdbook.ui.SettingActivity
import com.wedevote.wdbook.ui.account.LoginActivity
import com.wedevote.wdbook.ui.account.OnLoginResultCallBack
import com.wedevote.wdbook.ui.account.OrderListActivity
import com.wedevote.wdbook.ui.account.SSOLoginActivity
import com.wedevote.wdbook.ui.account.register.RegisterActivity
import com.wedevote.wdbook.ui.service.SyncDataService
import com.wedevote.wdbook.ui.user.feedback.HelpCenterActivity
import com.wedevote.wdbook.ui.user.notification.NotificationCenterActivity
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
import com.wedevote.wdbook.ui.widgets.WidgetMineItemLayout
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/***
 * @date 创建时间 2020/4/8 17:57
 * <AUTHOR> W.YuLong
 * @description
 */
class HomeMineFragment : BaseRootFragment(), View.OnClickListener,
    OnRefreshListener/*, BaseViewInterface */ {
    lateinit var avatarImageView: ImageView
    lateinit var notificationImageView: ImageView
    lateinit var notificationCountDotView: DotView
    lateinit var nickNameTextView: TextView
    lateinit var boughtBookLayout: WidgetMineItemLayout
    lateinit var orderItemLayout: WidgetMineItemLayout
    lateinit var favoriteItemLayout: WidgetMineItemLayout
    lateinit var settingLayout: WidgetMineItemLayout
    lateinit var feedbackLayout: WidgetMineItemLayout
    lateinit var noteCountLayout: WidgetMineItemLayout
    lateinit var notificationLayout: WidgetMineItemLayout
    lateinit var couponLayout: WidgetMineItemLayout
    lateinit var versionTextView: TextView
    lateinit var editAccountTextView: TextView
    lateinit var refreshLayout: SmartRefreshLayout
    lateinit var mailTextView: TextView
    lateinit var feedbackFlagView: DotView
    lateinit var notLoginLinerLayout: View
    lateinit var userInfoLinerLayout: View
    lateinit var registerButton: Button
    lateinit var loginButton: Button
    lateinit var topTitleLayout: CommTopTitleLayout

    var onNewNotificationListener: OnNewNotificationListener? = null

    private lateinit var formatCountBook: String
    private lateinit var formatCountNote: String
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        formatCountBook = getString(R.string.format_text_count_book)
        formatCountNote = getString(R.string.format_text_count_note)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val rootView = inflater.inflate(R.layout.fragment_home_mine_layout, container, false)
        initViewFromXML(rootView)
        setViewListeners()
        onRefresh(refreshLayout)

        if (APPConfig.isDisablePayment) {
            orderItemLayout.visibility = View.GONE
            boughtBookLayout.setTitle(findString(R.string.my_books))
        }
        EventBus.getDefault().register(this)
        return rootView
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun OnChangeAccountSuccessEvent(event: OnChangeAccountSuccess) {
        onRefresh(refreshLayout)
    }

    private fun setViewListeners() {
        avatarImageView.setOnClickListener(this)
        boughtBookLayout.setOnClickListener(this)
        orderItemLayout.setOnClickListener(this)
        notificationImageView.setOnClickListener(this)
        favoriteItemLayout.setOnClickListener(this)
        noteCountLayout.setOnClickListener(this)
        feedbackLayout.setOnClickListener(this)
        settingLayout.setOnClickListener(this)
        refreshLayout.setOnRefreshListener(this)
        mailTextView.setOnClickListener(this)
        couponLayout.setOnClickListener(this)
        registerButton.setOnClickListener(this)
        loginButton.setOnClickListener(this)
        editAccountTextView.setOnClickListener(this)
    }

    private fun initViewFromXML(rootView: View) {
        mailTextView = rootView.findViewById(R.id.mine_app_mail)
        refreshLayout = rootView.findViewById(R.id.mine_root_container_Layout)
        avatarImageView = rootView.findViewById(R.id.mine_avatar_ImageView)
        notificationImageView = rootView.findViewById(R.id.home_mine_notification_ImageView)
        notificationCountDotView = rootView.findViewById(R.id.home_mine_notification_count_DotView)
        couponLayout = rootView.findViewById(R.id.mine_coupon_Layout)
        nickNameTextView = rootView.findViewById(R.id.mine_nickname_TextView)
        boughtBookLayout = rootView.findViewById(R.id.mine_book_Layout)
        favoriteItemLayout = rootView.findViewById(R.id.mine_favorite_Layout)
        orderItemLayout = rootView.findViewById(R.id.mine_order_Layout)
        noteCountLayout = rootView.findViewById(R.id.mine_note_count_Layout)
        settingLayout = rootView.findViewById(R.id.mine_setting_ItemLayout)
        versionTextView = rootView.findViewById(R.id.mine_app_version_TextView)
        editAccountTextView = rootView.findViewById(R.id.mine_edit_account_TextView)
        feedbackLayout = rootView.findViewById(R.id.mine_feedback_ItemLayout)
        feedbackFlagView = rootView.findViewById(R.id.mine_feedback_flag_DotView)
        notLoginLinerLayout = rootView.findViewById(R.id.mine_not_login_LinerLayout)
        userInfoLinerLayout = rootView.findViewById(R.id.mine_user_info_LinerLayout)
        registerButton = rootView.findViewById(R.id.sso_register_Button)
        loginButton = rootView.findViewById(R.id.sso_login_Button)
        topTitleLayout = rootView.findViewById(R.id.home_mine_top_title_layout)

        topTitleLayout.setLeftTitle(getString(R.string.title_home_mine))
        versionTextView.text = ("V${APPUtil.getVersionName()}")
    }

    fun checkLogin(block: (isSuccess: Boolean) -> Unit) {
        if (!SDKSingleton.sessionBl.isLogin()) {
            SSOLoginActivity.checkAndGotoLogin(
                requireActivity(), callBack = object : OnLoginResultCallBack {
                    override fun onLoginResult(isSuccess: Boolean) {
                        block(isSuccess)
                    }
                }
            )
        } else {
            block(true)
        }
    }

    override fun onClick(v: View?) {
        when (v) {
            feedbackLayout -> {
                val intent = Intent(requireActivity(), HelpCenterActivity::class.java)
                startActivity(intent)
            }
            settingLayout -> {
                startActivity(Intent(activity, SettingActivity::class.java))
            }
            loginButton -> {
                startActivity(Intent(activity, LoginActivity::class.java))
            }
            registerButton -> {
                startActivity(Intent(activity, RegisterActivity::class.java))
            }
            editAccountTextView -> {
                startActivity(Intent(activity, EditAccountActivity::class.java))
            }
            else -> {
                checkLogin {
                    if (it) {
                        doNeedLoginAction(v)
                    }
                }
            }
        }
    }

    /*这里的点击操作是需要在登录状态下*/
    private fun doNeedLoginAction(v: View?) {
        when (v) {
            favoriteItemLayout -> {
                val intent = Intent(requireActivity(), FavoriteBookListActivity::class.java)
                startActivity(intent)
            }
            notificationImageView -> {
                val intent = Intent(requireActivity(), NotificationCenterActivity::class.java)
                startActivity(intent)
            }
            couponLayout -> {
                val intent = Intent(requireActivity(), CouponCenterActivity::class.java)
                startActivity(intent)
            }
            orderItemLayout -> {
                val intent = Intent(activity, OrderListActivity::class.java)
                startActivity(intent)
            }
            boughtBookLayout -> {
                val intent = Intent(activity, PurchasedBookListActivity::class.java)
                startActivity(intent)
            }
            noteCountLayout -> {
                startActivity(Intent(activity, BookNoteCountActivity::class.java))
            }
            mailTextView -> {
            }
            avatarImageView -> {
            }
        }
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        if (!hidden) {
            if (SDKSingleton.sessionBl.isLogin()) {
                MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
                    SDKSingleton.userBl.getUserInfoEntity()?.also {
                        initUI(it)
                    }
                }
            }
        }
    }

    companion object {
        var userInfoEntity:UserInfoEntity? = null
    }

    fun initUI(userInfoEntity: UserInfoEntity) {
        try {
            HomeMineFragment.userInfoEntity = userInfoEntity
            notLoginLinerLayout.visibility = View.GONE
            userInfoLinerLayout.visibility = View.VISIBLE
            PictureUtil.loadCircleImageWithDefault(avatarImageView, userInfoEntity.avatarPath, R.drawable.ic_default_avatar)
            nickNameTextView.text = userInfoEntity.nickname
            boughtBookLayout.setDataText(formatCountBook.format(SDKSingleton.dbWrapBl.getPurchasedCount()))
        } catch (e: Exception) {
            e.printStackTrace()
            SDKSingleton.loggerBl.handleThrowable(e, getFunctionInfo())
        }
    }

    fun initNoteCountUI(count: Long) {
        if (!SDKSingleton.sessionBl.isLogin()) {
            noteCountLayout.dataTextView.visibility = View.GONE
            return
        }
        if (SyncDataService.isInSyncProgress) {
            noteCountLayout.dataTextView.visibility = View.VISIBLE
            noteCountLayout.dataTextView.text = findString(R.string.syncing)
            return
        }

        noteCountLayout.dataTextView.visibility = View.VISIBLE
        noteCountLayout.setDataText(formatCountNote.format(count))
    }

    override fun onRefresh(layout: RefreshLayout?) {
        if (!SDKSingleton.sessionBl.isLogin()) {
            clearUserData()
            refreshLayout.finishLoadMoreAndRefresh()
            return
        }

        initNotificationStatusUI()
        MainScope().launch() {
            try {
                val statusEntity = SDKSingleton.userBl.getFeedbackStatus()
                feedbackFlagView.visibility =
                    if (statusEntity?.feedbackStatus == 1) View.VISIBLE else View.GONE

                SDKSingleton.userBl.getUserInfoEntity(true)?.let {
                    initUI(it)
                }
                SDKSingleton.syncBl.syncUserData()
                val count = SDKSingleton.userBl.getFavoriteCount()
                favoriteItemLayout.setDataText("%s本".format(count))

                val couponCount = SDKSingleton.userBl.getCouponCount(CouponStatus.CAN_USE.status)
                couponLayout.setDataText("${couponCount?.count ?: 0}")

                onNewNotificationListener?.onNewUnReadMessage(
                    feedbackFlagView.visibility == View.VISIBLE || notificationCountDotView.visibility == View.VISIBLE
                )
            } catch (exception: Throwable) {
                exception.printStackTrace()
            }
        }

        SDKSingleton.dbWrapBl.getAllNoteCount().let {
            initNoteCountUI(it)
        }
        refreshLayout.finishLoadMoreAndRefresh()
    }

    private fun clearUserData() {
        favoriteItemLayout.setDataText("")
        noteCountLayout.dataTextView.visibility = View.GONE
        boughtBookLayout.setDataText("")
        couponLayout.setDataText("")
        nickNameTextView.text = ""
        ImageLoadUtil.loadCircleImageWithDefault(
            avatarImageView,
            "",
            R.drawable.ic_default_avatar
        )
        notLoginLinerLayout.visibility = View.VISIBLE
        userInfoLinerLayout.visibility = View.GONE
    }


    private fun initNotificationStatusUI() {
        if (SDKSingleton.sessionBl.isLogin()) {
            MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
                val notificationStatusList = SDKSingleton.userBl.getNotificationStatus()
                var unreadCount = 0
                if (!notificationStatusList.isNullOrEmpty()) {
                    for (entity in notificationStatusList) {
                        unreadCount += entity.unreadCount
                    }
                }
                notificationCountDotView.visibility = if (unreadCount > 0) View.VISIBLE else View.GONE
                onNewNotificationListener?.onNewUnReadMessage(
                    feedbackFlagView.visibility == View.VISIBLE || notificationCountDotView.visibility == View.VISIBLE,
                )
            }
        } else {
            notificationCountDotView.visibility = View.GONE
        }
    }

    override fun onResume() {
        super.onResume()
        initNotificationStatusUI()
        if (!SDKSingleton.sessionBl.isLogin()) {
            clearUserData()
        } else {
            onRefresh(refreshLayout)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun doOnSyncShelfFinish(event: OnSyncPurchasedDataFinish) {
        boughtBookLayout.setDataText(formatCountBook.format(SDKSingleton.dbWrapBl.getPurchasedCount()))
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun doOnSyncNoteFinish(event: OnSyncNoteFinish) {
        initNoteCountUI(SDKSingleton.dbWrapBl.getAllNoteCount())
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onFeedbackReadEvent(onFeedbackReadEvent: OnFeedbackReadEvent) {
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            val statusEntity = SDKSingleton.userBl.getFeedbackStatus()
            feedbackFlagView.visibility = if (statusEntity?.feedbackStatus == 1) View.VISIBLE else View.GONE
            onNewNotificationListener?.onNewUnReadMessage(
                feedbackFlagView.visibility == View.VISIBLE || notificationCountDotView.visibility == View.VISIBLE
            )
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onNotificationReadEvent(event: OnNotificationReadEvent) {
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            val statusEntity = SDKSingleton.userBl.getFeedbackStatus()
            feedbackFlagView.visibility = if (statusEntity?.feedbackStatus == 1) View.VISIBLE else View.GONE
            onNewNotificationListener?.onNewUnReadMessage(
                feedbackFlagView.visibility == View.VISIBLE || notificationCountDotView.visibility == View.VISIBLE
            )
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onFavoriteChangeEvent(event: OnFavoriteEvent) {
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            val count = SDKSingleton.userBl.getFavoriteCount()
            favoriteItemLayout.setDataText("%s本".format(count))
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun doOnLoginEvent(event: OnLoginEvent) {
        notLoginLinerLayout.visibility = View.GONE
        userInfoLinerLayout.visibility = View.VISIBLE
        onRefresh(refreshLayout)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        EventBus.getDefault().unregister(this)
    }
}

/***
 *@date 创建时间 2022/8/4 10:50
 *<AUTHOR> W.YuLong
 *@description
 */
interface OnNewNotificationListener {
    fun onNewUnReadMessage(haveUnread: Boolean)
}
