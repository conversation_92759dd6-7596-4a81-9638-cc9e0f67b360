package com.wedevote.wdbook.ui.home.microwidget

import android.view.ViewGroup
import com.aquila.lib.base.BaseRecycleAdapter
import com.wedevote.wdbook.entity.store.ProductEntity

/***
 *@date 创建时间 2020/5/12 17:53
 *<AUTHOR> <PERSON><PERSON>
 *@description
 */
class BookItemRecyclerAdapter : BaseRecycleAdapter<ProductEntity, BookProductViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BookProductViewHolder {
        return BookProductViewHolder(parent, viewType)
    }

    override fun getItemViewType(position: Int): Int {
        return BookProductViewHolder.TYPE_PRODUCT_LIST
    }

    fun updateProductPurchaseStatus(productId: Long) {
        if (!dataList.isNullOrEmpty()) {
            var position = 0
            for (data in dataList!!) {
                if (data.productId == productId) {
                    data.purchased = 1
                    break
                }
                position++
            }
            notifyItemChanged(position)
        }
    }
}
