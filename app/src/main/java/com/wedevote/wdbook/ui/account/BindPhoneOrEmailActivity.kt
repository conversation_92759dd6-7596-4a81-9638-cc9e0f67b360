package com.wedevote.wdbook.ui.account

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.FrameLayout
import android.widget.TextView
import com.aquila.lib.tools.singleton.SPSingleton.Companion.get
import com.aquila.lib.tools.util.ToastUtil
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.exception.ApiException
import com.wedevote.wdbook.exception.ErrorInfo
import com.wedevote.wdbook.tools.event.OnChangeAccountSuccess
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.NetWorkUtils
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.ui.account.register.RegisterLayoutManager
import com.wedevote.wdbook.ui.dialogs.RegisterTipDialog
import com.wedevote.wdbook.ui.user.HomeMineFragment
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus

/***
 *@date 创建时间 2023/5/11
 *<AUTHOR> John.Qian
 *@description  绑定手机或邮箱
 */
class BindPhoneOrEmailActivity : RootActivity(), View.OnClickListener {
    private lateinit var backView: View
    private lateinit var contentLayout: FrameLayout
    private lateinit var nextTextView: TextView
    private lateinit var titleTextView: TextView
    private var registerLayoutManager: RegisterLayoutManager? = null
    private var mType = -1

    private fun findViewByIdFromXML() {
        backView = findViewById(R.id.register_back_View)
        contentLayout = findViewById(R.id.register_content_layout)
        nextTextView = findViewById(R.id.register_next_TextView)
        nextTextView.setText(R.string.finish)
        titleTextView = findViewById<TextView>(R.id.register_login_title_TextView)
    }

    public override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val rootView = layoutInflater.inflate(R.layout.activity_bind_phone_or_email, null)
        setContentView(rootView)
        findViewByIdFromXML()
        setViewCLickListeners()
        mType = intent.getIntExtra(IntentConstants.EXTRA_Account_Security_Type, -1)
        registerLayoutManager = RegisterLayoutManager(this)
        registerLayoutManager!!.resetVerificationCodeTimer()
        registerLayoutManager!!.isBindPhoneOrEmail = true
        contentLayout.addView(registerLayoutManager!!.rootView)
        if (mType == RegisterLayoutManager.BIND_MODE_EMAIL) {
            if (HomeMineFragment.userInfoEntity?.email.isNullOrEmpty()) {
                titleTextView.setText(R.string.bind_email)
            } else {
                titleTextView.setText(R.string.replace_bind_email)
            }
            registerLayoutManager?.setRegisterType(RegisterLayoutManager.REGISTER_MODE_EMAIL)
        } else {
            if (HomeMineFragment.userInfoEntity?.mobile.isNullOrEmpty()) {
                titleTextView.setText(R.string.bind_phone)
            } else {
                titleTextView.setText(R.string.replace_bind_phone)
            }
            registerLayoutManager?.setRegisterType(RegisterLayoutManager.REGISTER_MODE_MOBILE)
        }
        registerLayoutManager!!.setOnButtonStateChangeListener {
            nextTextView.isEnabled = it
        }
    }

    private fun setViewCLickListeners() {
        backView.setOnClickListener(this)
        nextTextView.setOnClickListener(this)
    }

    override fun onClick(v: View) {
        if (v === backView) {
            onBackPressed()
        } else if (v === nextTextView) {
            if (!NetWorkUtils.isNetworkAvailable()) {
                NetWorkUtils.showTipDialog(this, getString(R.string.no_network_connect))
                return
            }
            if (registerLayoutManager!!.checkInputFormat()) {
                MainScope().launch {
                    try {
                        APPUtil.showLoadingDialog(this@BindPhoneOrEmailActivity)
                        var account = registerLayoutManager!!.accountEditText!!.text.toString()
                        var verificationCode =
                            registerLayoutManager!!.verifyEditText!!.text.toString()
                        if (mType == RegisterLayoutManager.BIND_MODE_EMAIL) {
                            SDKSingleton.userBl.bindingEmail(account, verificationCode)
                        } else {
                            account = UserAccountManager.getCountryCode(registerLayoutManager!!.countryCodeTextView) + account
                            SDKSingleton.userBl.bindingMobile(account, verificationCode)
                        }
                        APPUtil.dismissLoadingDialog(this@BindPhoneOrEmailActivity)
                        var tipDialog = RegisterTipDialog(this@BindPhoneOrEmailActivity)
                        tipDialog.setOnDismissListener{
                            MainScope().launch {
                                EventBus.getDefault().post(OnChangeAccountSuccess())
                                finish()
                            }
                        }
                        tipDialog.show()
                        if (mType == RegisterLayoutManager.BIND_MODE_EMAIL) {
                            tipDialog.setTitleText(getString(R.string.email_change_success))
                            tipDialog.setContentText(getString(R.string.new_email_apply_to_bible_and_book))
                        } else {
                            tipDialog.setTitleText(getString(R.string.mobile_change_success))
                            tipDialog.setContentText(getString(R.string.new_phone_apply_to_bible_and_book))
                        }
                    } catch (exception: Throwable) {
                        APPUtil.dismissLoadingDialog(this@BindPhoneOrEmailActivity)
                        if (exception is ApiException) {
                            when (exception.code) {
                                ErrorInfo.UserExists.code -> {
                                    if (mType == RegisterLayoutManager.BIND_MODE_EMAIL) {
                                        registerLayoutManager!!.setErrorTip(
                                            true,
                                            getString(R.string.email_already_exist)
                                        )
                                    } else {
                                        registerLayoutManager!!.setErrorTip(
                                            true,
                                            getString(R.string.phone_number_already_exist)
                                        )
                                    }
                                }
                                ErrorInfo.InvalidVerificationCode.code -> {
                                    APPUtil.showTipDialog(
                                        this@BindPhoneOrEmailActivity,
                                        getString(R.string.warm_prompt_title),
                                        getString(R.string.invalid_verification_code)
                                    )
                                }
                                else -> {
                                    ToastUtil.showToastShort(exception.message)
                                }
                            }
                        } else {
                            ExceptionHandler.handleException(exception)
                        }
                    }
                }
            }
        }
    }

    private fun goNextStep() {
        if (registerLayoutManager!!.registerNextStep()) {
            nextTextView.setText(R.string.reset_password)
            registerLayoutManager!!.checkRegisterButtonState()
        }
    }

    override fun onBackPressed() {
        if (registerLayoutManager!!.checkingPassword) {
            registerLayoutManager!!.registerPrevStep()
            nextTextView.setText(R.string.next_step)
            registerLayoutManager!!.checkNextButtonState()
            registerLayoutManager!!.verifyCodeTitleTextView!!.setText(R.string.input_verify_code)
        } else {
            super.onBackPressed()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == IntentConstants.INTENT_RESULT_COUNTRY_CODE && data != null) {
            val countryCode = data.getStringExtra(IntentConstants.Extra_countryCode)
            registerLayoutManager!!.setCountryCodeText(countryCode)
            get().putString(SPKeyDefine.SP_defaultCountry, countryCode)
        }
    }
}