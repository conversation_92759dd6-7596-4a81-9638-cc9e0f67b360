package com.wedevote.wdbook.ui.home.microwidget

import android.graphics.Color
import android.text.Html
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.log.KLog
import com.aquila.lib.tools.util.ToastUtil
import com.aquila.lib.widget.view.AdaptiveImageView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.DownloadStatus
import com.wedevote.wdbook.entity.PurchasedResourceEntity
import com.wedevote.wdbook.entity.resource.ResourceDownloadInfo
import com.wedevote.wdbook.entity.store.ProductDetailEntity
import com.wedevote.wdbook.entity.store.ProductEntity
import com.wedevote.wdbook.tools.define.PurchaseStatus
import com.wedevote.wdbook.tools.download.DownloaderEngine
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.PictureUtil
import com.wedevote.wdbook.tools.util.ImageLoadUtil
import com.wedevote.wdbook.tools.util.NetWorkUtils
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.tools.util.initAuthorsName
import com.wedevote.wdbook.tools.util.parseHtmlTag
import com.wedevote.wdbook.ui.read.BookReadActivity
import com.wedevote.wdbook.ui.store.BookDetailActivity
import com.wedevote.wdbook.ui.store.DownloadOption
import com.wedevote.wdbook.ui.widgets.ProgressButton
import java.io.File

/***
 *@date 创建时间 2020/5/12 17:53
 *<AUTHOR> W.YuLong
 *@description 书籍的列表UI
 */
class BookProductViewHolder(parent: ViewGroup, var itemType: Int, var downloadEngine: DownloaderEngine? = null) :
    BaseViewHolder(parent, R.layout.item_micro_store_book_list_layout) {
    val coverImageView: AdaptiveImageView = itemView.findViewById(R.id.item_book_info_cover_ImageView)
    val purchaseFlagImageView: ImageView = itemView.findViewById(R.id.item_book_info_purchase_flag_ImageView)
    val nameTextView: TextView = itemView.findViewById(R.id.item_book_info_name_TextView)
    val authorTextView: TextView = itemView.findViewById(R.id.item_book_info_author_TextView)
    val originalPriceTextView: TextView = itemView.findViewById(R.id.item_book_info_original_price_TextView)
    val discountPriceTextView: TextView = itemView.findViewById(R.id.item_book_info_discount_price_TextView)
    val readButton: ProgressButton = itemView.findViewById(R.id.item_book_read_Button)

    companion object {
        const val TYPE_PRODUCT_LIST = 0
        const val TYPE_PRODUCT_DETAIL = 1
        const val TYPE_PRODUCT_ORDER = 2

        var color_E9973E = Color.parseColor("#E9973E")
        var color_FFE8E8 = Color.parseColor("#FFE8E8")
    }

    override fun <T> initUIData(t: T) {
        when (itemType) {
            TYPE_PRODUCT_LIST -> {
                initListUI(t)
            }
            TYPE_PRODUCT_DETAIL -> {
                initDetailUI(t)
            }
            TYPE_PRODUCT_ORDER -> {
                originalPriceTextView.visibility = View.GONE
                discountPriceTextView.visibility = View.INVISIBLE
                t as PurchasedResourceEntity
                // KLog.e("initAuthorsName(t.authorList):"+initAuthorsName(t.resourceBean?.authors))
                t.let { purchaseResourceEntity ->
                    nameTextView.text = Html.fromHtml(parseHtmlTag(purchaseResourceEntity.name))
                    authorTextView.text = Html.fromHtml(initAuthorsName(purchaseResourceEntity.authorList))
                    PictureUtil.loadImage(coverImageView, purchaseResourceEntity.cover!!)
                    initReadButtonShow(purchaseResourceEntity.resourceDownloadInfo)
                }
            }
        }
    }

    private fun <T> initListUI(t: T) {
        val productEntity = t as ProductEntity
        if (!t.activitiesList.isNullOrEmpty()) {
            t.price = t.activitiesList!![0]!!.amount
            t.priceCNY = t.activitiesList!![0]!!.amountCNY
            t.discount = t.activitiesList!![0]!!.discount
        }
        readButton.visibility = ViewGroup.GONE
        PictureUtil.loadImage(coverImageView, t.cover)
        nameTextView.text = Html.fromHtml(parseHtmlTag(t.title))
        authorTextView.text = Html.fromHtml(initAuthorsName(t.authorList))
        originalPriceTextView.visibility = View.VISIBLE
        discountPriceTextView.visibility = View.VISIBLE

        if (productEntity.purchased == 0) {
            purchaseFlagImageView.visibility = View.GONE
        } else {
            purchaseFlagImageView.visibility = View.VISIBLE
        }

        originalPriceTextView.text = APPUtil.setCurrentPrice(t.currency, t.price, t.priceCNY, t.discount, !t.activitiesList.isNullOrEmpty())
        discountPriceTextView.text = APPUtil.setOriginalPrice(t.currency, t.originalPrice)
        itemView.setOnClickListener { v ->
            BookDetailActivity.gotoBookDetail(v.context, productEntity.productId)
        }
    }

    private fun <T> initDetailUI(t: T) {
        t as ProductDetailEntity
        if (!t.activitiesList.isNullOrEmpty()) {
            t.price = t.activitiesList!![0]!!.amount
            t.priceCNY = t.activitiesList!![0]!!.amountCNY
            t.discount = t.activitiesList!![0]!!.discount
        }
        readButton.visibility = ViewGroup.VISIBLE
        authorTextView.text = Html.fromHtml(initAuthorsName(t.authorList))
        PictureUtil.loadImage(coverImageView, t.cover)
        nameTextView.text = t.title
        originalPriceTextView.visibility = View.VISIBLE
        discountPriceTextView.visibility = View.VISIBLE
        originalPriceTextView.text = APPUtil.setCurrentPrice(t.currency, t.price, t.priceCNY, t.discount)
        discountPriceTextView.text = APPUtil.setOriginalPrice(t.currency, t.originalPrice)
        purchaseFlagImageView.visibility = if (t.purchased == PurchaseStatus.UNDONE.value) View.GONE else View.VISIBLE

        itemView.setOnClickListener {
            KLog.d("点击事件")
            BookDetailActivity.gotoBookDetail(it.context, t.productId)
        }

        readButton.visibility = View.GONE
    }

    fun initReadButtonShow(resourceDownloadInfo: ResourceDownloadInfo) {
        readButton.visibility = View.VISIBLE
        when (resourceDownloadInfo.downloadStatus) {
            DownloadStatus.WAIT -> {
                readButton.needShowProgress = false
                readButton.setTextColor(color_E9973E)
                readButton.setText(R.string.download)
                readButton.currentType = ProgressButton.BTN_STATUS_NEED_DOWNLOAD
                readButton.invalidate()
            }
            DownloadStatus.COMPLETE -> {
                readButton.setTextColor(color_E9973E)
                readButton.needShowProgress = false
                if (File(resourceDownloadInfo.getActualFilePath()).exists()) {
                    readButton.currentType = ProgressButton.BTN_STATUS_READ
                    readButton.setText(R.string.read)
                } else {
                    readButton.setText(R.string.download)
                    readButton.currentType = ProgressButton.BTN_STATUS_NEED_DOWNLOAD
                }
                readButton.invalidate()
            }
            DownloadStatus.DOWNLOADING, DownloadStatus.PAUSE -> {
                readButton.setTextColor(color_FFE8E8)
                var downloadSize = 0L
                var tmpFile = File(resourceDownloadInfo.getTempFilePath())
                if (tmpFile.exists()) {
                    downloadSize = tmpFile.length()
                }
                readButton.needShowProgress = true
                if (resourceDownloadInfo.downloadInfo == null || resourceDownloadInfo.fileSize == 0L) {
                    readButton.setButtonProgress(0f)
                } else {
                    readButton.setButtonProgress(downloadSize * 100f / resourceDownloadInfo.fileSize)
                }
                readButton.setCurrentShowType(ProgressButton.BTN_STATUS_NEED_DOWNLOAD)
                readButton.postInvalidate()
            }
            DownloadStatus.UPDATE -> {
                readButton.setTextColor(color_E9973E)
                readButton.setText(R.string.update)
                readButton.needShowProgress = false
                readButton.currentType = ProgressButton.BTN_STATUS_NEED_DOWNLOAD
            }
            else -> {
                readButton.setText(R.string.download)
                readButton.needShowProgress = false
                readButton.currentType = ProgressButton.BTN_STATUS_NEED_DOWNLOAD
                readButton.invalidate()
            }
        }

        readButton.setOnClickListener {
//            if (!SDKSingleton.sessionBl.isLogin()) {
//                SSOLoginActivity.gotoLogin(it.context as Activity)
//                return@setOnClickListener
//            }

            when (readButton.currentType) {
                ProgressButton.BTN_STATUS_NEED_DOWNLOAD -> {
                    if (resourceDownloadInfo.fileId.isNullOrEmpty()) {
//                        ToastUtil.showToastShort(R.string.data_exception_prompt)
                        NetWorkUtils.showTipDialog(itemView.context, itemView.context.getString(R.string.no_network_connect))
                        return@setOnClickListener
                    }
                    if (!NetWorkUtils.isNetworkAvailable()) {
                        downloadEngine?.let { it1 -> NetWorkUtils.showTipDialog(it1.activity, it1.activity.getString(R.string.no_network_connect)) }
                        return@setOnClickListener
                    }
                    if (!NetWorkUtils.isNetworkAvailable()) {
                        downloadEngine?.let { it1 -> NetWorkUtils.showTipDialog(it1.activity, it1.activity.getString(R.string.no_network_connect)) }
                        return@setOnClickListener
                    }
                    resourceDownloadInfo.fileId?.let { downloadEngine?.readyDownloadOnlyByFileId(it) }
                }
                ProgressButton.BTN_STATUS_READ -> {
//                    downloadEntity = SDKSingleton.downloadBl.getDownloadFileEntity(resourceDownloadInfo)
                    // 下载完成之后可以重新获取一下
                    var existResourceDownloadInfo = SDKSingleton.dbWrapBl.getResourceDownloadInfo(resourceDownloadInfo.resourceId)

                    BookReadActivity.gotoBookReadActivity(
                        it.context,
                        existResourceDownloadInfo!!.getActualFilePath(),
                        existResourceDownloadInfo.downloadInfo?.fileId,
                        existResourceDownloadInfo.resourceId,
                    )
                }
            }
        }
    }

    fun updateButtonShow(option: DownloadOption) {
        when (option.downloadStatus) {
            DownloadStatus.WAIT -> {
                readButton.currentType = ProgressButton.BTN_STATUS_NEED_DOWNLOAD
                readButton.setTextColor(color_E9973E)
                readButton.setText(R.string.wait_download)
            }
            DownloadStatus.BEGIN -> {
                readButton.needShowProgress = true
                readButton.currentType = ProgressButton.BTN_STATUS_NEED_DOWNLOAD
                readButton.setTextColor(color_FFE8E8)
            }
            DownloadStatus.DOWNLOADING -> {
                readButton.setTextColor(color_FFE8E8)
                readButton.needShowProgress = true
                readButton.currentType = ProgressButton.BTN_STATUS_NEED_DOWNLOAD
                readButton.setButtonProgress(option.getProgress())
            }
            DownloadStatus.PAUSE -> {
                readButton.setTextColor(color_FFE8E8)
                readButton.needShowProgress = true
                readButton.currentType = ProgressButton.BTN_STATUS_NEED_DOWNLOAD
                readButton.text = findString(R.string.paused) + readButton.text
            }
            DownloadStatus.COMPLETE -> {
                readButton.needShowProgress = false
                readButton.setTextColor(color_E9973E)
                readButton.currentType = ProgressButton.BTN_STATUS_READ
                readButton.setText(R.string.read)
            }
            else -> {
                readButton.needShowProgress = false
                readButton.setText(R.string.download)
                readButton.setTextColor(color_E9973E)
                readButton.currentType = ProgressButton.BTN_STATUS_NEED_DOWNLOAD
            }
        }
        readButton.invalidate()
    }
}
