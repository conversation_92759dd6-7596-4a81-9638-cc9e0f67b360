package com.wedevote.wdbook.ui.user

import android.os.Bundle
import android.view.View
import android.view.View.GONE
import android.view.View.OnClickListener
import android.view.View.VISIBLE
import com.aquila.lib.tools.singleton.SPSingleton
import com.aquila.lib.widget.group.GroupImageTextLayout
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.tools.event.RestartAllActivityEvent
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.SPKeyDefine
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.*

/***
 * @date 创建时间 2022/6/1 22:07
 * <AUTHOR> W.YuLong
 * @description
 */
class LanguageSelectActivity : RootActivity(), OnClickListener {
    private lateinit var simpleChineseLayout: GroupImageTextLayout
    private lateinit var traditionalChineseLayout: GroupImageTextLayout

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_language_select_layout)
        simpleChineseLayout = findViewById(R.id.language_simple_Layout)
        traditionalChineseLayout = findViewById(R.id.language_traditional_layout)
        simpleChineseLayout.setOnClickListener(this)
        traditionalChineseLayout.setOnClickListener(this)

        initCurrentLanguageUI()
    }

    private fun initCurrentLanguageUI() {
        if (APPUtil.currentLocale == Locale.SIMPLIFIED_CHINESE) {
            simpleChineseLayout.imageView.visibility = VISIBLE
            traditionalChineseLayout.imageView.visibility = GONE
        } else {
            simpleChineseLayout.imageView.visibility = GONE
            traditionalChineseLayout.imageView.visibility = VISIBLE
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    override fun doChangeThemeEvent(event: RestartAllActivityEvent) {
        onBackPressed()
    }

    override fun onClick(v: View?) {
        when (v) {
            simpleChineseLayout -> {
                if (APPUtil.currentLocale == Locale.SIMPLIFIED_CHINESE) {
                    return
                }
                SPSingleton.get().putInt(SPKeyDefine.SP_CurrentLanguage, 0)
                APPUtil.updateCurrentLocale(this, Locale.SIMPLIFIED_CHINESE)
                EventBus.getDefault().post(RestartAllActivityEvent(true))
                initCurrentLanguageUI()
            }

            traditionalChineseLayout -> {
                if (APPUtil.currentLocale == Locale.TRADITIONAL_CHINESE) {
                    return
                }
                SPSingleton.get().putInt(SPKeyDefine.SP_CurrentLanguage, 1)
                APPUtil.updateCurrentLocale(this, Locale.TRADITIONAL_CHINESE)
                EventBus.getDefault().post(RestartAllActivityEvent(true))
                initCurrentLanguageUI()
            }
        }
    }
}