package com.wedevote.wdbook.ui.account.register

import android.content.Intent
import android.os.Bundle
import android.text.InputType
import android.view.View
import android.view.WindowManager
import android.widget.CheckBox
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.aquila.lib.base.OnViewClickListener
import com.aquila.lib.tools.singleton.SPSingleton.Companion.get
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.WrapCoroutineHelper
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.NetWorkUtils
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.ui.CommWebViewActivity
import com.wedevote.wdbook.ui.account.LoginManager
import com.wedevote.wdbook.ui.dialogs.AgreementTipDialog

/***
 *@date 创建时间 2023.04.13
 *<AUTHOR> John.Qian
 *@description
 */
class RegisterActivity : RootActivity(), View.OnClickListener {
    private lateinit var backView: View
    private lateinit var contentLayout: FrameLayout
    private lateinit var nextTextView: TextView
    private lateinit var titleTextView: TextView
    private lateinit var protocolTextView: TextView
    private lateinit var privacyTextView: TextView
    private lateinit var userAgreeLayout: View
    private lateinit var registerTypeTextView: TextView
    private lateinit var tipTextView: TextView
    private lateinit var registerTypeImageView: ImageView
    private lateinit var agreeCheckBox: CheckBox
    lateinit var registerTypeLayout: LinearLayout
    private var registerLayoutManager: RegisterLayoutManager? = null
    private var loginManager: LoginManager? = null
    private var tempPhone: String = ""
    private var tempEmail: String = ""

    private fun findViewByIdFromXML() {
        backView = findViewById(R.id.register_back_View)
        contentLayout = findViewById(R.id.register_content_layout)
        nextTextView = findViewById(R.id.register_next_TextView)
        nextTextView.setText(R.string.next_step)
        protocolTextView = findViewById(R.id.register_use_protocol_TextView)
        protocolTextView.setText(R.string.text_user_agreement)
        privacyTextView = findViewById(R.id.register_use_privacy_TextView)
        privacyTextView.setText(R.string.text_privacy_agreement)
        userAgreeLayout = findViewById(R.id.register_user_agree_layout)
        titleTextView = findViewById<TextView>(R.id.register_login_title_TextView)
        titleTextView.setText(R.string.use_email_register)
        registerTypeLayout = findViewById(R.id.register_type_change_Layout)
        agreeCheckBox = findViewById(R.id.register_use_agree_CheckBox)
        (findViewById<View>(R.id.register_use_description_TextView) as TextView).setText(R.string.sso_login_read_agree)
        registerTypeTextView = findViewById(R.id.register_type_change_TextView)
        tipTextView = findViewById(R.id.register_tip_TextView)
        tipTextView.visibility = View.GONE
        registerTypeImageView = findViewById(R.id.register_type_change_ImageView)
        registerTypeTextView.setText(R.string.use_phone_register)
        agreeCheckBox.isChecked = true
        registerTypeImageView.isSelected = true
    }

    public override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val rootView = layoutInflater.inflate(R.layout.activity_register, null)
        setContentView(rootView)
        findViewByIdFromXML()
        setViewCLickListeners()
        registerLayoutManager = RegisterLayoutManager(this)
        contentLayout.addView(registerLayoutManager!!.rootView)
        registerLayoutManager!!.setOnButtonStateChangeListener {
            nextTextView.isEnabled = it
        }
    }

    private fun setViewCLickListeners() {
        backView.setOnClickListener(this)
        nextTextView.setOnClickListener(this)
        protocolTextView.setOnClickListener(this)
        privacyTextView.setOnClickListener(this)
        registerTypeLayout.setOnClickListener(this)
    }

    override fun onClick(v: View) {
        if (v === backView) {
            onBackPressed()
        } else if (v === protocolTextView) {
            if (!NetWorkUtils.isNetworkAvailable()) {
                NetWorkUtils.showTipDialog(this, getString(R.string.no_network_connect))
                return
            }
            WrapCoroutineHelper.getBookAgreementURL { url ->
                CommWebViewActivity.gotoWebView(
                    this,
                    url,
                    titleName = findString(R.string.title_agreement)
                )
            }
        } else if (v === privacyTextView) {
            if (!NetWorkUtils.isNetworkAvailable()) {
                NetWorkUtils.showTipDialog(this, getString(R.string.no_network_connect))
                return
            }
            WrapCoroutineHelper.getBookPrivacyURL { url ->
                CommWebViewActivity.gotoWebView(
                    this,
                    url,
                    titleName = findString(R.string.title_privacy)
                )
            }
        } else if (v === nextTextView) {
            if (!NetWorkUtils.isNetworkAvailable()) {
                NetWorkUtils.showTipDialog(this, getString(R.string.no_network_connect))
                return
            }
            if (registerLayoutManager!!.checkingPassword) {
                registerLayoutManager!!.registerUserAccount {}
            } else {
                if (!registerLayoutManager!!.isInputCorrect) {
                    return
                }
                if (!registerLayoutManager!!.checkInputFormat()) {
                    return
                }
                if (!agreeCheckBox.isChecked) {
                    var tipDialog = AgreementTipDialog(this)
                    tipDialog.show()
                    var lp= tipDialog.window?.attributes;
                    lp!!.dimAmount=0.25f;
                    tipDialog.window?.attributes = lp
                    tipDialog.window?.clearFlags(WindowManager.LayoutParams.FLAG_BLUR_BEHIND)
                    tipDialog.window?.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)


                    tipDialog.onViewClickListener = object : OnViewClickListener {
                        override fun <T> onClickAction(v: View, str: String, t: T?) {
                            agreeCheckBox.isChecked = true
                            registerLayoutManager!!.checkVerificationCode {
                                goNextStep()
                            }
                        }
                    }
                } else {
                    registerLayoutManager!!.checkVerificationCode {
                        goNextStep()
                    }
                }
            }
        } else if (v === registerTypeLayout) {
            registerLayoutManager?.verifyEditText?.setText("")
            registerLayoutManager?.checkVerifyButtonState(true)
            if (registerLayoutManager!!.registerType == RegisterLayoutManager.REGISTER_MODE_MOBILE) {
                registerLayoutManager!!.setRegisterType(RegisterLayoutManager.REGISTER_MODE_EMAIL)
                registerTypeTextView.setText(R.string.use_phone_register)
                titleTextView.setText(R.string.use_email_register)
                tempPhone = registerLayoutManager?.accountEditText?.text?.trim().toString()
                registerLayoutManager?.accountEditText?.inputType = InputType.TYPE_TEXT_VARIATION_EMAIL_ADDRESS
                registerLayoutManager?.accountEditText?.setText(tempEmail)
                registerTypeImageView.isSelected = true
            } else {
                registerLayoutManager!!.setRegisterType(RegisterLayoutManager.REGISTER_MODE_MOBILE)
                registerTypeTextView.setText(R.string.use_email_register)
                titleTextView.setText(R.string.use_phone_register)
                tempEmail = registerLayoutManager?.accountEditText?.text?.trim().toString()
                registerLayoutManager?.accountEditText?.inputType = InputType.TYPE_CLASS_PHONE
                registerLayoutManager?.accountEditText?.setText(tempPhone)
                registerTypeImageView.isSelected = false
            }
        }
    }

    private fun goNextStep() {
        if (registerLayoutManager!!.registerNextStep()) {
            titleTextView.setText(R.string.set_password)
            userAgreeLayout.visibility = View.GONE
            registerTypeLayout.visibility = View.GONE
            tipTextView.visibility = View.VISIBLE
            nextTextView.setText(R.string.register_now)
            registerLayoutManager!!.checkRegisterButtonState()
        }
    }

    override fun onBackPressed() {
        if (registerLayoutManager!!.checkingPassword) {
            registerLayoutManager!!.registerPrevStep()
            if (registerLayoutManager!!.registerType == RegisterLayoutManager.REGISTER_MODE_MOBILE) {
                titleTextView.setText(R.string.use_email_register)
            } else {
                titleTextView.setText(R.string.use_phone_register)
            }
            userAgreeLayout.visibility = View.VISIBLE
            registerTypeLayout.visibility = View.VISIBLE
            tipTextView.visibility = View.GONE
            nextTextView.setText(R.string.next_step)
            registerLayoutManager!!.checkNextButtonState()
            registerLayoutManager!!.verifyCodeTitleTextView!!.setText(R.string.input_verify_code)
        } else {
            super.onBackPressed()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == IntentConstants.INTENT_RESULT_COUNTRY_CODE && data != null) {
            val countryCode = data.getStringExtra(IntentConstants.Extra_countryCode)
            registerLayoutManager!!.setCountryCodeText(countryCode)
            get().putString(SPKeyDefine.SP_defaultCountry, countryCode)
        }
    }
}