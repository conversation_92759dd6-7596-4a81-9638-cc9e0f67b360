package com.wedevote.wdbook.ui.home.microwidget

import android.content.Intent
import android.graphics.Color
import android.text.Html
import android.text.Spannable
import android.text.SpannableString
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StrikethroughSpan
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.log.KLog
import com.wedevote.wdbook.tools.util.ImageLoadUtil
import com.aquila.lib.widget.view.AdaptiveImageView
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.home.NewWidgetDetailEntity
import com.wedevote.wdbook.entity.home.WidgetContainerCombineEntity
import com.wedevote.wdbook.entity.store.ProductEntity
import com.wedevote.wdbook.tools.util.GsonUtil
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.PictureUtil
import com.wedevote.wdbook.tools.util.UnitFormatUtil
import com.wedevote.wdbook.tools.util.dp2px
import com.wedevote.wdbook.tools.util.findString
import com.wedevote.wdbook.tools.util.initAuthorsName
import com.wedevote.wdbook.ui.store.BookDetailActivity
import com.wedevote.wdbook.ui.store.RecommendBookListActivity
import com.wedevote.wdbook.utils.JsonUtility
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/***
 * @date 创建时间 2021/11/19 16:47
 * <AUTHOR> W.YuLong
 * @description
 */
class MicroRecommendViewHolder(parent: ViewGroup, var type: Int) :
    BaseViewHolder(parent, R.layout.micro_widget_recommend_layout),
    View.OnClickListener {
    val nameTextView: TextView = itemView.findViewById(R.id.micro_recommend_title_TextView)
    val viewAllButton: TextView = itemView.findViewById(R.id.micro_recommend_view_all_Button)
    val dataRecycleView: CustomRecyclerView = itemView.findViewById(R.id.micro_recommend_data_RecyclerView)
    var dataAdapter: RecommendAdapter = RecommendAdapter(type)

    enum class DisplayButtonType(val type: String) {
        NONE("0"),
        EXCHANGE("1"),
        VIEW_ALL("2"),
    }

    var title: String = ""
    var displayButtonType: String? = DisplayButtonType.VIEW_ALL.type
    var detailEntity: NewWidgetDetailEntity? = null
    var combineEntity: WidgetContainerCombineEntity? = null

    init {
        dataRecycleView.adapter = dataAdapter
    }

    override fun <T> initUIData(t: T) {
        combineEntity = t as WidgetContainerCombineEntity
        dataAdapter.dataSourceType = t.dataSourceMethod

        nameTextView.text = t.containerTitle
        if (!t.detailEntityList.isNullOrEmpty()) {
            detailEntity = t.detailEntityList!![0]

            if ((dataAdapter.dataList?.size ?: 0) == 0) {
                initData(detailEntity!!)
            }

            displayButtonType = detailEntity!!.paramsMap[NewWidgetDetailEntity.KEY_displayButtonType].toString()
            viewAllButton.setOnClickListener(this)
            // 显示按钮 0:不展示 1:换一换 2:全部
            when (displayButtonType) {
                DisplayButtonType.NONE.type -> {
                    viewAllButton.visibility = View.GONE
                }

                DisplayButtonType.EXCHANGE.type -> {
                    viewAllButton.setText(R.string.change)
                }

                DisplayButtonType.VIEW_ALL.type -> {
                    viewAllButton.setText(R.string.view_all)
                }
            }
        }
    }

    private fun initData(detailEntity: NewWidgetDetailEntity) {
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            val paramsStr = detailEntity.paramsMap[NewWidgetDetailEntity.KEY_requestParams]
            var subParamsMap = HashMap<String, String?>()
            if (!paramsStr.isNullOrEmpty()) {
                subParamsMap = JsonUtility.decodeFromString(paramsStr)
                when {
                    subParamsMap.containsKey("token") -> {
                        subParamsMap["token"] = SDKSingleton.sessionBl.getAccessToken()
                    }

                    subParamsMap.containsKey("page") -> {
                        subParamsMap["page"] = "1"
                    }
                }
            }

            SDKSingleton.storeBl.getRecommendDataList(
                "${detailEntity.paramsMap[NewWidgetDetailEntity.KEY_dataSource]}",
                subParamsMap,
            ).also { entity ->
                if (!entity.productList.isNullOrEmpty()) {
                    if (!isListDataEquals(entity.productList, dataAdapter.dataList)) {
                        dataAdapter.dataList = entity.productList
                    }
                }
            }
        }
    }

    override fun onClick(v: View?) {
        if (v === viewAllButton) {
            when (displayButtonType) {
                DisplayButtonType.EXCHANGE.type -> {
                    initData(detailEntity!!)
                }

                else -> {
                    val intent = Intent(v.context, RecommendBookListActivity::class.java)
                    intent.putExtra(
                        IntentConstants.EXTRA_WidgetDetailEntityJson,
                        JsonUtility.encodeToString(detailEntity),
                    )
                    intent.putExtra(IntentConstants.EXTRA_CategoryName, nameTextView.text.toString())
                    v.context.startActivity(intent)
                }
            }
        }
    }

    private fun isListDataEquals(leftList: List<ProductEntity>?, rightList: MutableList<ProductEntity>?): Boolean {
        if (leftList.isNullOrEmpty() || rightList.isNullOrEmpty()) {
            return false
        }
        return leftList.size == rightList.size &&
                GsonUtil.objectToJson(leftList) == GsonUtil.objectToJson(rightList)
    }
}

/***
 *@date 创建时间 2021/11/19 17:14
 *<AUTHOR> W.YuLong
 *@description
 */
class RecommendAdapter(var type: Int) : BaseRecycleAdapter<ProductEntity, BaseViewHolder>() {
    var dataSourceType = 1
        set(value) {
            field = value
            notifyDataSetChanged()
        }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder {
        return if (type == MicroWidgetViewFactory.HOLDER_TYPE_KEY_upDownItems) {
            Style01ViewHolder(parent)
        } else {
            Style02ViewHolder(parent, dataSourceType)
        }
    }
}

/***
 *@date 创建时间 2021/11/19 17:25
 *<AUTHOR> W.YuLong
 *@description
 */
class Style02ViewHolder(parent: ViewGroup, val dataSourceType: Int) : BaseViewHolder(parent, R.layout.cell_recommend_style_02) {
    val coverImageView: AdaptiveImageView = itemView.findViewById(R.id.recommend_style_02_cover_ImageView)
    val nameTextView: TextView = itemView.findViewById(R.id.recommend_style_02_name_TextView)
    val authorTextView: TextView = itemView.findViewById(R.id.recommend_style_02_author_TextView)
    val descTextView: TextView = itemView.findViewById(R.id.recommend_style_02_desc_TextView)
    val freeFlagTextView: TextView = itemView.findViewById(R.id.recommend_style_02_free_flag_TextView)
    val discountTextView: TextView = itemView.findViewById(R.id.recommend_style_02_discount_flag_TextView)

    val originalPriceTextView: TextView = itemView.findViewById(R.id.recommend_style_02_oringal_price_TextView)
    val discountPriceTextView: TextView = itemView.findViewById(R.id.recommend_style_02_discount_price_TextView)
    val priceContainerLayout: ViewGroup = itemView.findViewById(R.id.recommend_style_02_price_container_layout)
    val infoContainerLayout: ViewGroup = itemView.findViewById(R.id.recommend_style_02_info_container_layout)

    override fun <T> initUIData(t: T) {
        t as ProductEntity
        PictureUtil.loadImage(coverImageView, t.cover)
        nameTextView.text = t.title

        freeFlagTextView.visibility = if (t.price <= 0f) View.VISIBLE else View.GONE
        itemView.setOnClickListener {
            BookDetailActivity.gotoBookDetail(it.context, t.productId)
        }

        val discount = if (!t.activitiesList.isNullOrEmpty()) {
            t.activitiesList!![0]!!.discount
        } else {
            t.discount
        }
        if (t.price == 0f) {
            freeFlagTextView.visibility = View.VISIBLE
            discountTextView.visibility = View.GONE
        } else {
            if (discount < 1f && discount > 0f) {
                UnitFormatUtil.formatDiscountText(discount).let {
                    if (!it.isNullOrEmpty()) {
                        discountTextView.visibility = View.VISIBLE
                        discountTextView.text = it
                    }
                }
            } else {
                freeFlagTextView.visibility = View.GONE
                discountTextView.visibility = View.GONE
            }
        }

        if (dataSourceType == 1) {
            priceContainerLayout.visibility = View.GONE
            infoContainerLayout.visibility = View.VISIBLE
            authorTextView.text = initAuthorsName(t.authorList)
            descTextView.text = Html.fromHtml(t.desc)
        } else {
            UnitFormatUtil.formatDiscountText(discount).let {
                if (!it.isNullOrEmpty()) {
                    discountTextView.visibility = View.VISIBLE
                    discountTextView.text = it
                }
            }
            originalPriceTextView.text = formatOriginalPrice(t.originalPrice)
            discountPriceTextView.text = formatDiscountPrice(t)
            priceContainerLayout.visibility = View.VISIBLE
            infoContainerLayout.visibility = View.GONE
        }
    }

    fun formatDiscountPrice(entity: ProductEntity): SpannableString {
        val priceUSD = "${
            if (!entity.activitiesList.isNullOrEmpty()) {
                entity.activitiesList!![0].amount
            } else {
                entity.price
            }
        }"
        val priceArray = priceUSD.split(".")
        val priceCNY = "(${findString(R.string.about)}￥${
            if (!entity.activitiesList.isNullOrEmpty()) {
                entity.activitiesList!![0].amountCNY
            } else {
                entity.priceCNY
            }
        })"
        val priceStr = "\$$priceUSD$priceCNY"
        val sp = SpannableString("${findString(R.string.discount_price)} $priceStr")

        sp.setSpan(
            ForegroundColorSpan(coverImageView.context.resources.getColor(R.color.color_red_E33733)),
            4,
            sp.length,
            Spannable.SPAN_INCLUSIVE_INCLUSIVE
        )
        sp.setSpan(
            AbsoluteSizeSpan(dp2px(20)),
            sp.indexOf(priceArray[0]),
            sp.indexOf(priceArray[0]) + priceArray[0].length,
            Spannable.SPAN_INCLUSIVE_INCLUSIVE,
        )
        return sp
    }

    fun formatOriginalPrice(price: Float): SpannableString {
        val oriPrice = "\$$price"
        val sp = SpannableString("${findString(R.string.original_price)}：$oriPrice")
        val start = sp.indexOf(oriPrice)
        val end = start + oriPrice.length
        sp.setSpan(StrikethroughSpan(), start, end, Spannable.SPAN_INCLUSIVE_INCLUSIVE)
        return sp
    }
}

/***
 *@date 创建时间 2021/11/19 17:25
 *<AUTHOR> W.YuLong
 *@description
 */
class Style01ViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.cell_recommend_style_01) {
    val coverImageView: AdaptiveImageView = itemView.findViewById(R.id.recommend_style_01_cover_ImageView)
    val nameTextView: TextView = itemView.findViewById(R.id.recommend_style_01_name_TextView)
    val authorTextView: TextView = itemView.findViewById(R.id.recommend_style_01_author_TextView)
    val freeFlagTextView: TextView = itemView.findViewById(R.id.recommend_style_01_free_flag_TextView)
    val discountTextView: TextView = itemView.findViewById(R.id.recommend_style_01_discount_TextView)

    override fun <T> initUIData(t: T) {
        t as ProductEntity
        PictureUtil.loadImage(coverImageView, t.cover)
        nameTextView.text = t.title
        authorTextView.text = initAuthorsName(t.authorList)

        if (t.price == 0f) {
            freeFlagTextView.visibility = View.VISIBLE
            discountTextView.visibility = View.GONE
        } else {
            val discount = if (!t.activitiesList.isNullOrEmpty()) {
                t.activitiesList!![0]!!.discount
            } else {
                t.discount
            }
            if (discount < 1f && discount > 0f) {
                UnitFormatUtil.formatDiscountText(discount).let {
                    if (!it.isNullOrEmpty()) {
                        discountTextView.visibility = View.VISIBLE
                        discountTextView.text = it
                    }
                }
            } else {
                freeFlagTextView.visibility = View.GONE
                discountTextView.visibility = View.GONE
            }
        }

        itemView.setOnClickListener {
            BookDetailActivity.gotoBookDetail(it.context, t.productId)
        }
    }
}
