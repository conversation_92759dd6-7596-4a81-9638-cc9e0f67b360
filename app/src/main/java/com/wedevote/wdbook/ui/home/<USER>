package com.wedevote.wdbook.ui.home

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.lifecycle.lifecycleScope
import com.aquila.lib.layout.SmartRefreshLayout
import com.aquila.lib.layout.api.RefreshLayout
import com.aquila.lib.layout.listener.OnRefreshListener
import com.aquila.lib.log.KLog
import com.aquila.lib.tools.singleton.SPSingleton
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.BaseRootFragment
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.base.TestDeeplinkActivity
import com.wedevote.wdbook.constants.HomeType
import com.wedevote.wdbook.entity.notification.NotificationEntity
import com.wedevote.wdbook.tools.event.OnSyncNotificationMessageFinish
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.tools.util.isEqualOne
import com.wedevote.wdbook.tools.util.toJsonStr
import com.wedevote.wdbook.ui.home.microwidget.HomeMicroCommAdapter
import com.wedevote.wdbook.ui.home.microwidget.MicroWidgetViewFactory
import com.wedevote.wdbook.ui.store.BookCategoryListActivity
import com.wedevote.wdbook.ui.store.search.SearchActivity
import com.wedevote.wdbook.ui.user.notification.NotificationDetailActivity
import com.wedevote.wdbook.ui.user.cart.ShoppingCartActivity
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/***
 * @date 创建时间 2021/11/16 19:40
 * <AUTHOR> W.YuLong
 * @description
 */
class HomeStoreFragment : BaseRootFragment(), View.OnClickListener, OnRefreshListener {
    lateinit var categoryTextView: TextView
    lateinit var searchTextView: TextView
    lateinit var deeplinkTextView: TextView
    lateinit var dataRecyclerView: CustomRecyclerView
    lateinit var refreshLayout: SmartRefreshLayout

    lateinit var notifyLayout: RelativeLayout
    lateinit var notifyCloseImageView: ImageView
    lateinit var notifyTextView: TextView
    lateinit var cartImageView: ImageView


    lateinit var microAdapter: HomeStoreAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        var v = inflater.inflate(R.layout.fragment_new_home_store_layout, container, false)
        initViewFromXML(v)
        setViewListeners()
        microAdapter = HomeStoreAdapter()
        dataRecyclerView.adapter = microAdapter
        reloadLocalWidgetContainer()
        loadData()
        return v
    }

    fun initViewFromXML(v: View) {
        categoryTextView = v.findViewById(R.id.home_store_top_category_TextView)
        searchTextView = v.findViewById(R.id.home_store_top_search_TextView)
        dataRecyclerView = v.findViewById(R.id.home_store_data_RecyclerView)
        refreshLayout = v.findViewById(R.id.home_store_RefreshLayout)
        deeplinkTextView = v.findViewById(R.id.deeplink_test_TextView)
        notifyLayout = v.findViewById(R.id.home_store_notification_layout)
        notifyCloseImageView = v.findViewById(R.id.home_store_close_banner_notify_ImageView)
        notifyTextView = v.findViewById(R.id.home_store_banner_notification_TextView)
        cartImageView = v.findViewById(R.id.home_store_shopping_cart_ImageView)
    }

    fun setViewListeners() {
        deeplinkTextView.setOnClickListener(this)
        categoryTextView.setOnClickListener(this)
        searchTextView.setOnClickListener(this)
        cartImageView.setOnClickListener(this)
        refreshLayout.setOnRefreshListener(this)
    }

    override fun onClick(v: View?) {
        when (v) {
            cartImageView -> {
                val intent = Intent(requireContext(), ShoppingCartActivity::class.java)
                startActivity(intent)
            }
            deeplinkTextView -> {
                val intent = Intent(requireContext(), TestDeeplinkActivity::class.java)
                startActivity(intent)
            }
            categoryTextView -> {
                val intent = Intent(requireContext(), BookCategoryListActivity::class.java)
                v.context.startActivity(intent)
            }
            searchTextView -> {
                val intent = Intent(requireContext(), SearchActivity::class.java)
                startActivity(intent)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun doOnNotificationEvent(event: OnSyncNotificationMessageFinish) {
//        var exceptType = if (SPSingleton.get().getBoolean(SPKeyDefine.SP_ReceiveCouponNotification, true)) 0 else NotificationType.COUPON.type
        var notificationEntity = SDKSingleton.userBl.getLatestNotificationEntityExceptType(0)
        val closedId = SPSingleton.get().getString(SPKeyDefine.SP_ClosedNotifyTipId, "")

        if (notificationEntity != null && APPUtil.formatNotifyMsgId(notificationEntity) != closedId && notificationEntity.showMethod == 2) {
            notifyLayout.visibility = View.VISIBLE
            notifyTextView.text = notificationEntity.title
            notifyCloseImageView.setOnClickListener {
                notifyLayout.visibility = View.GONE
                SPSingleton.get().putString(SPKeyDefine.SP_ClosedNotifyTipId, APPUtil.formatNotifyMsgId(notificationEntity))
            }

            notifyLayout.setOnClickListener {
                NotificationDetailActivity.gotoNotificationDetail(
                    requireContext(), notificationEntity.id, notificationEntity.type,
                    listener = null
                )
                SPSingleton.get().putString(SPKeyDefine.SP_ClosedNotifyTipId, APPUtil.formatNotifyMsgId(notificationEntity))
                notifyLayout.visibility = View.GONE
            }
        }
    }

    private var isFirst: Boolean = true
    override fun onRefresh(layout: RefreshLayout?) {
        microAdapter = HomeStoreAdapter()
        dataRecyclerView.adapter = microAdapter
        loadData()
    }

    private fun loadData() {
        isFirst = SPSingleton.get().getBoolean(SPKeyDefine.SP_is_first_load_store, true)
        lifecycleScope.launch {
            // 先直接同步数据
            try {
                if (isFirst) {
                    activity?.let { APPUtil.showLoadingDialog(it) }
                    SPSingleton.get().putBoolean(SPKeyDefine.SP_is_first_load_store, false)
                }
                SDKSingleton.syncBl.syncAppData()
                APPUtil.syncNotification()

                doOnNotificationEvent(OnSyncNotificationMessageFinish(1))

                reloadLocalWidgetContainer()
                refreshLayout.finishLoadMoreAndRefresh()
                activity?.let { APPUtil.dismissLoadingDialog(it) }
            } catch (e: Exception) {
                e.printStackTrace()
                refreshLayout.finishLoadMoreAndRefresh()
                reloadLocalWidgetContainer()
                activity?.let { APPUtil.dismissLoadingDialog(it) }
                ExceptionHandler.handleException(e)
            }
        }
    }

    private fun reloadLocalWidgetContainer() {
        microAdapter.dataList =
            SDKSingleton.appBl.getNewWidgetCombineDataList(HomeType.StoreHome).toMutableList()
        KLog.d(microAdapter.dataList?.toJsonStr())

        if (!microAdapter.dataList.isNullOrEmpty()) {
            var index: Int = -1
            for (i in microAdapter.dataList!!.indices) {
                if (microAdapter.dataList!![i].containerKey.isEqualOne(
                        MicroWidgetViewFactory.KEY_upDownItems,
                        MicroWidgetViewFactory.KEY_leftRightItems
                    )
                ) {
                    index = i
                    microAdapter.notifyItemChanged(index)
                }
            }
        }
    }
}

/***
 *@date 创建时间 2021/11/18 17:30
 *<AUTHOR> W.YuLong
 *@description
 */
class HomeStoreAdapter : HomeMicroCommAdapter()
