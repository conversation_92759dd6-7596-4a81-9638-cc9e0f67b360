package com.wedevote.wdbook.ui.home

/***
 * @date 创建时间 2022/4/15 15:27
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */
enum class HomeTab(val tab: Int) {
    EMPTY(-1),
    
    //    READ(0),
    SHELF(1),
    STORE(2),
    MINE(3);

    companion object {
        fun contentOf(v: Int): HomeTab {
            return when (v) {
                -1 -> EMPTY
//                0 -> READ
                1 -> SHELF
                3 -> MINE
                else -> STORE
            }
        }
    }
}
