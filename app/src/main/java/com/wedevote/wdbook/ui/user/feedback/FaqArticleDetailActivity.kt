package com.wedevote.wdbook.ui.user.feedback

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.View
import android.webkit.WebChromeClient
import android.webkit.WebSettings.LayoutAlgorithm
import android.webkit.WebView
import android.widget.TextView
import androidx.webkit.WebSettingsCompat
import androidx.webkit.WebViewFeature
import com.wedevote.wdbook.R
import com.wedevote.wdbook.R.id
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/***
 * @date 创建时间 2022/5/6 17:08
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @description
 */
class FaqArticleDetailActivity : RootActivity() {
    
    lateinit var titleLayout: CommTopTitleLayout
    lateinit var noNetworkTextView: TextView
    lateinit var webView: WebView
    
    companion object {
        fun gotoFaqArticalDetailActivity(context: Context, feedbackId: Long) {
            val intent = Intent(context, FaqArticleDetailActivity::class.java)
            intent.putExtra(IntentConstants.EXTRA_feedbackId, feedbackId)
            context.startActivity(intent)
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_faq_article_detail_layout)
        initViewFromXML()
        val id = intent.getLongExtra(IntentConstants.EXTRA_feedbackId, -1)
        configWebViewSetting()
        if (id != -1L) {
            MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
                val articleEntity = SDKSingleton.userBl.getHelpArticle(id)
                if (articleEntity != null) {
                    titleLayout.setTitle(articleEntity!!.title)
                    webView.loadDataWithBaseURL(null, articleEntity!!.content, "text/html", "UTF-8", "")
                } else {
                    onBackPressed()
                }
            }
        } else {
            onBackPressed()
        }
    }
    
    private fun initViewFromXML() {
        titleLayout = findViewById(id.faq_detail_title_layout)
        webView = findViewById(id.faq_detail_WebView)
        noNetworkTextView = findViewById(id.faq_detail_no_network_TextView)
        webView.visibility = View.GONE
        webView.setBackgroundColor(Color.TRANSPARENT)
    }
    
    fun configWebViewSetting() {
        //支持javascript
        webView.settings.setJavaScriptEnabled(true);
        // 设置可以支持缩放
        webView.settings.setSupportZoom(true);
        // 设置出现缩放工具
        webView.settings.setBuiltInZoomControls(true);
        //扩大比例的缩放
        webView.settings.setUseWideViewPort(true);
        webView.settings.setSupportZoom(false);
        //自适应屏幕
        webView.settings.setLayoutAlgorithm(LayoutAlgorithm.SINGLE_COLUMN);
        webView.settings.setLoadWithOverviewMode(true);
        
        if (WebViewFeature.isFeatureSupported(WebViewFeature.FORCE_DARK)) {
            val isDark = !SDKSingleton.appBl.isCurrentThemeLight()
            WebSettingsCompat.setForceDark(
                webView.settings,
                if (isDark) WebSettingsCompat.FORCE_DARK_ON else WebSettingsCompat.FORCE_DARK_OFF
            )
        }
        webView.webChromeClient = webChromeClient
    }

    private val webChromeClient = object : WebChromeClient() {
        override fun onProgressChanged(view: WebView?, newProgress: Int) {
            if (newProgress == 100) {
                //加载完成
                webView.visibility = View.VISIBLE
            }
            super.onProgressChanged(view, newProgress)
        }
    }

}