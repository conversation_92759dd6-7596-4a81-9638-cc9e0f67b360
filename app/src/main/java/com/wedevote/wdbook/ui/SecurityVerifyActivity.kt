package com.wedevote.wdbook.ui

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.TextView
import com.aquila.lib.tools.singleton.SPSingleton.Companion.get
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.NetWorkUtils
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.ui.account.BindPhoneOrEmailActivity
import com.wedevote.wdbook.ui.account.register.RegisterLayoutManager
import com.wedevote.wdbook.ui.user.HomeMineFragment

/***
 *@date 创建时间 2023/5/8
 *<AUTHOR> <PERSON><PERSON>
 *@description  安全验证页面
 */
class SecurityVerifyActivity : RootActivity(), View.OnClickListener {

    private lateinit var backView: View
    private lateinit var contentLayout: FrameLayout
    private lateinit var verifyMethodLayout: LinearLayout
    private lateinit var nextTextView: TextView
    private lateinit var verifyMethodTextView: TextView
    private lateinit var titleTextView: TextView
    private var registerLayoutManager: RegisterLayoutManager? = null

    private var mType = -1

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_security_verify_layout)
        initViewFromXML()
        setViewListeners()
        mType = intent.getIntExtra(IntentConstants.EXTRA_Account_Security_Type, -1)
        initUI()
    }

    private fun initUI() {
        registerLayoutManager = RegisterLayoutManager(this)
        registerLayoutManager!!.isResetPassword = true
        contentLayout.addView(registerLayoutManager!!.rootView)
        if (mType == RegisterLayoutManager.BIND_MODE_EMAIL) {
            registerLayoutManager?.setRegisterType(RegisterLayoutManager.REGISTER_MODE_EMAIL, false)
            verifyMethodLayout.visibility = View.GONE
        } else if (mType == RegisterLayoutManager.BIND_MODE_MOBILE) {
            registerLayoutManager?.setRegisterType(RegisterLayoutManager.REGISTER_MODE_MOBILE, false)
            verifyMethodLayout.visibility = View.GONE
        } else if (mType == RegisterLayoutManager.ACCOUNT_MODE_CHANGE_PASSWORD){
            val isEmailEmpty = !HomeMineFragment.userInfoEntity!!.email.isNullOrEmpty()
            val isMobileEmpty = !HomeMineFragment.userInfoEntity!!.mobile.isNullOrEmpty()
            if (isEmailEmpty && isMobileEmpty) {
                registerLayoutManager?.setRegisterType(RegisterLayoutManager.REGISTER_MODE_EMAIL, false)
                verifyMethodLayout.visibility = View.VISIBLE
                verifyMethodTextView.setText(R.string.mobile_verify)
            } else if (isEmailEmpty) {
                registerLayoutManager?.setRegisterType(RegisterLayoutManager.REGISTER_MODE_EMAIL, false)
                verifyMethodLayout.visibility = View.GONE
            } else if (isMobileEmpty) {
                registerLayoutManager?.setRegisterType(RegisterLayoutManager.REGISTER_MODE_MOBILE, false)
                verifyMethodLayout.visibility = View.GONE
            }
        }
        registerLayoutManager!!.setOnButtonStateChangeListener {
            nextTextView.isEnabled = it
        }

        registerLayoutManager!!.passwordEditText.hint = getString(R.string.please_input_new_password)
        registerLayoutManager!!.passwordEditTextTwo.hint = getString(R.string.please_input_new_password_again)
    }

    private fun setViewListeners() {
        backView.setOnClickListener(this)
        nextTextView.setOnClickListener(this)
        verifyMethodTextView.setOnClickListener(this)
    }

    private fun initViewFromXML() {
        backView = findViewById(R.id.register_back_View)
        contentLayout = findViewById(R.id.register_content_layout)
        nextTextView = findViewById(R.id.register_next_TextView)
        verifyMethodTextView = findViewById(R.id.security_verify_method_TextView)
        verifyMethodLayout = findViewById(R.id.security_verify_method_layout)
        nextTextView.setText(R.string.next_step)
        titleTextView = findViewById<TextView>(R.id.register_login_title_TextView)
        titleTextView.setText(R.string.reset_password)
    }

    override fun onClick(v: View?) {
        when (v) {
            backView -> {
                onBackPressed()
            }
            verifyMethodTextView -> {
                if (registerLayoutManager!!.registerType == RegisterLayoutManager.REGISTER_MODE_EMAIL) {
                    registerLayoutManager?.setRegisterType(RegisterLayoutManager.REGISTER_MODE_MOBILE, false)
                    verifyMethodTextView.setText(R.string.email_verify)
                } else {
                    registerLayoutManager?.setRegisterType(RegisterLayoutManager.REGISTER_MODE_EMAIL, false)
                    verifyMethodTextView.setText(R.string.mobile_verify)
                }
            }
            nextTextView -> {
                if (!NetWorkUtils.isNetworkAvailable()) {
                    NetWorkUtils.showTipDialog(this, getString(R.string.no_network_connect))
                    return
                }
                registerLayoutManager!!.checkVerificationCode{
                    goNextStep()
                }
            }
        }
    }

    private fun goNextStep() {
        if (mType == RegisterLayoutManager.BIND_MODE_MOBILE || mType == RegisterLayoutManager.BIND_MODE_EMAIL) {
            val intent = Intent(
                this@SecurityVerifyActivity,
                BindPhoneOrEmailActivity::class.java
            )
            intent.putExtra(IntentConstants.EXTRA_Account_Security_Type, mType)
            startActivity(intent)
            finish()
        } else {
            //跳转重置密码页面
            val intent = Intent(
                this@SecurityVerifyActivity,
                SetPasswordActivity::class.java
            )
            startActivity(intent)
            finish()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == IntentConstants.INTENT_RESULT_COUNTRY_CODE && data != null) {
            val countryCode = data.getStringExtra(IntentConstants.Extra_countryCode)
            registerLayoutManager!!.setCountryCodeText(countryCode)
            get().putString(SPKeyDefine.SP_defaultCountry, countryCode)
        }
    }
}
