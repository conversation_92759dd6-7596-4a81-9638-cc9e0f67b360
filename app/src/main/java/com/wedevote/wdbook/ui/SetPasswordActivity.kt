package com.wedevote.wdbook.ui

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.FrameLayout
import android.widget.TextView
import com.aquila.lib.tools.singleton.SPSingleton
import com.aquila.lib.tools.util.ToastUtil
import com.wdbible.app.wedevotebible.tools.security.EncodingUtils
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.Constants
import com.wedevote.wdbook.exception.ApiException
import com.wedevote.wdbook.exception.ErrorInfo
import com.wedevote.wdbook.tools.event.LogoutEvent
import com.wedevote.wdbook.tools.event.OnChangeAccountSuccess
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.AnalyticsUtils
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.NetWorkUtils
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.ui.account.LoginActivity
import com.wedevote.wdbook.ui.account.register.RegisterLayoutManager
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import android.widget.EditText
import android.widget.ImageView
import android.text.TextWatcher
import android.text.Editable
import com.wedevote.wdbook.ui.dialogs.RegisterTipDialog

/***
 *@date 创建时间 2023/5/22
 *<AUTHOR> John.Qian
 *@description  设置密码页面
 */
class SetPasswordActivity : RootActivity(), View.OnClickListener {
    private lateinit var backView: View
    private lateinit var contentLayout: FrameLayout
    private lateinit var nextTextView: TextView
    private lateinit var titleTextView: TextView
    private var registerLayoutManager: RegisterLayoutManager? = null
    private lateinit var oldPasswordEditText: EditText
    private lateinit var oldPasswordShowImageView: ImageView
    private lateinit var oldPasswordErrorTextView: TextView

    private fun findViewByIdFromXML() {
        backView = findViewById(R.id.register_back_View)
        contentLayout = findViewById(R.id.register_content_layout)
        nextTextView = findViewById(R.id.register_next_TextView)
        nextTextView.setText(R.string.reset_password)
        titleTextView = findViewById(R.id.register_login_title_TextView)
        titleTextView.setText(R.string.set_password)
        oldPasswordEditText = findViewById(R.id.set_password_old_EditText)
        oldPasswordShowImageView = findViewById(R.id.set_password_show_ImageView)
        oldPasswordErrorTextView = findViewById(R.id.password_error_tip_TextView)
    }

    public override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val rootView = layoutInflater.inflate(R.layout.activity_set_password, null)
        setContentView(rootView)
        findViewByIdFromXML()
        setViewCLickListeners()
        registerLayoutManager = RegisterLayoutManager(this)
        contentLayout.addView(registerLayoutManager!!.rootView)
        registerLayoutManager!!.isResetPassword = true
        registerLayoutManager!!.setNextStepUi()
        registerLayoutManager!!.setOnButtonStateChangeListener {
            nextTextView.isEnabled = it && isOldPasswordCorrect(false)
        }
        registerLayoutManager!!.passwordEditText.hint = getString(R.string.please_input_new_password)
        registerLayoutManager!!.passwordEditTextTwo.hint = getString(R.string.please_input_new_password_again)
        registerLayoutManager!!.phoneTitleTextView!!.setText(R.string.new_password)
        registerLayoutManager!!.setEditTextInputSpace(oldPasswordEditText)
        initOldPasswordListeners()
    }

    private fun setViewCLickListeners() {
        backView.setOnClickListener(this)
        nextTextView.setOnClickListener(this)
    }

    override fun onClick(v: View) {
        if (APPConfig.isFastClick()) return
        if (v === backView) {
            onBackPressed()
        } else if (v === nextTextView) {
            if (!NetWorkUtils.isNetworkAvailable()) {
                NetWorkUtils.showTipDialog(this, getString(R.string.no_network_connect))
                return
            }
            if (!isOldPasswordCorrect() || !registerLayoutManager!!.isPasswordCorrect() || !registerLayoutManager!!.isPasswordTwoCorrect()) return
            // 检查新密码不能与旧密码相同
            val oldPwd = oldPasswordEditText.text.toString().trim()
            val newPwd = registerLayoutManager!!.passwordEditText.text.toString().trim()
            if (oldPwd == newPwd) {
                registerLayoutManager!!.passwordEditText.isSelected = true
                val passwordErrorTip = registerLayoutManager!!.rootView.findViewById<TextView>(R.id.password_error_tip_TextView)
                passwordErrorTip.visibility = View.VISIBLE
                passwordErrorTip.setText(R.string.new_password_not_same_old)
                return
            }
            MainScope().launch {
                try {
                    APPUtil.showLoadingDialog(this@SetPasswordActivity).setCancelable(false)

                    val encryptSign = EncodingUtils.rsaEncryptByPublicKey(
                        oldPasswordEditText.text.toString(),
                        Constants.PUBLICK_KEY
                    )
                    SDKSingleton.sessionBl.getAccessToken()
                    SDKSingleton.sessionBl.checkpassword(encryptSign)

                    val sign = oldPasswordEditText.text.toString() + "\n" + registerLayoutManager!!.passwordEditText.text.toString()
                    val signPassword = EncodingUtils.rsaEncryptByPublicKey(
                        sign,
                        Constants.PUBLICK_KEY
                    )
                    SDKSingleton.userBl.updatePassword(signPassword)
                    APPUtil.dismissLoadingDialog(this@SetPasswordActivity)
                    var tipDialog = RegisterTipDialog(this@SetPasswordActivity)
                    tipDialog.setOnDismissListener{
                        MainScope().launch {
                            EventBus.getDefault().post(OnChangeAccountSuccess(true))
                            logout()
                            finish()
                        }
                    }
                    tipDialog.show()
                    tipDialog.setTitleText(getString(R.string.change_password_success))
                    tipDialog.setContentText(getString(R.string.new_password_apply_to_bible_and_book))
                } catch (exception: Throwable) {
                    APPUtil.dismissLoadingDialog(this@SetPasswordActivity)
                    if (exception is ApiException) {
                        when (exception.code) {
                            ErrorInfo.PasswordNotMatch.code, ErrorInfo.PasswordNotMatch2.code -> {
                                oldPasswordEditText.isSelected = true
                                oldPasswordErrorTextView.visibility = View.VISIBLE
                                oldPasswordErrorTextView.setText(R.string.user_password_wrong)
                            }
                            ErrorInfo.UserNotExists.code, ErrorInfo.UserNotExists2.code -> {
                                APPUtil.showTipDialog(
                                    this@SetPasswordActivity,
                                    getString(R.string.warm_prompt_title),
                                    getString(R.string.this_account_not_exist)
                                )
                            }
                            ErrorInfo.InvalidVerificationCode.code -> {
                                APPUtil.showTipDialog(
                                    this@SetPasswordActivity,
                                    getString(R.string.warm_prompt_title),
                                    getString(R.string.invalid_verification_code)
                                )
                            }
                            else -> {
                                ToastUtil.showToastShort(exception.message)
                            }
                        }
                    } else {
                        ExceptionHandler.handleException(exception)
                    }
                }
            }
        }
    }

    private suspend fun logout() {
        SDKSingleton.sessionBl.logout()
        SPSingleton.get().removeKey(SPKeyDefine.SP_LoginUserId)
        EventBus.getDefault().post(LogoutEvent())
        startActivity(Intent(this, LoginActivity::class.java))
        AnalyticsUtils.updateAnalyticsUserID()
    }

    private fun initOldPasswordListeners() {
        oldPasswordShowImageView.setOnClickListener {
            registerLayoutManager?.setPasswordShowState(
                oldPasswordShowImageView.isSelected,
                oldPasswordShowImageView,
                oldPasswordEditText
            )
        }
        oldPasswordEditText.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus) {
                isOldPasswordCorrect()
            } else {
                oldPasswordEditText.isSelected = false
                oldPasswordErrorTextView.visibility = View.GONE
            }
        }
        oldPasswordEditText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                oldPasswordEditText.isSelected = false
                oldPasswordErrorTextView.visibility = View.GONE
            }
        })
    }

    private fun isOldPasswordCorrect(isShowErrorTip: Boolean = true): Boolean {
        val pwd = oldPasswordEditText.text.toString().trim()
        if (pwd.isEmpty()) {
            if (isShowErrorTip) {
                oldPasswordEditText.isSelected = true
                oldPasswordErrorTextView.visibility = View.VISIBLE
                oldPasswordErrorTextView.setText(R.string.password_can_not_be_empty)
            }
            return false
        } else if (pwd.length < 7) {
            if (isShowErrorTip) {
                oldPasswordEditText.isSelected = true
                oldPasswordErrorTextView.visibility = View.VISIBLE
                oldPasswordErrorTextView.setText(R.string.password_length_can_not_less_than_seven)
            }
            return false
        }
        return true
    }
}