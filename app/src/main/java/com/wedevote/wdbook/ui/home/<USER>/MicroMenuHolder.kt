package com.wedevote.wdbook.ui.home.microwidget

import android.content.Intent
import android.view.LayoutInflater
import android.view.ViewGroup
import com.aquila.lib.base.BaseViewHolder
import com.wedevote.wdbook.tools.util.ImageLoadUtil
import com.aquila.lib.widget.group.GroupImageTextLayout
import com.google.android.flexbox.FlexboxLayout
import com.wedevote.wdbook.R
import com.wedevote.wdbook.entity.home.WidgetContainerCombineEntity
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.PictureUtil
import com.wedevote.wdbook.ui.home.BookSectionListActivity
import com.wedevote.wdbook.utils.JsonUtility

/***
 * @date 创建时间 2021/11/30 15:26
 * <AUTHOR> W.YuLong
 * @description
 */
class MicroMenuHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.micro_holder_recommend_menu_layout) {
    val containerView: FlexboxLayout = itemView as FlexboxLayout

    override fun <T> initUIData(t: T) {
        t as WidgetContainerCombineEntity
        containerView.removeAllViews()
        if (!t.detailEntityList.isNullOrEmpty()) {
            for (item in t.detailEntityList!!) {
                val itemLayout: GroupImageTextLayout =
                    LayoutInflater.from(itemView.context).inflate(R.layout.menu_cell_layout, containerView, false) as GroupImageTextLayout
                containerView.addView(itemLayout)

                itemLayout.setText(item.widgetTitle)
                PictureUtil.loadImageWithError(itemLayout.imageView, item.imgPath, R.drawable.ic_default_book_cover)
                itemLayout.setOnClickListener {
                    t.let { entity ->
                        val intent = Intent(it.context, BookSectionListActivity::class.java)
                        intent.putExtra(IntentConstants.EXTRA_WidgetDetailEntityJson, JsonUtility.encodeToString(item))
                        it.context.startActivity(intent)
                    }
                }
            }
        }
    }
}
