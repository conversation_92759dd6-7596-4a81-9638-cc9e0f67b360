package com.wedevote.wdbook.ui.home.microwidget

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.view.ViewGroup
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.log.KLog
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.ui.CommWebViewActivity
import com.wedevote.wdbook.utils.JsonUtility
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/***
 * @date 创建时间 2020/4/10 18:00
 * <AUTHOR> W.YuLong
 * @description 微件系统的工厂模式
 */
object MicroWidgetViewFactory {
    /*书籍分类*/
    const val HOLDER_TYPE_banners = 1
    const val HOLDER_TYPE_sections = 2
    const val HOLDER_TYPE_leftRightItems = 3
    const val HOLDER_TYPE_KEY_upDownItems = 4

    const val KEY_upDownItems = "upDownItems"
    const val KEY_leftRightItems = "leftRightItems"
    const val KEY_sections = "sections"
    const val KEY_banners = "banners"

    fun createHolderByType(parent: ViewGroup, type: Int): BaseViewHolder {
        return when (type) {
            HOLDER_TYPE_banners -> MicroBannerViewHolder(parent)
            HOLDER_TYPE_sections -> MicroMenuHolder(parent)
            HOLDER_TYPE_leftRightItems -> MicroRecommendViewHolder(parent, HOLDER_TYPE_leftRightItems)
            HOLDER_TYPE_KEY_upDownItems -> MicroRecommendViewHolder(parent, HOLDER_TYPE_KEY_upDownItems)
            else -> MicroBannerViewHolder(parent)
        }
    }

    fun getViewType(type: String?): Int {
        return when (type) {
            KEY_sections -> HOLDER_TYPE_sections
            KEY_banners -> HOLDER_TYPE_banners
            KEY_leftRightItems -> HOLDER_TYPE_leftRightItems
            KEY_upDownItems -> HOLDER_TYPE_KEY_upDownItems
            else -> -1
        }
    }

    fun parseContainerParams(params: String?): HashMap<String, String> {
        val map = HashMap<String, String>()
        if (params.isNullOrEmpty()) {
            return map
        }
        val list = params.split("\n")
        for (item in list) {
            if (!item.isNullOrEmpty()) {
                val entry = item.split("=")
                if (entry.size == 2) {
                    map[entry[0]] = entry[1]
                }
            }
        }
        return map
    }

    fun parseWidgetParams(widgetParams: String?, intent: Intent) {
        if (widgetParams.isNullOrEmpty()) {
            return
        }
        val params = widgetParams.split("&")
        for (p in params) {
            val values = p.split("=")
            if (values.size != 2) {
                continue
            }
            when (values[0]) {
                "categoryId" -> intent.putExtra(IntentConstants.EXTRA_CategoryId, values[1].toLong())
                "url" -> intent.putExtra(IntentConstants.EXTRA_url, values[1])
                "urlView" -> {
                    val contentURL = Uri.parse(values[1])
                    intent.data = contentURL
                }
            }
        }
    }

    fun parseDeepLinkParams(context: Context, widgetParams: String?) {
        if (widgetParams.isNullOrEmpty()) {
            return
        }
        val paramsMap: HashMap<String, String> = JsonUtility.decodeFromString(widgetParams)
        paramsMap["dataSource"]?.let { url ->
            if (!DeepLinkUtils.parseDeepLink(context, url)) {
                openUrl(context, paramsMap, url)
            }
        }
    }

    private fun openUrl(context: Context, paramsMap: HashMap<String, String>, url: String) {
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            var jumpUrl = if (SDKSingleton.sessionBl.isLogin()) {
                var nonce = SDKSingleton.appBl.getTokenNonce()
                val map = HashMap<String, String>()
                if (!nonce.isNullOrEmpty()) {
                    map["nonce"] = nonce
                }
                APPUtil.formatUrl(url, map)
            } else {
                url
            }

            // 如果需要登录，则带上nonce
            KLog.d("jumpUrl = $jumpUrl")
            if (paramsMap["openWithBrowser"] == "1") { // 1 表示需要打开网页
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse(jumpUrl.toString().replace("https","http")))
                context.startActivity(intent)
            } else {
                CommWebViewActivity.gotoWebView(context, jumpUrl.toString(), false, "")
            }
        }
    }
}
