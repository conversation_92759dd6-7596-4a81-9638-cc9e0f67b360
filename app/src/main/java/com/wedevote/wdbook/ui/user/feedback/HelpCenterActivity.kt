package com.wedevote.wdbook.ui.user.feedback

import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.widget.ProgressBar
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.tools.util.ToastUtil
import com.aquila.lib.widget.view.CustomRecyclerView
import com.aquila.lib.widget.view.DotView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.feedback.HelpFaqContainerEntity
import com.wedevote.wdbook.ui.account.OnLoginResultCallBack
import com.wedevote.wdbook.ui.account.SSOLoginActivity
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/***
 * @date 创建时间 2022/4/8 15:08
 * <AUTHOR> W.<PERSON>
 * @description 帮助中心的页面
 */
class HelpCenterActivity : RootActivity(), View.OnClickListener {
    lateinit var feedbackTextView: TextView
    lateinit var loadingProgressBar: ProgressBar
    
    lateinit var flagView: DotView
    lateinit var dataRecyclerView: CustomRecyclerView
    lateinit var dataAdapter: HelpFaqContainerAdapter
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_help_ceneter_layout)
        initViewFromXML()
        dataAdapter = HelpFaqContainerAdapter()
        dataRecyclerView.adapter = dataAdapter
        getDataFromServer()
        setViewListeners()
    }
    
    private fun setViewListeners() {
        feedbackTextView.setOnClickListener(this)
    }
    
    private fun initViewFromXML() {
        feedbackTextView = findViewById(R.id.help_center_i_will_feedback_TextView)
        loadingProgressBar = findViewById(R.id.help_center_loading_ProgressBar)
        
        flagView = findViewById(R.id.help_center_flag_DotView)
        dataRecyclerView = findViewById(R.id.help_center_data_RecyclerView)
    }
    
    fun getDataFromServer() {
        MainScope().launch(ExceptionHandler.getCoroutineExceptionHandler {
            ToastUtil.showToastShort("加载数据失败，请退出重试")
            loadingProgressBar.visibility = View.GONE
        }) {
            loadingProgressBar.visibility = View.VISIBLE
            val dataList = SDKSingleton.userBl.getHelpFaqList()
            dataAdapter.dataList = dataList?.toMutableList()
            loadingProgressBar.visibility = View.GONE
        }
    }
    
    override fun onResume() {
        super.onResume()
        initFeedbackStatusIU()
    }
    
    fun initFeedbackStatusIU() {
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            val statusEntity = SDKSingleton.userBl.getFeedbackStatus()
            if (statusEntity != null && statusEntity.feedbackStatus == 1) {
                flagView.visibility = View.VISIBLE
            } else {
                flagView.visibility = View.GONE
            }
        }
    }
    
    var lastClickTime: Long = 0
    override fun onClick(v: View?) {
        when (v) {
            feedbackTextView -> {
                if (System.currentTimeMillis() - lastClickTime > 1000) {
                    if (!SDKSingleton.sessionBl.isLogin()) {
                        SSOLoginActivity.checkAndGotoLogin(
                            this,
                            callBack = object : OnLoginResultCallBack {
                                override fun onLoginResult(isSuccess: Boolean) {
                                    if (isSuccess) {
                                        gotoFeedbackActivity()
                                    }
                                }
                            })
                    } else {
                        gotoFeedbackActivity()
                    }
                }
            }
        }
    }
    
    fun gotoFeedbackActivity() {
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            val dataList = SDKSingleton.userBl.getFeedbackTitleList()
            if (dataList.isNullOrEmpty()) {
                EditFeedbackActivity.gotoEditFeedback(this@HelpCenterActivity)
            } else {
                UserFeedbackListActivity.gotoUserFeedbackListActivity(this@HelpCenterActivity)
            }
        }
        lastClickTime = System.currentTimeMillis()
    }
}

/***
 *@date 创建时间 2022/4/8 16:12
 *<AUTHOR> W.YuLong
 *@description
 */
class HelpFaqContainerAdapter :
    BaseRecycleAdapter<HelpFaqContainerEntity, HelpCenterItemViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): HelpCenterItemViewHolder {
        return HelpCenterItemViewHolder(parent)
    }
}

/***
 *@date 创建时间 2022/4/8 15:52
 *<AUTHOR> W.YuLong
 *@description
 */
class HelpCenterItemViewHolder(parent: ViewGroup) :
    BaseViewHolder(parent, R.layout.holder_item_faq_category_list_layout) {
    val titleTextView: TextView = itemView.findViewById(R.id.faq_item_title_TextView)
    val dataRecyclerView: CustomRecyclerView =
        itemView.findViewById(R.id.faq_item_data_RecyclerView)
    val faqArticleAdapter: FaqArticleItemAdapter = FaqArticleItemAdapter()
    
    init {
        dataRecyclerView.adapter = faqArticleAdapter
    }
    
    override fun <T> initUIData(t: T) {
        t as HelpFaqContainerEntity
        titleTextView.text = t.title
        faqArticleAdapter.dataList = t.articleList?.toMutableList()
    }
}
