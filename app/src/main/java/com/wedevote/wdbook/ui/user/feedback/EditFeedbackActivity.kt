package com.wedevote.wdbook.ui.user.feedback

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import com.aquila.lib.base.BaseRecycleAdapter
import com.aquila.lib.base.BaseViewHolder
import com.aquila.lib.tools.util.ToastUtil
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.EntranceType
import com.wedevote.wdbook.entity.feedback.FeedbackSaveEntity
import com.wedevote.wdbook.entity.feedback.FeedbackTagEntity
import com.wedevote.wdbook.ui.widgets.CommTopTitleLayout
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/***
 * @date 创建时间 2022/4/8 17:42
 * <AUTHOR> W.YuLong
 * @description 意见反馈的页面
 */
class EditFeedbackActivity : RootActivity(), View.OnClickListener {

    lateinit var topTitleLayout: CommTopTitleLayout
    lateinit var typeRecyclerView: CustomRecyclerView
    lateinit var contentEditText: EditText
    lateinit var submitButton: Button
    lateinit var itemAdapter: TypeItemAdapter

//    var typeList = arrayListOf("提建议", "支付问题", "功能问题", "其他")

    companion object {
        fun gotoEditFeedback(context: Context) {
            val intent = Intent(context, EditFeedbackActivity::class.java)
            context.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initViewFromXML()

        itemAdapter = TypeItemAdapter()
        typeRecyclerView.adapter = itemAdapter

        submitButton.setOnClickListener(this)
        contentEditText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

            override fun afterTextChanged(s: Editable?) {
                submitButton.isEnabled = !s?.trim().isNullOrEmpty()
            }

        })
        getDataFromServer()
    }

    private fun initViewFromXML() {
        setContentView(R.layout.activity_feedback_content_layout)
        typeRecyclerView = findViewById(R.id.feedback_type_RecyclerView)
        contentEditText = findViewById(R.id.feedback_input_content_EditText)
        submitButton = findViewById(R.id.feedback_submit_Button)
    }

    fun getDataFromServer() {
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            val tagList = SDKSingleton.userBl.getFeedbackTagList(EntranceType.FROM_USER.type)
            itemAdapter.dataList = tagList?.toMutableList()
            submitButton.isEnabled = !contentEditText.text.trim().isNullOrEmpty()
        }
    }

    var lastClickTime: Long = 0
    override fun onClick(v: View?) {
        when (v) {
            submitButton -> {
                if (System.currentTimeMillis() - lastClickTime > 1000) {
                    lastClickTime = System.currentTimeMillis()

                    if (contentEditText.text.trim().isNullOrEmpty()) {
                        ToastUtil.showToastShort(R.string.please_input_feedback_content)
                        return
                    }
    
                    MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
                        val tagEntity = itemAdapter.getDataFromPosition(itemAdapter.selectPosition)
                        val paramEntity = FeedbackSaveEntity()
                        paramEntity.tagIdList = arrayListOf(tagEntity!!.tagId)
                        paramEntity.messageText = contentEditText.text.toString()
                        SDKSingleton.userBl.saveFeedback(paramEntity)
                        UserFeedbackListActivity.gotoUserFeedbackListActivity(this@EditFeedbackActivity)
                        finish()
                    }
                }

            }
        }
    }

    /***
     *@date 创建时间 2022/4/8 19:43
     *<AUTHOR> W.YuLong
     *@description
     */
    class TypeItemAdapter : BaseRecycleAdapter<FeedbackTagEntity, TypeViewHolder>() {
        var selectPosition = 0
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TypeViewHolder {
            return TypeViewHolder(parent)
        }

        override fun onBindViewHolder(holder: TypeViewHolder, position: Int) {
            val data = getDataFromPosition(position)
            holder.initUIData(data)
            holder.nameTextView.isSelected = (selectPosition == position)

            holder.itemView.setOnClickListener {
                selectPosition = position
                notifyDataSetChanged()
            }
        }
    }

    /***
     *@date 创建时间 2022/4/8 19:42
     *<AUTHOR> W.YuLong
     *@description
     */
    class TypeViewHolder(parent: ViewGroup) : BaseViewHolder(parent, R.layout.holder_item_feedback_type_layout) {
        val nameTextView: TextView = itemView.findViewById(R.id.type_feedback_content_TextView)

        override fun <T> initUIData(t: T) {
            t as FeedbackTagEntity
            nameTextView.text = t.tagName
        }
    }
}
