package com.wedevote.wdbook.ui.store.search

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.KeyEvent
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.aquila.lib.dialog.CommAlertDialog
import com.aquila.lib.layout.SmartRefreshLayout
import com.aquila.lib.layout.api.RefreshLayout
import com.aquila.lib.layout.listener.OnLoadMoreListener
import com.aquila.lib.layout.listener.OnRefreshListener
import com.aquila.lib.layout.util.DensityUtil
import com.aquila.lib.tools.util.ToastUtil
import com.aquila.lib.widget.view.CustomRecyclerView
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.store.SearchItemEntity
import com.wedevote.wdbook.tools.interfaces.OnItemClickListener
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.NetWorkUtils
import com.wedevote.wdbook.ui.dialogs.LoadingProgressDialog
import com.wedevote.wdbook.ui.read.lib.EPubBook
import com.wedevote.wdbook.ui.store.FlexboxDataAdapter
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/***
 * @date 创建时间 2021/12/14 17:31
 * <AUTHOR> W.YuLong
 * @description 搜索的页面
 */
class SearchActivity : RootActivity(), View.OnClickListener, OnLoadMoreListener, OnRefreshListener {
    lateinit var searchEditText: EditText
    lateinit var clearInputImageView: ImageView
    lateinit var cancelTextView: TextView
    lateinit var historyCommendLayout: ConstraintLayout
    lateinit var historyLayout: ConstraintLayout
    lateinit var deleteHistoryImageView: ImageView
    lateinit var historyDataRecyclerView: CustomRecyclerView

    lateinit var noResultLayout: LinearLayout
    lateinit var resultDataRecyclerView: CustomRecyclerView
    lateinit var resultRefreshLayout: SmartRefreshLayout
    lateinit var noHistoryLayout: LinearLayout

    lateinit var historyFlexAdapter: FlexboxDataAdapter<SearchItemEntity>

    lateinit var resultDataAdapter: SearchResultAdapter

    var entity = SearchItemEntity()

    var currentPage = 1
    var totalPage = 99
    var currentSearchEntity = SearchItemEntity()

    /*加载搜索历史记录的条数*/
    val maxSearchHistoryCount = 8L

    private lateinit var loadingDialog: LoadingProgressDialog

    enum class SearchModeUI(val value: Int) {
        HISTORY(0),
        RESULT_DATA(1),
        NO_DATA(2),
    }

    private var currentMode = SearchModeUI.HISTORY

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_search_layout)
        initViewFromXML()
        initCurrentModeShow(currentMode)
        loadingDialog = LoadingProgressDialog(this)
        initAdapters()
        loadSearchHistoryData()
        setViewListeners()
        searchEditText.post {
            var keywords = intent.getStringExtra(IntentConstants.EXTRA_Keywords)
            if (!keywords.isNullOrEmpty()) {
                searchEditText.setText(keywords)
                searchEditText.setSelection(keywords.length)
                userDoSearchAction()
            } else {
                searchEditText.requestFocus()
            }
        }
    }

    private fun initViewFromXML() {
        searchEditText = findViewById(R.id.search_input_EditText)
        clearInputImageView = findViewById(R.id.search_clear_input_ImageView)
        cancelTextView = findViewById(R.id.search_cancel_TextView)
        historyCommendLayout = findViewById(R.id.search_history_and_recommend_container_layout)
        historyLayout = findViewById(R.id.search_history_container_layout)
        deleteHistoryImageView = findViewById(R.id.search_delete_history_ImageView)
        historyDataRecyclerView = findViewById(R.id.search_history_RecyclerView)
//        suggestDataRecyclerView = findViewById(R.id.search_suggest_RecyclerView)
        noResultLayout = findViewById(R.id.search_no_result_Layout)
        resultDataRecyclerView = findViewById(R.id.search_result_data_RecyclerView)
        resultRefreshLayout = findViewById(R.id.search_data_RefreshLayout)
        noHistoryLayout = findViewById(R.id.search_no_history_Layout)
    }

    private fun initAdapters() {
        historyFlexAdapter = FlexboxDataAdapter(FlexboxDataAdapter.FlexboxViewType.SEARCH_HISTORY)
        historyFlexAdapter.enableSelectItem = false
        historyDataRecyclerView.adapter = historyFlexAdapter

//        suggestFlexAdapter = FlexboxDataAdapter(FlexboxDataAdapter.FlexboxViewType.SUGGEST_KEYWORDS)
//        suggestFlexAdapter.enableSelectItem = false
//        suggestDataRecyclerView.adapter = suggestFlexAdapter
//        initSuggestKeywords()

        resultDataAdapter = SearchResultAdapter()
        resultDataRecyclerView.adapter = resultDataAdapter
    }

    private fun setViewListeners() {
        clearInputImageView.setOnClickListener(this)
        cancelTextView.setOnClickListener(this)
        deleteHistoryImageView.setOnClickListener(this)
        resultRefreshLayout.setOnRefreshListener(this).setOnLoadMoreListener(this)
        val onFlexItemSelectListener = object : OnItemClickListener {
            override fun <T> onItemClick(obj: Any, tag: String, t: T) {
                if (t is SearchItemEntity) {
                    searchEditText.setText(t.keyWords)
                    searchEditText.setSelection(t.keyWords.length)
                    userDoSearchAction(t)
                } else if (t is String) {
                    searchEditText.setText(t)
                    searchEditText.setSelection(t.length)
                    userDoSearchAction(null)
                }
            }
        }
        historyFlexAdapter.onItemClickListener = onFlexItemSelectListener
//        suggestFlexAdapter.onItemClickListener = onFlexItemSelectListener

        searchEditText.setOnEditorActionListener(object : TextView.OnEditorActionListener {
            override fun onEditorAction(v: TextView?, actionId: Int, event: KeyEvent?): Boolean {
                if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                    userDoSearchAction()
                    return true
                }
                return false
            }
        })
        searchEditText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

            override fun afterTextChanged(s: Editable?) {
                clearInputImageView.visibility = if (s!!.length > 0) View.VISIBLE else View.GONE
            }
        })
    }

//    fun initSuggestKeywords() {
//        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
//            suggestFlexAdapter.dataList = SDKSingleton.appBl.getSuggestSearch()
//        }
//    }

    /*用户手动点击搜索操作*/
    fun userDoSearchAction(entity: SearchItemEntity? = null) {
        if (APPConfig.isFastClick()) return
        if (entity == null) {
            var searchEntity = SearchItemEntity()
            searchEntity.keyWords = EPubBook.removeRTLControlChars(searchEditText.text.toString().trim())
            currentSearchEntity = searchEntity
        } else {
            currentSearchEntity = entity
            currentSearchEntity.updateTime = System.currentTimeMillis()
        }
        if (currentSearchEntity.keyWords.isNullOrBlank()) {
            ToastUtil.showToastShort(R.string.input_search_keyword)
            return
        }
        val length = currentSearchEntity.keyWords.length
        if (length > 32) {
            currentSearchEntity.keyWords = currentSearchEntity.keyWords.subSequence(length - 32, length).toString()
        }

        SDKSingleton.appBl.saveSearchData(currentSearchEntity)
        loadSearchHistoryData()
        loadingDialog.show()
        loadingDialog.setTitleText(getString(R.string.loading))
        onRefresh(resultRefreshLayout)
    }

    private fun loadSearchHistoryData() {
        historyFlexAdapter.dataList = SDKSingleton.appBl.getSearchDataList(maxSearchHistoryCount)
        historyLayout.visibility = if (historyFlexAdapter.itemCount > 0) View.VISIBLE else View.GONE
        if (historyFlexAdapter.itemCount > 0) {
            noHistoryLayout.visibility = View.GONE
        } else {
            noHistoryLayout.visibility = View.VISIBLE
        }
    }

    override fun onRefresh(refreshLayout: RefreshLayout?) {
        currentPage = 1
        resultRefreshLayout.isEnableLoadMore = true
        resultDataAdapter.clearDataList()
        onLoadMore(resultRefreshLayout)
    }

    override fun onLoadMore(refreshLayout: RefreshLayout) {
        MainScope().launch(ExceptionHandler.getCoroutineExceptionHandler {
            resultRefreshLayout.finishLoadMoreAndRefresh()
            loadingDialog.dismiss()
        }) {
            val resultPageData = SDKSingleton.storeBl.searchProduct(currentPage, 10, currentSearchEntity.keyWords)
            if (resultPageData != null) {
                totalPage = resultPageData.totalPage
                resultDataAdapter.addDataList(resultPageData.productList)
                resultRefreshLayout.isEnableLoadMore = (currentPage < totalPage)
                currentPage++
            } else {
                resultRefreshLayout.isEnableLoadMore = false
            }
            resultRefreshLayout.finishLoadMoreAndRefresh()

            if (resultDataAdapter.itemCount <= 0) {
                initCurrentModeShow(SearchModeUI.NO_DATA)
            } else {
                initCurrentModeShow(SearchModeUI.RESULT_DATA)
            }

            if (loadingDialog.isShowing) {
                loadingDialog.dismiss()
            }
        }
    }

    private fun initCurrentModeShow(mode: SearchModeUI) {
        when (mode) {
            SearchModeUI.HISTORY -> {
                historyCommendLayout.visibility = View.VISIBLE
                resultRefreshLayout.visibility = View.GONE
                noResultLayout.visibility = View.GONE
            }
            SearchModeUI.RESULT_DATA -> {
                historyCommendLayout.visibility = View.GONE
                resultRefreshLayout.visibility = View.VISIBLE
                noResultLayout.visibility = View.GONE
                val inputMethodManager = getSystemService(INPUT_METHOD_SERVICE) as android.view.inputmethod.InputMethodManager
                inputMethodManager.hideSoftInputFromWindow(searchEditText.windowToken, 0)
                searchEditText.clearFocus()
            }
            SearchModeUI.NO_DATA -> {
                historyCommendLayout.visibility = View.GONE
                resultRefreshLayout.visibility = View.GONE
                noResultLayout.visibility = View.VISIBLE
            }
        }
        currentMode = mode
    }

    override fun onClick(v: View?) {
        when (v) {
            cancelTextView -> {
                onBackPressed()
            }
            clearInputImageView -> {
                searchEditText.setText("")
                initCurrentModeShow(SearchModeUI.HISTORY)
            }
            deleteHistoryImageView -> {
                showClearSearchHistoryDialog()
            }
        }
    }

    fun showClearSearchHistoryDialog() {
        CommAlertDialog.with(this)
            .setTitle(R.string.are_you_sure_delete_all_history)
            .setTitleTextTextSize(16)
            .setDialogHeight(DensityUtil.dp2px(110f))
            .setDialogWidth(DensityUtil.dp2px(325f))
            .setStartText(R.string.label_cancel).setEndText(R.string.label_delete)
            .setLeftColorRes(R.color.text_color_blue_007AFF)
            .setRightColorRes(R.color.color_red_FF342A)
            .setOnViewClickListener { dialog, view, i ->
                if (i == CommAlertDialog.TAG_CLICK_END) {
                    SDKSingleton.appBl.clearSearchHistory()
                    loadSearchHistoryData()
                    ToastUtil.showToastShort(R.string.has_clear_search_data)
                }
            }.showDialog()
    }
}
