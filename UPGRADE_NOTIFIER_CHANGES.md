# API版本错误码统一处理改动说明

## 改动概述

将API版本错误码（601和602）的处理从UI层移至中间层统一处理，实现了类似WDBibleApi的架构模式。

## 主要改动

### 1. 中间层改动

#### ResponseHandler.kt
- 在`SDKConfig.handleResponse`方法中添加了对601和602错误码的统一处理
- 当检测到这些错误码时，会在主线程中调用`upgradeNotifier?.onNeedUpdate()`方法
- 使用`MainScope().launch`确保UI操作在主线程中执行

#### SDKConfig.kt
- 添加了`upgradeNotifier: UpgradeNotifier?`参数
- 支持各平台传入自定义的升级通知实现

#### SDK创建方法
- Android: `SDK.Companion.create()`方法添加了`upgradeNotifier`参数
- iOS: `IosSDK`构造函数添加了`upgradeNotifier`参数

### 2. Android端改动

#### AndroidUpgradeNotifier.kt (新增)
- 实现了`UpgradeNotifier`接口
- 在`onNeedUpdate`方法中调用`APPUpgradeManager.showUpdateDialog()`
- 使用`ActivityLifecycleCallbacks`来跟踪当前Activity
- 确保只在有效的FragmentActivity中显示升级对话框

#### SDKSingleton.kt
- 在`initializeSdk`方法中创建`AndroidUpgradeNotifier`实例
- 将其传递给SDK创建方法

#### APP.kt
- 在`onCreate`方法中调用`AndroidUpgradeNotifier.init(this)`来初始化Activity跟踪

#### HomeMineFragment.kt
- 移除了特定的API版本错误码处理逻辑
- 移除了不再需要的import语句

### 3. 测试
- 添加了`UpgradeNotifierTest.kt`来验证实现

## 优势

1. **统一处理**: 所有API接口的601/602错误码都会被统一处理，不需要在每个调用点单独处理
2. **代码复用**: 避免了在多个地方重复相同的错误处理逻辑
3. **平台兼容**: iOS端可以实现自己的`UpgradeNotifier`或传入null来忽略这些错误
4. **易于维护**: 升级逻辑集中在一个地方，便于后续修改和维护

## iOS端适配

iOS端可以选择：
1. 实现自己的`UpgradeNotifier`来处理升级通知
2. 传入`null`来忽略这些错误码（保持现有行为）

## 技术细节

### 线程安全
- 升级通知在主线程中执行，避免了UI操作的线程问题
- 使用`MainScope().launch`确保对话框显示在正确的线程中

### Activity管理
- 使用`ActivityLifecycleCallbacks`跟踪当前活跃的Activity
- 使用`WeakReference`避免内存泄漏
- 只在`FragmentActivity`中显示升级对话框

## 向后兼容性

- 所有现有的API调用都会自动获得这个功能
- 不需要修改现有的业务逻辑代码
- iOS端如果不传入`upgradeNotifier`，行为与之前保持一致

## 问题修复

### 线程问题修复
- 修复了在后台线程中尝试显示UI对话框导致的崩溃问题
- 确保所有UI操作都在主线程中执行
