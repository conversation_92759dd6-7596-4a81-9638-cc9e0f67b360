package com.aquila.lib.layout.api;


import android.annotation.SuppressLint;

import androidx.annotation.RestrictTo;

import static androidx.annotation.RestrictTo.Scope.*;

/**
 * 刷新底部
 * Created by SCWANG on 2017/5/26.
 */
@SuppressLint("SupportAnnotationUsage")
@RestrictTo({LIBRARY,LIBRARY_GROUP,SUBCLASSES})
public interface RefreshFooter extends RefreshInternal {

    /**
     * 设置数据全部加载完成，将不能再次触发加载功能
     * @param noMoreData 是否有更多数据
     * @return true 支持全部加载完成的状态显示 false 不支持
     */
    boolean setNoMoreData(boolean noMoreData);
}
