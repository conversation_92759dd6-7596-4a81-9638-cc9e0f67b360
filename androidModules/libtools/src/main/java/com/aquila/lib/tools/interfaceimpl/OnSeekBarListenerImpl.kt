package com.aquila.lib.tools.interfaceimpl

import android.widget.SeekBar

/***
 * @date 创建时间 2019-11-25 17:46
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */
open class OnSeekBarListenerImpl : SeekBar.OnSeekBarChangeListener {
    override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
    }

    override fun onStartTrackingTouch(seekBar: SeekBar?) {
    }

    override fun onStopTrackingTouch(seekBar: SeekBar?) {
    }
}
