package com.aquila.lib.tools.interfaceimpl

import android.text.Editable
import android.text.TextWatcher

/***
 * @date 创建时间 2018/5/14 18:09
 * <AUTHOR> <PERSON><PERSON>
 * @description TextWatcher子类实现
 */
open class TextWatcherImpl : TextWatcher {
    override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {
    }

    override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
    }

    override fun afterTextChanged(s: Editable) {
    }
}
