package com.aquila.lib.tools

import android.content.Context
import android.content.pm.ApplicationInfo

/***
 * @date 创建时间 2018/7/20 10:50
 * <AUTHOR> <PERSON><PERSON><PERSON>ong
 * @description
 */
object ToolsLibAPP {

    private var application: Context? = null
    private var iToolBridgeInterface: IToolBridgeInterface? = null

    @JvmStatic
    val isApkInDebug: Boolean
        get() {
            try {
                val info = get().applicationInfo
                return info.flags and ApplicationInfo.FLAG_DEBUGGABLE != 0
            } catch (e: Exception) {
                return false
            }
        }

    @JvmStatic
    fun init(app: Context) {
        application = app
    }

    fun setToolBridgeInterface(iToolBridgeInterface: IToolBridgeInterface) {
        ToolsLibAPP.iToolBridgeInterface = iToolBridgeInterface
    }

    @JvmStatic
    fun get(): Context {
        return application!!
    }

    interface IToolBridgeInterface {
        fun getGlideDirPath(): String?
    }
}
