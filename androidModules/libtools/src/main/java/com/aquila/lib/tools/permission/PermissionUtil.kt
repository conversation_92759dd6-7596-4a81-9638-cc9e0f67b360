package com.aquila.lib.tools.permission

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.provider.Settings
import android.view.Gravity
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.PermissionChecker
import com.aquila.lib.dialog.CommAlertDialog
import java.io.Serializable

/***
 * @date 创建时间 2019-06-24 17:34
 * <AUTHOR> <PERSON><PERSON>
 * @description 权限申请的封装
 */
class PermissionUtil : AppCompatActivity() {
    private val PERMISSION_REQUEST_CODE = 64
    private val REQUEST_SETTING_CODE = 121

    companion object {
        val callbackMap = HashMap<String, PermissionCallBack>()

        val EXTRA_KEY = "Key"
        val EXTRA_PERMISSIONS = "permissions"
        val EXTRA_PROMPT = "prompt"

        @JvmStatic
        fun requestPermission(context: Context, permissions: Array<String>, permissionCallBack: PermissionCallBack) {
            requestPermission(context, permissions, null, permissionCallBack)
        }

        @JvmStatic
        fun requestPermission(context: Context, permissions: Array<String>, promptOption: PermissionOption?, permissionCallBack: PermissionCallBack) {
            if (hasPermission(context, permissions)) {
                permissionCallBack.onSuccess(permissions)
                return
            }

            val key = System.currentTimeMillis().toString()
            val intent = Intent(context, PermissionUtil::class.java).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                putExtra(EXTRA_KEY, key)
                putExtra(EXTRA_PERMISSIONS, permissions)
                if (promptOption != null) {
                    putExtra(EXTRA_PROMPT, promptOption)
                } else {
                    putExtra(EXTRA_PROMPT, PermissionOption())
                }
            }
            callbackMap[key] = permissionCallBack
            context.startActivity(intent)
            if (context is Activity) {
                context.overridePendingTransition(0, 0)
            }
        }

        // 判断权限是否已打开
        fun hasPermission(context: Context, permissions: Array<String>): Boolean {
            for (per in permissions) {
                if (PermissionChecker.checkSelfPermission(context, per) != PermissionChecker.PERMISSION_GRANTED) {
                    return false
                }
            }
            return true
        }
    }

    /****************************End companion object*******************************************************/

    lateinit var key: String
    lateinit var permissionOption: PermissionOption
    var permissions: Array<String>? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        key = intent.getStringExtra(EXTRA_KEY) ?: ""
        permissions = intent.getStringArrayExtra(EXTRA_PERMISSIONS)
        permissionOption = intent.getSerializableExtra(EXTRA_PROMPT) as PermissionOption

        permissions?.let {
            if (permissionOption.prompt.isNullOrEmpty()) {
                // 直接申请权限
                ActivityCompat.requestPermissions(this, it, PERMISSION_REQUEST_CODE)
            } else {
                showPromptDialog(permissionOption.prompt!!, it)
            }
        }
    }

    fun showPromptDialog(prompt: String, permissions: Array<String>) {
        CommAlertDialog.with(this).setTitle("温馨提示")
            .setMessage(prompt).setCancelAble(false)
            .setMiddleText("我知道了").setAllButtonColor(0xff528FFF.toInt())
            .setOnViewClickListener { _, _, _ ->
                ActivityCompat.requestPermissions(this@PermissionUtil, permissions, PERMISSION_REQUEST_CODE)
            }.create().show()
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == PERMISSION_REQUEST_CODE) {
            val listener = callbackMap[key]
            if (hasPermission(this, permissions) && isGranted(*grantResults)) {
                listener?.onSuccess(permissions)
                finish()
            } else {
                if (permissionOption.failureText.isNullOrEmpty()) {
                    listener?.onFailure(permissions)
                    finish()
                } else {
                    showRefusePermissionDialog(permissions)
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQUEST_SETTING_CODE) {
            permissions?.let {
                ActivityCompat.requestPermissions(this, it, PERMISSION_REQUEST_CODE)
            }
        }
    }

    fun showRefusePermissionDialog(permissions: Array<String>) {
        CommAlertDialog.with(this).setMessage(permissionOption.failureText).setStartText(permissionOption.OKText)
            .setEndText(permissionOption.cancelText)
            .setOnViewClickListener { _, _, t ->
                when (t) {
                    CommAlertDialog.TAG_CLICK_START -> {
                        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                        intent.data = Uri.parse("package:$packageName")
                        startActivityForResult(intent, REQUEST_SETTING_CODE)
                    }
                    CommAlertDialog.TAG_CLICK_END -> {
                        callbackMap[key]?.onFailure(permissions)
                        finish()
                    }
                }
            }.setCancelAble(false).setTouchOutsideCancel(false)
            .setGravity(Gravity.BOTTOM)
            .create().show()
    }

    private fun isGranted(vararg grantResult: Int): Boolean {
        if (grantResult.size == 0) {
            return false
        }
        for (result in grantResult) {
            if (result != PackageManager.PERMISSION_GRANTED) {
                return false
            }
        }
        return true
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(0, 0)
    }

    override fun onDestroy() {
        super.onDestroy()
        callbackMap.remove(key)
    }

    open class PermissionCallBackImpl : PermissionCallBack {
        override fun onSuccess(permission: Array<String>) {
        }
    }

    interface PermissionCallBack {
        fun onSuccess(permission: Array<String>)
        fun onFailure(permission: Array<String>) {
        }
    }
}

/****权限提示的说明类*/
class PermissionOption(
    var prompt: String? = null,
    var failureText: String? = "您拒绝了权限申请，当前操作无法执行",
    var OKText: String = "去设置",
    var cancelText: String = "我知道了"
) : Serializable
