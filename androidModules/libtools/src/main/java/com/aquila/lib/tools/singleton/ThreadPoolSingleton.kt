package com.aquila.lib.tools.singleton

import java.util.concurrent.Callable
import java.util.concurrent.Future
import java.util.concurrent.SynchronousQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit

/***
 * @date 创建时间 2018/4/18 15:15
 * <AUTHOR> <PERSON><PERSON>
 * @description 线程池的单例模式
 */
object ThreadPoolSingleton {

    private var executorService = ThreadPoolExecutor(0, 100, 30L, TimeUnit.SECONDS, SynchronousQueue())

    @JvmStatic
    fun executeTask(runnable: () -> Unit) {
        executorService.execute(runnable)
    }

    @JvmStatic
    fun <T> executeCallableTask(callable: Callable<T>): Future<T> {
        return executorService.submit(callable)
    }

    @JvmStatic
    fun executeTask(runnable: Runnable) {
        executorService.execute(runnable)
    }
}
