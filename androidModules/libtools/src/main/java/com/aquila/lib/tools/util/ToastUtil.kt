package com.aquila.lib.tools.util

import android.graphics.drawable.GradientDrawable
import android.os.Build
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import android.widget.TextView
import android.widget.Toast
import androidx.annotation.StringRes
import androidx.core.content.ContextCompat
import com.aquila.lib.tools.ToolsLibAPP

/***
 * @date 创建时间 2018/5/22 15:34
 * <AUTHOR> W.<PERSON>ong
 * @description Toast提示的基类
 */
object ToastUtil {

    val toast = Toast.makeText(ToolsLibAPP.get(), "", Toast.LENGTH_SHORT)
    var showTime = 0L
    
    private var centerToastShowTime = 0L
    private var lastCenterToastMessage = ""
    private const val CENTER_TOAST_SHOW_INTERVAL = 3000L
    
    fun showToastShortOnce(@StringRes textId: Int) {
        toast.setText(textId)
        if (System.currentTimeMillis() - showTime > CENTER_TOAST_SHOW_INTERVAL) {
            toast.show()
            showTime = System.currentTimeMillis()
        }
    }

    @JvmStatic
    fun showToastShort(@StringRes textId: Int) {
        Toast.makeText(ToolsLibAPP.get(), textId, Toast.LENGTH_SHORT).show()
    }

    @JvmStatic
    fun showToastShort(text: CharSequence?) {
        if (TextUtils.isEmpty(text)) {
            return
        }
        Toast.makeText(ToolsLibAPP.get(), text, Toast.LENGTH_SHORT).show()
    }

    @JvmStatic
    fun showToastLong(text: CharSequence?) {
        if (TextUtils.isEmpty(text)) {
            return
        }
        Toast.makeText(ToolsLibAPP.get(), text, Toast.LENGTH_LONG).show()
    }

    @JvmStatic
    fun showToastLong(@StringRes textId: Int) {
        Toast.makeText(ToolsLibAPP.get(), textId, Toast.LENGTH_LONG).show()
    }
    
    /**
     * 在屏幕中央显示Toast
     * @param text 要显示的文本
     * @param duration Toast显示时长
     */
    @JvmStatic
    fun showToastCenter(text: CharSequence?, duration: Int = Toast.LENGTH_SHORT) {
        if (TextUtils.isEmpty(text)) {
            return
        }
        
        val currentTime = System.currentTimeMillis()
        if (text.toString() == lastCenterToastMessage && 
            currentTime - centerToastShowTime < CENTER_TOAST_SHOW_INTERVAL) {
            return
        }
        
        lastCenterToastMessage = text.toString()
        centerToastShowTime = currentTime
        
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.R) {
            // Android 11以下版本直接使用gravity
            val toast = Toast.makeText(ToolsLibAPP.get(), text, duration)
            toast.setGravity(Gravity.CENTER, 0, 0)
            toast.show()
        } else {
            // Android 11及以上版本使用自定义布局来实现居中效果
            showCustomToastCenter(text, duration)
        }
    }
    
    /**
     * 在屏幕中央显示Toast（使用资源ID）
     * @param textId 要显示的文本资源ID
     * @param duration Toast显示时长
     */
    @JvmStatic
    fun showToastCenter(@StringRes textId: Int, duration: Int = Toast.LENGTH_SHORT) {
        val message = ToolsLibAPP.get().getString(textId)
        
        // 如果短时间内重复显示相同消息，则忽略
        val currentTime = System.currentTimeMillis()
        if (message == lastCenterToastMessage && 
            currentTime - centerToastShowTime < CENTER_TOAST_SHOW_INTERVAL) {
            return
        }
        
        lastCenterToastMessage = message
        centerToastShowTime = currentTime
        
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.R) {
            // Android 11以下版本直接使用gravity
            val toast = Toast.makeText(ToolsLibAPP.get(), textId, duration)
            toast.setGravity(Gravity.CENTER, 0, 0)
            toast.show()
        } else {
            // Android 11及以上版本使用自定义布局来实现居中效果
            showCustomToastCenter(message, duration)
        }
    }
    
    /**
     * 使用自定义布局在屏幕中央显示Toast (适用于Android 11及以上)
     * @param text 要显示的文本
     * @param duration Toast显示时长
     */
    private fun showCustomToastCenter(text: CharSequence?, duration: Int = Toast.LENGTH_SHORT) {
        if (TextUtils.isEmpty(text)) {
            return
        }
        
        try {
            // 创建自定义TextView作为Toast的视图
            val context = ToolsLibAPP.get()
            val textView = TextView(context)
            textView.text = text
            textView.textSize = 14f
            textView.setTextColor(0xFFFFFFFF.toInt())  // 白色文字
            
            val padding = (12 * context.resources.displayMetrics.density).toInt()
            textView.setPadding(padding, padding, padding, padding)
            
            val background = GradientDrawable()
            background.cornerRadius = 16 * context.resources.displayMetrics.density  // 16dp圆角
            background.setColor(0xE6333333.toInt())  // 半透明深灰色背景
            textView.background = background
            
            val toast = Toast(context)
            toast.duration = duration
            toast.view = textView
            toast.setGravity(Gravity.CENTER, 0, 0)
            toast.show()
        } catch (e: Exception) {
            try {
                val toastLayoutId = ToolsLibAPP.get().resources.getIdentifier(
                    "transient_notification", 
                    "layout", 
                    "android"
                )
                if (toastLayoutId > 0) {
                    val inflater = LayoutInflater.from(ToolsLibAPP.get())
                    val view = inflater.inflate(toastLayoutId, null)
                    
                    val messageView = view.findViewById<TextView>(
                        ToolsLibAPP.get().resources.getIdentifier(
                            "message", 
                            "id", 
                            "android"
                        )
                    )
                    
                    if (messageView != null) {
                        messageView.text = text
                        
                        val toast = Toast(ToolsLibAPP.get())
                        toast.duration = duration
                        toast.view = view
                        
                        toast.setGravity(Gravity.CENTER, 0, 0)
                        toast.show()
                        return
                    }
                }
            } catch (e: Exception) {
                val toast = Toast.makeText(ToolsLibAPP.get(), text, duration)
                toast.show()
            }
        }
    }
}
