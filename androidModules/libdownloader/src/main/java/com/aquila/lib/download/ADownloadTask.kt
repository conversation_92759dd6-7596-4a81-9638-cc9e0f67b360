package com.aquila.lib.download

import com.aquila.lib.log.KLog
import okhttp3.OkHttpClient
import okhttp3.Request
import java.io.File
import java.io.InputStream
import java.io.RandomAccessFile
import java.lang.Exception

/***
 * @date 创建时间 2020/9/30 17:59
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */
class ADownloadTask(var okHttpClient: OkHttpClient, var entity: ADownloadEntity, var aDownloadingListener: ADownloadingListener) : Runnable {

    /*这两处是为了外部判断对象equals的*/
    override fun toString(): String {
        return entity.tag
    }

    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        return other != null && toString().equals(other.toString())
    }

    var isRunning = false

    private var isDownloadPause = false

    fun pauseDownloadTask() {
        isDownloadPause = true
    }

    override fun run() {
        isRunning = true
        aDownloadingListener.onBegin(entity)
        var file = File(entity.tempPath)
        if (!file.exists()) {
            file.createNewFile()
        }
        var tempFileLength = file.length()
        val request: Request = Request.Builder().addHeader("RANGE", "bytes=$tempFileLength-")
            .url(entity.downloadUrl).build()

        val response = okHttpClient.newCall(request).execute()
        var inputStream: InputStream? = null
        if (response.isSuccessful) {
            try {
                val total: Long = response.body!!.contentLength()
                inputStream = response.body!!.byteStream()
                val randomAccessFile = RandomAccessFile(file, "rwd")
                randomAccessFile.seek(tempFileLength)
                val bytes = ByteArray(1024 * 8)
                var len: Int = 0
                var lastTime = 0L
                var currentTime = 0L
                while (inputStream.read(bytes).also({ len = it }) != -1) {
                    randomAccessFile.write(bytes, 0, len)
                    tempFileLength += len.toLong()
                    currentTime = System.currentTimeMillis()

                    if (currentTime - lastTime > 500L) {
                        aDownloadingListener.onDownloading(entity, tempFileLength, total)
                        lastTime = currentTime
                        KLog.d("total = ${formatFileSize(total)}, tempFileLength = ${formatFileSize(tempFileLength)}")
                    }

                    if (isDownloadPause) {
                        aDownloadingListener.onPause(entity)
                        break
                    }
                }

                val tempFile = File(entity.tempPath)
                if (tempFileLength >= total) {
                    // 是否需要被拦截前端执行处理
                    if (aDownloadingListener.isInterceptToDealFile(entity)) {
                        // 前端处理完后返回处理结果
                        var resultEntity = aDownloadingListener.dealCompleteFile(entity, tempFile)
                        if (resultEntity.resultCode == 200) { // 处理成功回调
                            aDownloadingListener.onComplete(entity)
                        } else { // 处理失败回调
                            aDownloadingListener.onException(entity, resultEntity.resultCode, resultEntity.message ?: "")
                        }
                    } else {
                        val resultFile = File(entity.resultPath)
                        val result = tempFile.renameTo(resultFile)
                        if (result) {
                            tempFile.delete()
                            aDownloadingListener.onComplete(entity)
                        } else {
                            aDownloadingListener.onException(entity, ADownloadConfigs.ERROR_CODE_COMPLETE_ERROR, "文件已下载完成为临时文件名，但rename错误")
                        }
                    }
                } else {
                    tempFile.delete()
                    aDownloadingListener.onException(entity, ADownloadConfigs.ERROR_CODE_NOT_FULL_DOWNLOAD, "未完整下载文件")
                }
            } catch (e: Exception) {
                e.printStackTrace()
                aDownloadingListener.onException(entity, ADownloadConfigs.ERROR_CODE_OTHER_ERROR, "下载出现了异常：" + e.message)
                isRunning = false
            } finally {
                inputStream?.close()
                response.body?.close()
            }
            isRunning = false
        }
    }

    val SIZE_KB = 1024L
    val SIZE_MB = SIZE_KB * 1024L
    val SIZE_GB = SIZE_MB * 1024L
    private fun formatFileSize(byteSize: Long): String {
        return when (byteSize) {
            in 0..SIZE_KB -> byteSize.toString() + "B"

            in SIZE_KB..SIZE_MB -> trimDotEndZero(String.format("%.1f", byteSize.toDouble() / SIZE_KB)) + "K"

            in SIZE_MB..SIZE_GB -> trimDotEndZero(String.format("%.1f", byteSize.toDouble() / SIZE_MB)) + "M"

            else -> trimDotEndZero(String.format("%.1f", byteSize.toDouble() / SIZE_GB)) + "G"
        }
    }

    fun trimDotEndZero(oriStr: String): String {
        var oriStr = oriStr
        if (oriStr.indexOf(".") > 0) {
            // 去掉多余的0
            oriStr = oriStr.replace("0+?$".toRegex(), "")
            // 如最后一位是.则去掉
            oriStr = oriStr.replace("[.]$".toRegex(), "")
        }
        return oriStr
    }
}
