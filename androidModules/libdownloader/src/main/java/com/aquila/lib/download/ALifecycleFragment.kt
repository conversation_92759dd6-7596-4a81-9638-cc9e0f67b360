package com.aquila.lib.download

import android.os.Bundle
import androidx.fragment.app.Fragment

/**
 * 创建一个LifecycleFragment，用于绑定Activity的生命周期
 * 并通过LifecycleListener接口将监听结果返回给调用者
 */
class ALifecycleFragment(var lifecycleListener: ALifecycleListener? = null) : Fragment() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        lifecycleListener?.onLifecycleCreate()
    }

    override fun onStart() {
        super.onStart()
        lifecycleListener?.onLifecycleStart()
    }

    override fun onStop() {
        super.onStop()
        lifecycleListener?.onLifecycleStop()
    }

    override fun onDestroy() {
        super.onDestroy()
        lifecycleListener?.onLifecycleDestroy()
    }

    /**
     * 监听LifecycleFragment生命周期的接口
     */
    interface ALifecycleListener {
        fun onLifecycleCreate()
        fun onLifecycleStart() {}
        fun onLifecycleStop() {}
        fun onLifecycleDestroy()
    }
}
