package com.aquila.lib.download

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import androidx.fragment.app.FragmentActivity
import com.aquila.lib.log.KLog

/***
 * @date 创建时间 2020/9/30 17:59
 * <AUTHOR> <PERSON><PERSON>
 * @description 下载引擎
 */
open class ADownloadEngine(val activity: FragmentActivity) : ALifecycleFragment.ALifecycleListener {
    private var isServiceConnected = false

    lateinit var binder: ADownloadService.APKDownloadBinder
    private var onServiceConnectedListener: AOnDownloadServiceConnectedListener? = null

    protected var aDownloadingListener: ADownloadingListener? = null

    open fun setDownloadingListener(listener: ADownloadingListener?) {
        aDownloadingListener = listener
        if (isServiceConnected) {
            aDownloadingListener?.let {
                binder.addDownloadListener(it)
            }
        }
    }

    val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            isServiceConnected = true
            binder = service as ADownloadService.APKDownloadBinder
            aDownloadingListener?.let {
                binder.addDownloadListener(it)
            }
            onServiceConnectedListener?.onServiceConnected()
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            isServiceConnected = false
            KLog.d("解绑下载服务")
        }
    }

    init {
        activity.supportFragmentManager.beginTransaction()
            .add(ALifecycleFragment(this), ALifecycleFragment::class.java.name)
            .commitAllowingStateLoss()
    }

    override fun onLifecycleCreate() {
        val intent = Intent(activity, ADownloadService::class.java)
        activity.startService(intent)
        activity.bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
    }

    override fun onLifecycleDestroy() {
        aDownloadingListener?.let {
            binder.removeDownloadListener(it)
        }
        activity.unbindService(serviceConnection)
    }

    fun startOrPauseDownload(entity: ADownloadEntity) {
        binder.startOrPauseDownload(entity)
    }

    fun <T> startOrPauseDownload(t: T, transformListener: OnDataTransformListener) {
        binder.startOrPauseDownload(transformListener.onDataTransform(t))
    }

    fun getAllInDownloadingTasks(): ArrayList<ADownloadEntity> {
        return binder.getAllInDownloadingTaskList()
    }

    fun getAlPendingTaskList(): ArrayList<ADownloadEntity> {
        return binder.getAllPendingTaskList()
    }
}
