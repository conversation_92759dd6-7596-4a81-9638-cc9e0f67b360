package com.aquila.lib.download

import java.io.File

interface ADownloadingListener {
    /*在下载队列中等待*/
    fun onWait(entity: ADownloadEntity) {}

    /*移出下载等待队列*/
    fun onCancelWait(entity: ADownloadEntity) {}

    /*开始下载*/
    fun onBegin(entity: ADownloadEntity) {}

    /*下载中*/
    fun onDownloading(entity: ADownloadEntity, downloadSize: Long, totalSize: Long) {}

    /*下载暂停*/
    fun onPause(entity: ADownloadEntity) {}

    /*下载完成*/
    fun onComplete(entity: ADownloadEntity) {}

    /*下载异常*/
    fun onException(entity: ADownloadEntity, errorCode: Int, errorMsg: String) {}

    /*自行处理下载成功的文件，返回是否处理成功*/
    fun dealCompleteFile(entity: ADownloadEntity, file: File): DealDownloadFileEntity {
        return DealDownloadFileEntity()
    }

    /*是否需要拦截自行处理下载完成的文件*/
    fun isInterceptToDealFile(entity: ADownloadEntity): <PERSON><PERSON><PERSON> {
        return false
    }
}
