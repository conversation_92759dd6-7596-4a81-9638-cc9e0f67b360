package com.aquila.lib.download

import android.app.Service
import android.content.Intent
import android.os.Binder
import android.os.Handler
import android.os.IBinder
import okhttp3.OkHttpClient
import java.util.LinkedList
import java.util.concurrent.SynchronousQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit

/***
 * @date 创建时间 2020/9/30 17:58
 * <AUTHOR> W.Yu<PERSON>ong
 * @description
 */
class ADownloadService : Service() {
    private var executorService = ThreadPoolExecutor(0, 100, 30L, TimeUnit.SECONDS, SynchronousQueue())
    fun executeTask(runnable: Runnable?) {
        executorService.execute(runnable)
    }

    var binder = APKDownloadBinder()
    override fun onBind(intent: Intent?): IBinder? {
        return binder
    }

    private lateinit var okHttpClient: OkHttpClient
    private val TIME_OUT = 60L
    override fun onCreate() {
        super.onCreate()
        okHttpClient = OkHttpClient.Builder()
            .connectTimeout(TIME_OUT, TimeUnit.SECONDS)
            .readTimeout(TIME_OUT, TimeUnit.SECONDS)
            .writeTimeout(TIME_OUT, TimeUnit.SECONDS).build()
    }

    /***
     *@date 创建时间 2020/10/1 09:43
     *<AUTHOR> W.YuLong
     *@description
     */
    inner class APKDownloadBinder : Binder(), ADownloadingListener {
        val taskMap = HashMap<String, ADownloadTask>()
        val pendingTaskList = LinkedList<ADownloadTask>()
        val downloadListenerList = ArrayList<ADownloadingListener>()

        val handler = Handler()

        fun addDownloadListener(downloadingListener: ADownloadingListener) {
            downloadListenerList.add(downloadingListener)
        }

        fun removeDownloadListener(downloadingListener: ADownloadingListener) {
            downloadListenerList.remove(downloadingListener)
        }

        /*开始或暂停下载*/
        fun startOrPauseDownload(entity: ADownloadEntity) {
            var task = taskMap.get(entity.tag)
            if (task == null) {
                task = ADownloadTask(okHttpClient, entity, this)
                if (taskMap.size >= ADownloadConfigs.MAX_TASK_COUNT) {
                    addPendingTask(task)
                } else {
                    taskMap[entity.tag] = task
                    executeTask(task)
                }
            } else {
                task.pauseDownloadTask()
            }
        }

        /*添加任务到待下载队列汇总*/
        fun addPendingTask(task: ADownloadTask) {
            var isInPending = false
            for (t in pendingTaskList) {
                if (t.entity.tag == task.entity.tag) {
                    isInPending = true
                    break
                }
            }
            if (isInPending) {
                pendingTaskList.remove(task)
            } else {
                pendingTaskList.add(task)
            }
        }

        fun getAllInDownloadingTaskList(): ArrayList<ADownloadEntity> {
            var list = ArrayList<ADownloadEntity>()
            if (taskMap.isNotEmpty()) {
                for (task in taskMap.values) {
                    list.add(task.entity)
                }
            }
            return list
        }

        fun getAllPendingTaskList(): ArrayList<ADownloadEntity> {
            var list = ArrayList<ADownloadEntity>()
            if (pendingTaskList.isNotEmpty()) {
                for (task in pendingTaskList) {
                    list.add(task.entity)
                }
            }
            return list
        }

        private fun executeNextPendingTask(entity: ADownloadEntity) {
            taskMap.remove(entity.tag)
            if (!pendingTaskList.isNullOrEmpty()) {
                val task = pendingTaskList.poll()
                taskMap[task.entity.tag] = task
                executeTask(task)
            }
            if (taskMap.isEmpty()) {
                stopSelf()
            }
        }

        // /*********************************************************************************************/
        override fun onWait(entity: ADownloadEntity) {
            handler.post {
                for (l in downloadListenerList) {
                    l.onWait(entity)
                }
                ADownloadConfigs.globeListener?.updateDownloadStatus(entity, ADownloadConfigs.DOWNLOAD_STATUS_WAIT)
            }
        }

        override fun onCancelWait(entity: ADownloadEntity) {
            handler.post {
                for (l in downloadListenerList) {
                    l.onCancelWait(entity)
                }
                ADownloadConfigs.globeListener?.updateDownloadStatus(entity, ADownloadConfigs.DOWNLOAD_STATUS_CANCEL_WAIT)
            }
        }

        override fun onBegin(entity: ADownloadEntity) {
            handler.post {
                ADownloadConfigs.globeListener?.updateDownloadStatus(entity, ADownloadConfigs.DOWNLOAD_STATUS_BEGIN)
                for (l in downloadListenerList) {
                    l.onBegin(entity)
                }
            }
        }

        override fun onDownloading(entity: ADownloadEntity, downloadSize: Long, totalSize: Long) {
            handler.post {
                for (l in downloadListenerList) {
                    l.onDownloading(entity, downloadSize, totalSize)
                }
            }
        }

        override fun onPause(entity: ADownloadEntity) {
            handler.post {
                for (l in downloadListenerList) {
                    l.onPause(entity)
                }
                ADownloadConfigs.globeListener?.updateDownloadStatus(entity, ADownloadConfigs.DOWNLOAD_STATUS_PAUSE)
                executeNextPendingTask(entity)
            }
        }

        override fun onComplete(entity: ADownloadEntity) {
            handler.post {
                for (l in downloadListenerList) {
                    l.onComplete(entity)
                }
                ADownloadConfigs.globeListener?.updateDownloadStatus(entity, ADownloadConfigs.DOWNLOAD_STATUS_COMPLETE)
                executeNextPendingTask(entity)
            }
        }

        override fun onException(entity: ADownloadEntity, errorCode: Int, errorMsg: String) {
            handler.post {
                for (l in downloadListenerList) {
                    l.onException(entity, errorCode, errorMsg)
                }
                ADownloadConfigs.globeListener?.updateDownloadStatus(entity, ADownloadConfigs.DOWNLOAD_STATUS_ERROR)
                executeNextPendingTask(entity)
            }
        }
    }
}
