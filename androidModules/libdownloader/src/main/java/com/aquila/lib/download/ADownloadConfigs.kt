package com.aquila.lib.download

/***
 * @date 创建时间 2020/10/21 14:23
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @description
 */

internal object ADownloadConfigs {
    const val ERROR_CODE_COMPLETE_ERROR = 1001
    const val ERROR_CODE_OTHER_ERROR = 1002
    const val ERROR_CODE_NOT_FULL_DOWNLOAD = 1003

    const val MAX_TASK_COUNT = 3

    var globeListener: OnGlobeUpdateDownloadStatusListener? = null

    // 在下载队列中等待
    const val DOWNLOAD_STATUS_WAIT = "wait"
    /*取消在下载队列中等待*/
    const val DOWNLOAD_STATUS_CANCEL_WAIT = "cancelWait"

    // 准备阶段
    const val DOWNLOAD_STATUS_BEGIN = "begin"

    // 下载中
    const val DOWNLOAD_STATUS_DOWNLOADING = "downloading"

    // 下载完成
    const val DOWNLOAD_STATUS_COMPLETE = "completed"

    // 下载暂停
    const val DOWNLOAD_STATUS_PAUSE = "pause"

    // 下载错误
    const val DOWNLOAD_STATUS_ERROR = "error"
}
