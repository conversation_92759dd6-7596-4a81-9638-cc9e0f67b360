package com.wedevote.wdbook

import android.content.Context
import com.russhwolf.settings.SharedPreferencesSettings
import com.wedevote.wdbook.constants.AppEnvironment
import com.wedevote.wdbook.db.DatabaseDriverFactory
import com.wedevote.wdbook.log.WDAntilog
import com.wedevote.wdbook.network.HttpClientFactory
import com.wedevote.wdbook.utils.Platform
import com.wedevote.wdbook.utils.createHttpEngine
import io.github.aakira.napier.Napier
import java.net.Proxy

/***
 * Created by <PERSON> on 2021/4/22 11:24
 *
 * @description
 */
fun SDK.Companion.create(context: Context, env: AppEnvironment = AppEnvironment.TEST, isDebug: Boolean = true, socksProxy: Proxy?, businessPatch: BusinessPatch? = null): SDK {
    if (isDebug) {
        Napier.base(WDAntilog(logLevel = env.getNapierLogLevel()))
    }

    val sdkConfig = SDKConfig(
        platform = Platform(context, socksProxy != null),
        env = env,
        settings = SharedPreferencesSettings(context.getSharedPreferences("wd_kmm_prefs", Context.MODE_PRIVATE)),
        databaseDriverFactory = DatabaseDriverFactory(context = context),
        businessPatch = businessPatch,
    )
    return SDK(
        sdkConfig,
        isDebug,
        HttpClientFactory(sdkConfig) { cacheSize, timeout -> createHttpEngine(context, cacheSize, timeout, socksProxy) },
        HttpClientFactory(sdkConfig) { cacheSize, timeout -> createHttpEngine(context, cacheSize, timeout, null) },
    )
}
