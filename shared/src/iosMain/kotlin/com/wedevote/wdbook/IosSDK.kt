package com.wedevote.wdbook

import com.russhwolf.settings.NSUserDefaultsSettings
import com.wedevote.wdbook.adapter.IosApiServerBl
import com.wedevote.wdbook.adapter.IosAppBl
import com.wedevote.wdbook.adapter.IosDownloadBl
import com.wedevote.wdbook.adapter.IosPaymentBl
import com.wedevote.wdbook.adapter.IosSessionBl
import com.wedevote.wdbook.adapter.IosStoreBl
import com.wedevote.wdbook.adapter.IosSyncBl
import com.wedevote.wdbook.adapter.IosUserBl
import com.wedevote.wdbook.constants.AppEnvironment
import com.wedevote.wdbook.constants.AppMode
import com.wedevote.wdbook.db.DatabaseDriverFactory
import com.wedevote.wdbook.log.WDAntilog
import com.wedevote.wdbook.network.HttpClientFactory
import com.wedevote.wdbook.utils.Platform
import com.wedevote.wdbook.utils.createHttpEngine
import io.github.aakira.napier.Napier
import platform.Foundation.NSUserDefaults

/***
 * Created by <PERSON> on 2021/4/21 14:32
 *
 * @description
 */
class IosSDK(var env: AppEnvironment = AppEnvironment.TEST, var isDebug: Boolean = true, appMode: AppMode, businessPatch: BusinessPatch? = null) {

    constructor() : this(AppEnvironment.TEST, true, AppMode.Debug)

    init {
        if (isDebug) Napier.base(WDAntilog(logLevel = env.getNapierLogLevel()))
//        cocoapods.wdNetwork.WDCft.sharedInstance().start(CFT_LOG_TRACE)
    }

    private val sdkConfig = SDKConfig(
        platform = Platform(),
        env = env,
        settings = NSUserDefaultsSettings(NSUserDefaults.standardUserDefaults()),
        databaseDriverFactory = DatabaseDriverFactory(),
        appMode = appMode,
        businessPatch = businessPatch,
    )
    val sdk = SDK(
        sdkConfig,
        isDebug,
        HttpClientFactory(sdkConfig) { _, _ -> createHttpEngine() },
        HttpClientFactory(sdkConfig) { _, _ -> createHttpEngine(enableCftProxy = false) }
    )

    val userBl = IosUserBl(sdk.userBl)
    val appBl = IosAppBl(sdk.appBl)
    val syncBl = IosSyncBl(sdk.syncBl)
    val sessionBl = IosSessionBl(sdk.sessionBl)
    val downloadBl = IosDownloadBl(sdk.downloadBl)
    val storeBl = IosStoreBl(sdk.storeBl)
    val paymentBl = IosPaymentBl(sdk.paymentBl)
    val apiServerBl = IosApiServerBl(sdk.apiServerBl)
}
