package com.wedevote.wdbook.network

import com.wedevote.wdbook.SDKConfig
import com.wedevote.wdbook.bl.ApiServerBl
import com.wedevote.wdbook.entity.base.ApiResponse
import com.wedevote.wdbook.entity.base.ErrorDetail
import com.wedevote.wdbook.exception.ApiException
import com.wedevote.wdbook.exception.ErrorInfo
import com.wedevote.wdbook.exception.SDKException
import io.github.aakira.napier.Napier
import io.ktor.client.call.body
import io.ktor.client.plugins.ClientRequestException
import io.ktor.client.plugins.ServerResponseException
import io.ktor.client.statement.HttpResponse
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/***
 * Created by <PERSON> on 2023/2/15 16:05
 *
 * @description
 */
@Throws(Throwable::class)
internal suspend inline fun <reified T> SDKConfig.handleResponse(block: (() -> HttpResponse)): T? {
    return try {
        val result: ApiResponse<T> = block().body()
        when (result.errno) {
            200 -> {
                prefs.increaseCFTSuccessAPICount()
                result.data
            }
            ApiErrorCode.DeviceExceedLimit_1312 -> {
                ApiErrorCode.DeviceExceedLimit_1312 as T?
            }
            else -> {
                Napier.e("Invalid response errno ${result.errno},${result.errDetail}", tag = "ResponseHandler")
                if (result.errno == ApiErrorCode.InvalidToken) {
                    Napier.e("Invalid token, mark token as invalid", tag = "ResponseHandler")
                    this.prefs.tokenInvalid = true
                }

                // Handle API version errors
                CoroutineScope(Dispatchers.Default).launch {
                    when (result.errno) {
                        ErrorInfo.ErrorCodeApiVersionTooLow.code -> {
                            <EMAIL>?.onNeedUpdate(isForce = true, upgradeUrl = null)
                        }
                        ErrorInfo.ErrorCodeApiExpired.code -> {
                            <EMAIL>?.onNeedUpdate(isForce = false, upgradeUrl = null)
                        }
                    }
                }

                throw ApiException(result.errno, result.msg, result.errDetail).apply {
                    this.errDetail = ErrorDetail(msg = result.msg, errno = result.errno, url = block().call.request.url.encodedPath)
                }
            }
        }
    } catch (e: Exception) {
        e.printStackTrace()
        when (e) {
            is ApiException -> {
                Napier.e("ApiException: ${e.message}", tag = "ResponseHandler")
                throw e
            }

            is SDKException -> {
                Napier.e("SDKException: ${e.message}", tag = "ResponseHandler")
                throw e
            }

            is ClientRequestException -> {
                Napier.e("ClientRequestException: ${e.message}", tag = "ResponseHandler")
                throw e
            }

            is ServerResponseException -> {
                Napier.e("ServerResponseException: ${e.message}", tag = "ResponseHandler")
                throw e
            }

            else -> {
                Napier.e("Other Exception: ${e.message}", tag = "ResponseHandler")
                throw e
            }
        }
    }
}
