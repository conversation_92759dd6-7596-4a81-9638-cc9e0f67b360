package com.wedevote.wdbook

import com.russhwolf.settings.Settings
import com.wedevote.wdbook.constants.AppEnvironment
import com.wedevote.wdbook.constants.AppMode
import com.wedevote.wdbook.constants.AppPlatform
import com.wedevote.wdbook.constants.Constants
import com.wedevote.wdbook.constants.LanguageMode
import com.wedevote.wdbook.db.DatabaseDriverFactory
import com.wedevote.wdbook.exception.SDKException
import com.wedevote.wdbook.storage.Prefs
import com.wedevote.wdbook.utils.Platform
import io.github.aakira.napier.Napier
import io.ktor.http.encodeURLParameter

/***
 * Created by <PERSON> on 2021/4/21 10:35
 *
 * @description
 */
internal class SDKConfig(
    val platform: Platform,
    val env: AppEnvironment = AppEnvironment.TEST,
    val settings: Settings,
    val databaseDriverFactory: DatabaseDriverFactory,
    val appMode: AppMode? = null,
    val businessPatch: BusinessPatch? = null,
) {
    val prefs = Prefs(settings, platform)

    /*微件的版本号*/
    val widgetVersionCode = 1

    val deviceInformation = getDeviceInformationValue()

    var market: LanguageMode
        get() = prefs.market
        set(value) {
            prefs.market = value
        }

    val iosAuthLoginSchema: String
        get() {
            return when (env) {
                AppEnvironment.PROD, AppEnvironment.BETA, AppEnvironment.INNER -> Constants.IOS_AUTH_LOGIN_SCHEMA
                else -> Constants.IOS_AUTH_LOGIN_SCHEMA_TEST
            }
        }

    val isIOSPlatform: Boolean = platform.appPlatform == AppPlatform.IOS

    val androidWDBiblePackage: String
        get() {
            return when (env) {
                AppEnvironment.PROD, AppEnvironment.BETA, AppEnvironment.INNER -> Constants.ANDROID_WD_BIBLE_PACKAGE
                else -> Constants.ANDROID_WD_BIBLE_PACKAGE_TEST
            }
        }

    val androidWDBibleHDPackage: String
        get() {
            return when (env) {
                AppEnvironment.PROD, AppEnvironment.BETA, AppEnvironment.INNER -> Constants.ANDROID_WD_BIBLE_HD_PACKAGE
                else -> Constants.ANDROID_WD_BIBLE_HD_PACKAGE_TEST
            }
        }

    val wdBookPackage: String
        get() {
            return when (env) {
                AppEnvironment.PROD, AppEnvironment.BETA, AppEnvironment.INNER -> Constants.WD_BOOK_PACKAGE
                else -> Constants.WD_BOOK_PACKAGE_TEST
            }
        }

    val wdBookSchema: String
        get() {
            return when (env) {
                AppEnvironment.PROD -> Constants.WD_BOOK_SCHEMA
                AppEnvironment.BETA -> Constants.WD_BOOK_SCHEMA_BETA
                AppEnvironment.INNER -> Constants.WD_BOOK_SCHEMA_INNER
                else -> Constants.WD_BOOK_SCHEMA_TEST
            }
        }

    val wdBookSchemaHost = Constants.WD_BOOK_SCHEMA_HOST

    val iosAuthLoginCheckSchema: String
        get() {
            return when (env) {
                AppEnvironment.PROD -> Constants.IOS_AUTH_LOGIN_CHECK_SCHEMA
                AppEnvironment.BETA -> Constants.IOS_AUTH_LOGIN_CHECK_SCHEMA_BETA
                AppEnvironment.INNER -> Constants.IOS_AUTH_LOGIN_CHECK_SCHEMA
                else -> Constants.IOS_AUTH_LOGIN_CHECK_SCHEMA_TEST
            }
        }

    val userAgent: String
        get() {
            val appInfo = "$wdBookSchema/${platform.getVersionCode()}"
            var storeInfo = platform.getAppStore(appMode)
            if (storeInfo != "") {
                storeInfo = " ($storeInfo)"
            }
            val deviceInfo =
                " ${platform.osName}/${platform.osVersion} (${platform.deviceName}/${platform.deviceModel}; ${platform.countryCode})"
            val userAgent = "$appInfo$storeInfo$deviceInfo"
            Napier.d("The User-Agent is $userAgent", tag = "SDKConfig")
            return userAgent
        }

    private fun getDeviceInformationValue(): String {
        val deviceName = platform.deviceName.encodeURLParameter()
        return "${platform.osName}/${platform.osVersion}/${platform.getVersionCode()}/$deviceName/${platform.deviceModel}/${prefs.deviceId}"
    }

    fun isLogin(): Boolean {
        val userId = prefs.userId
        return userId.isNotEmpty() && userId != Constants.ANONYMOUS_USER_ID
    }

    @Throws(Throwable::class)
    fun requireLogin() {
        if (!isLogin()) {
            Napier.e("User not login", tag = "SDKConfig")
            throw SDKException.TokenExpireException
        }
    }

    @Throws(Throwable::class)
    fun checkLogin() {
        if (!isLogin()) {
            Napier.e("User un login", tag = "SDKConfig")
            throw SDKException.UnLoginException
        }
    }

    fun wdBibleEndpoint(): String = env.wdBibleEndpoint

    fun wdBookEndpoint(): String {
        return prefs.apiUrl.ifEmpty { env.wdBookEndpoint }
    }

    fun ssoEndpoint(): String = env.ssoEndpoint

    fun imageServerEndpoint(): String = env.imageServerEndpoint

    fun wdFeedbackExceptionEndpoint() = env.feedbackEndpoint

    fun getFullImageUrl(url: String): String {
        if (url.isEmpty()) return url

        var imgUrl = url
        if (imgUrl.contains("://")) return imgUrl
        if (imgUrl.startsWith("/")) {
            imgUrl = imgUrl.trimStart('/')
        }
        return "${imageServerEndpoint()}/$imgUrl"
    }

    val cftConnectedString: String
        get() = if (platform.cftConnected) Constants.HEADER_VALUE_CFT_CONNECTED else Constants.HEADER_VALUE_CFT_NOT_CONNECTED
}
